{"__meta": {"id": "01JXBX48HH5A4N8EGCQCBRNJ76", "datetime": "2025-06-10 11:11:18", "utime": **********.578652, "method": "GET", "uri": "/payroll", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.315874, "end": **********.578665, "duration": 5.26279091835022, "duration_str": "5.26s", "measures": [{"label": "Booting", "start": **********.315874, "relative_start": 0, "end": **********.554512, "relative_end": **********.554512, "duration": 0.*****************, "duration_str": "239ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.554524, "relative_start": 0.*****************, "end": **********.578667, "relative_end": 1.9073486328125e-06, "duration": 5.****************, "duration_str": "5.02s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.573951, "relative_start": 0.*****************, "end": **********.58501, "relative_end": **********.58501, "duration": 0.011059045791625977, "duration_str": "11.06ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.405459, "relative_start": 1.****************, "end": **********.575895, "relative_end": **********.575895, "duration": 4.***************, "duration_str": "4.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: payroll.index", "start": **********.410143, "relative_start": 1.***************, "end": **********.410143, "relative_end": **********.410143, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.master", "start": **********.799371, "relative_start": 1.***************, "end": **********.799371, "relative_end": **********.799371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.include", "start": 1749525075.450108, "relative_start": 2.1342339515686035, "end": 1749525075.450108, "relative_end": 1749525075.450108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.header", "start": 1749525075.497865, "relative_start": 2.1819908618927, "end": 1749525075.497865, "relative_end": 1749525075.497865, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sidebar", "start": 1749525075.502343, "relative_start": 2.1864688396453857, "end": 1749525075.502343, "relative_end": 1749525075.502343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: description_modal", "start": **********.516721, "relative_start": 5.200846910476685, "end": **********.516721, "relative_end": **********.516721, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.footer", "start": **********.519508, "relative_start": 5.203633785247803, "end": **********.519508, "relative_end": **********.519508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.footer_js", "start": **********.523255, "relative_start": 5.207381010055542, "end": **********.523255, "relative_end": **********.523255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 93097288, "peak_usage_str": "89MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.10", "Environment": "local", "Debug Mode": "Enabled", "URL": "schola.one", "Timezone": "Asia/Kuala_Lumpur", "Locale": "en"}}, "views": {"count": 8, "nb_templates": 8, "templates": [{"name": "payroll.index", "param_count": null, "params": [], "start": **********.410115, "type": "blade", "hash": "bladeD:\\laragon\\www\\schola\\resources\\views/payroll/index.blade.phppayroll.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fresources%2Fviews%2Fpayroll%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "layouts.master", "param_count": null, "params": [], "start": **********.79935, "type": "blade", "hash": "bladeD:\\laragon\\www\\schola\\resources\\views/layouts/master.blade.phplayouts.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fresources%2Fviews%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}}, {"name": "layouts.include", "param_count": null, "params": [], "start": 1749525075.450086, "type": "blade", "hash": "bladeD:\\laragon\\www\\schola\\resources\\views/layouts/include.blade.phplayouts.include", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fresources%2Fviews%2Flayouts%2Finclude.blade.php&line=1", "ajax": false, "filename": "include.blade.php", "line": "?"}}, {"name": "layouts.header", "param_count": null, "params": [], "start": 1749525075.497846, "type": "blade", "hash": "bladeD:\\laragon\\www\\schola\\resources\\views/layouts/header.blade.phplayouts.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "layouts.sidebar", "param_count": null, "params": [], "start": 1749525075.502325, "type": "blade", "hash": "bladeD:\\laragon\\www\\schola\\resources\\views/layouts/sidebar.blade.phplayouts.sidebar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fresources%2Fviews%2Flayouts%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}}, {"name": "description_modal", "param_count": null, "params": [], "start": **********.516698, "type": "blade", "hash": "bladeD:\\laragon\\www\\schola\\resources\\views/description_modal.blade.phpdescription_modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fresources%2Fviews%2Fdescription_modal.blade.php&line=1", "ajax": false, "filename": "description_modal.blade.php", "line": "?"}}, {"name": "layouts.footer", "param_count": null, "params": [], "start": **********.519476, "type": "blade", "hash": "bladeD:\\laragon\\www\\schola\\resources\\views/layouts/footer.blade.phplayouts.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fresources%2Fviews%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}, {"name": "layouts.footer_js", "param_count": null, "params": [], "start": **********.523232, "type": "blade", "hash": "bladeD:\\laragon\\www\\schola\\resources\\views/layouts/footer_js.blade.phplayouts.footer_js", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fresources%2Fviews%2Flayouts%2Ffooter_js.blade.php&line=1", "ajax": false, "filename": "footer_js.blade.php", "line": "?"}}]}, "queries": {"count": 12, "nb_statements": 12, "nb_visible_statements": 12, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.05614999999999999, "accumulated_duration_str": "56.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 2 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.604645, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "schola", "explain": null, "start_percent": 0, "width_percent": 1.175}, {"sql": "select * from `schools` where `schools`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 13}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "Role", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckRole.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.610595, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "checkSchoolStatus:13", "source": {"index": 22, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FMiddleware%2FCheckSchoolStatus.php&line=13", "ajax": false, "filename": "CheckSchoolStatus.php", "line": "13"}, "connection": "schola", "explain": null, "start_percent": 1.175, "width_percent": 0.819}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (2) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 27}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "Role", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckRole.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.61586, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "schola", "explain": null, "start_percent": 1.995, "width_percent": 1.264}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 304}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 174}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 175}], "start": **********.6215699, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:266", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=266", "ajax": false, "filename": "PermissionRegistrar.php", "line": "266"}, "connection": "schola", "explain": null, "start_percent": 3.259, "width_percent": 2.297}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 613, 1259, 1260, 1261, 1262, 1263, 1265, 1266, 1267, 1268, 1269, 1270, 1271, 1272, 1273)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 304}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 174}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 175}], "start": **********.629931, "duration": 0.04616, "duration_str": "46.16ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:266", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=266", "ajax": false, "filename": "PermissionRegistrar.php", "line": "266"}, "connection": "schola", "explain": null, "start_percent": 5.557, "width_percent": 82.208}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 2 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [2, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.357804, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "schola", "explain": null, "start_percent": 87.765, "width_percent": 1.728}, {"sql": "select * from `session_years` where `school_id` = 1 and `session_years`.`deleted_at` is null order by `start_date` asc limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3655221, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:53", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=53", "ajax": false, "filename": "PayrollController.php", "line": "53"}, "connection": "schola", "explain": null, "start_percent": 89.492, "width_percent": 2.351}, {"sql": "select * from `session_years` where `school_id` = 1 and `default` = 1 and `session_years`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/SessionYear/SessionYearRepository.php", "file": "D:\\laragon\\www\\schola\\app\\Repositories\\SessionYear\\SessionYearRepository.php", "line": 18}, {"index": 17, "namespace": null, "name": "app/Services/CachingService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\CachingService.php", "line": 181}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 21, "namespace": null, "name": "app/Services/CachingService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\CachingService.php", "line": 119}], "start": **********.384531, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "SessionYearRepository.php:18", "source": {"index": 16, "namespace": null, "name": "app/Repositories/SessionYear/SessionYearRepository.php", "file": "D:\\laragon\\www\\schola\\app\\Repositories\\SessionYear\\SessionYearRepository.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FRepositories%2FSessionYear%2FSessionYearRepository.php&line=18", "ajax": false, "filename": "SessionYearRepository.php", "line": "18"}, "connection": "schola", "explain": null, "start_percent": 91.843, "width_percent": 1.888}, {"sql": "select * from `school_settings` where `school_id` = 1 and `school_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/CachingService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\CachingService.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 19, "namespace": null, "name": "app/Services/CachingService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\CachingService.php", "line": 119}, {"index": 20, "namespace": null, "name": "app/Services/CachingService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\CachingService.php", "line": 130}], "start": **********.8939202, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "CachingService.php:131", "source": {"index": 15, "namespace": null, "name": "app/Services/CachingService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\CachingService.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FServices%2FCachingService.php&line=131", "ajax": false, "filename": "CachingService.php", "line": "131"}, "connection": "schola", "explain": null, "start_percent": 93.731, "width_percent": 2.048}, {"sql": "select * from `semesters` where `school_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/Semester/SemesterRepository.php", "file": "D:\\laragon\\www\\schola\\app\\Repositories\\Semester\\SemesterRepository.php", "line": 16}, {"index": 16, "namespace": null, "name": "app/Services/CachingService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\CachingService.php", "line": 187}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "app/Services/CachingService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\CachingService.php", "line": 119}], "start": 1749525075.49319, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "SemesterRepository.php:16", "source": {"index": 15, "namespace": null, "name": "app/Repositories/Semester/SemesterRepository.php", "file": "D:\\laragon\\www\\schola\\app\\Repositories\\Semester\\SemesterRepository.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FRepositories%2FSemester%2FSemesterRepository.php&line=16", "ajax": false, "filename": "SemesterRepository.php", "line": "16"}, "connection": "schola", "explain": null, "start_percent": 95.779, "width_percent": 1.175}, {"sql": "select * from `school_branch` where `school_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": "view", "name": "layouts.sidebar", "file": "D:\\laragon\\www\\schola\\resources\\views/layouts/sidebar.blade.php", "line": 110}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.356644, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "layouts.sidebar:110", "source": {"index": 14, "namespace": "view", "name": "layouts.sidebar", "file": "D:\\laragon\\www\\schola\\resources\\views/layouts/sidebar.blade.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fresources%2Fviews%2Flayouts%2Fsidebar.blade.php&line=110", "ajax": false, "filename": "sidebar.blade.php", "line": "110"}, "connection": "schola", "explain": null, "start_percent": 96.955, "width_percent": 1.638}, {"sql": "select * from `staffs` where `staffs`.`user_id` = 2 and `staffs`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "layouts.sidebar", "file": "D:\\laragon\\www\\schola\\resources\\views/layouts/sidebar.blade.php", "line": 910}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.4591238, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "layouts.sidebar:910", "source": {"index": 21, "namespace": "view", "name": "layouts.sidebar", "file": "D:\\laragon\\www\\schola\\resources\\views/layouts/sidebar.blade.php", "line": 910}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fresources%2Fviews%2Flayouts%2Fsidebar.blade.php&line=910", "ajax": false, "filename": "sidebar.blade.php", "line": "910"}, "connection": "schola", "explain": null, "start_percent": 98.593, "width_percent": 1.407}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 13675, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 227, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\SchoolSetting": {"value": 118, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FSchoolSetting.php&line=1", "ajax": false, "filename": "SchoolSetting.php", "line": "?"}}, "App\\Models\\SessionYear": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FSessionYear.php&line=1", "ajax": false, "filename": "SessionYear.php", "line": "?"}}, "App\\Models\\Semester": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FSemester.php&line=1", "ajax": false, "filename": "Semester.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\School": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FSchool.php&line=1", "ajax": false, "filename": "School.php", "line": "?"}}}, "count": 14026, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 141, "messages": [{"message": "[\n  ability => payroll-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1090141564 data-indent-pad=\"  \"><span class=sf-dump-note>payroll-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">payroll-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1090141564\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.364191, "xdebug_link": null}, {"message": "[\n  ability => class-teacher,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1686070189 data-indent-pad=\"  \"><span class=sf-dump-note>class-teacher </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">class-teacher</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1686070189\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749525075.501059, "xdebug_link": null}, {"message": "[\n  ability => medium-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1645882718 data-indent-pad=\"  \"><span class=sf-dump-note>medium-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">medium-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1645882718\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.350683, "xdebug_link": null}, {"message": "[\n  ability => subject-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-711593831 data-indent-pad=\"  \"><span class=sf-dump-note>subject-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">subject-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-711593831\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.351112, "xdebug_link": null}, {"message": "[\n  ability => class-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1021400627 data-indent-pad=\"  \"><span class=sf-dump-note>class-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">class-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1021400627\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.351546, "xdebug_link": null}, {"message": "[\n  ability => class-section-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1788130326 data-indent-pad=\"  \"><span class=sf-dump-note>class-section-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">class-section-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1788130326\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.352077, "xdebug_link": null}, {"message": "[\n  ability => subject-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1547735535 data-indent-pad=\"  \"><span class=sf-dump-note>subject-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">subject-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1547735535\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.352689, "xdebug_link": null}, {"message": "[\n  ability => semester-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-587814690 data-indent-pad=\"  \"><span class=sf-dump-note>semester-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">semester-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-587814690\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.353911, "xdebug_link": null}, {"message": "[\n  ability => stream-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-643893575 data-indent-pad=\"  \"><span class=sf-dump-note>stream-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">stream-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-643893575\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.354755, "xdebug_link": null}, {"message": "[\n  ability => shift-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-901191854 data-indent-pad=\"  \"><span class=sf-dump-note>shift-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">shift-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-901191854\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.355392, "xdebug_link": null}, {"message": "[\n  ability => promote-student-create,\n  target => transfer-student-create,\n  result => true,\n  user => 2,\n  arguments => [0 => transfer-student-create]\n]", "message_html": "<pre class=sf-dump id=sf-dump-645315223 data-indent-pad=\"  \"><span class=sf-dump-note>promote-student-create transfer-student-create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">promote-student-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">transfer-student-create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; transfer-student-create]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-645315223\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.355853, "xdebug_link": null}, {"message": "[\n  ability => student-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1084751507 data-indent-pad=\"  \"><span class=sf-dump-note>student-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">student-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1084751507\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.356241, "xdebug_link": null}, {"message": "[\n  ability => student-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-701590034 data-indent-pad=\"  \"><span class=sf-dump-note>student-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">student-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-701590034\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.360589, "xdebug_link": null}, {"message": "[\n  ability => form-fields-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-508006824 data-indent-pad=\"  \"><span class=sf-dump-note>form-fields-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">form-fields-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-508006824\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.361184, "xdebug_link": null}, {"message": "[\n  ability => student-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1396138042 data-indent-pad=\"  \"><span class=sf-dump-note>student-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">student-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1396138042\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.361647, "xdebug_link": null}, {"message": "[\n  ability => student-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-536624474 data-indent-pad=\"  \"><span class=sf-dump-note>student-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">student-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-536624474\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.363018, "xdebug_link": null}, {"message": "[\n  ability => student-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-940387528 data-indent-pad=\"  \"><span class=sf-dump-note>student-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">student-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-940387528\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.36443, "xdebug_link": null}, {"message": "[\n  ability => class-report,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1182454091 data-indent-pad=\"  \"><span class=sf-dump-note>class-report </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">class-report</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1182454091\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.396787, "xdebug_link": null}, {"message": "[\n  ability => class-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-520033579 data-indent-pad=\"  \"><span class=sf-dump-note>class-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">class-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-520033579\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.397588, "xdebug_link": null}, {"message": "[\n  ability => student-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-649452110 data-indent-pad=\"  \"><span class=sf-dump-note>student-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">student-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-649452110\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.400687, "xdebug_link": null}, {"message": "[\n  ability => student-reset-password,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-33358452 data-indent-pad=\"  \"><span class=sf-dump-note>student-reset-password </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">student-reset-password</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-33358452\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.40212, "xdebug_link": null}, {"message": "[\n  ability => student-edit,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1525795024 data-indent-pad=\"  \"><span class=sf-dump-note>student-edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">student-edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1525795024\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.403692, "xdebug_link": null}, {"message": "[\n  ability => guardian-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-106758290 data-indent-pad=\"  \"><span class=sf-dump-note>guardian-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">guardian-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-106758290\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.404554, "xdebug_link": null}, {"message": "[\n  ability => teacher-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1113217756 data-indent-pad=\"  \"><span class=sf-dump-note>teacher-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">teacher-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1113217756\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.405281, "xdebug_link": null}, {"message": "[\n  ability => teacher-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1703512938 data-indent-pad=\"  \"><span class=sf-dump-note>teacher-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">teacher-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1703512938\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.405949, "xdebug_link": null}, {"message": "[\n  ability => timetable-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1283184451 data-indent-pad=\"  \"><span class=sf-dump-note>timetable-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">timetable-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1283184451\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.409705, "xdebug_link": null}, {"message": "[\n  ability => timetable-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1707348430 data-indent-pad=\"  \"><span class=sf-dump-note>timetable-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">timetable-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1707348430\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.410348, "xdebug_link": null}, {"message": "[\n  ability => timetable-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1859678254 data-indent-pad=\"  \"><span class=sf-dump-note>timetable-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">timetable-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1859678254\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.411981, "xdebug_link": null}, {"message": "[\n  ability => timetable-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1537882819 data-indent-pad=\"  \"><span class=sf-dump-note>timetable-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">timetable-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1537882819\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.413741, "xdebug_link": null}, {"message": "[\n  ability => holiday-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2003038549 data-indent-pad=\"  \"><span class=sf-dump-note>holiday-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">holiday-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2003038549\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.414441, "xdebug_link": null}, {"message": "[\n  ability => holiday-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-102001000 data-indent-pad=\"  \"><span class=sf-dump-note>holiday-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">holiday-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-102001000\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.416181, "xdebug_link": null}, {"message": "[\n  ability => lesson-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-958000386 data-indent-pad=\"  \"><span class=sf-dump-note>lesson-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">lesson-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-958000386\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.418033, "xdebug_link": null}, {"message": "[\n  ability => lesson-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1114907335 data-indent-pad=\"  \"><span class=sf-dump-note>lesson-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">lesson-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1114907335\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.419624, "xdebug_link": null}, {"message": "[\n  ability => topic-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1932166447 data-indent-pad=\"  \"><span class=sf-dump-note>topic-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">topic-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1932166447\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.421156, "xdebug_link": null}, {"message": "[\n  ability => assignment-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-465937318 data-indent-pad=\"  \"><span class=sf-dump-note>assignment-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">assignment-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-465937318\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.423187, "xdebug_link": null}, {"message": "[\n  ability => assignment-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1073703278 data-indent-pad=\"  \"><span class=sf-dump-note>assignment-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">assignment-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1073703278\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.424782, "xdebug_link": null}, {"message": "[\n  ability => assignment-submission,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-465111375 data-indent-pad=\"  \"><span class=sf-dump-note>assignment-submission </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">assignment-submission</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-465111375\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.426391, "xdebug_link": null}, {"message": "[\n  ability => slider-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1802915093 data-indent-pad=\"  \"><span class=sf-dump-note>slider-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">slider-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1802915093\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.426899, "xdebug_link": null}, {"message": "[\n  ability => notification-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1686411074 data-indent-pad=\"  \"><span class=sf-dump-note>notification-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">notification-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1686411074\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.427816, "xdebug_link": null}, {"message": "[\n  ability => class-teacher,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1252315150 data-indent-pad=\"  \"><span class=sf-dump-note>class-teacher </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">class-teacher</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1252315150\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.428536, "xdebug_link": null}, {"message": "[\n  ability => class-teacher,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1403002384 data-indent-pad=\"  \"><span class=sf-dump-note>class-teacher </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">class-teacher</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1403002384\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.429867, "xdebug_link": null}, {"message": "[\n  ability => class-teacher,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-991751764 data-indent-pad=\"  \"><span class=sf-dump-note>class-teacher </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">class-teacher</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-991751764\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.430569, "xdebug_link": null}, {"message": "[\n  ability => class-teacher,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1580930895 data-indent-pad=\"  \"><span class=sf-dump-note>class-teacher </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">class-teacher</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1580930895\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.431479, "xdebug_link": null}, {"message": "[\n  ability => class-teacher,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-973639616 data-indent-pad=\"  \"><span class=sf-dump-note>class-teacher </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">class-teacher</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-973639616\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.432079, "xdebug_link": null}, {"message": "[\n  ability => class-teacher,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-922597469 data-indent-pad=\"  \"><span class=sf-dump-note>class-teacher </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">class-teacher</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-922597469\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.432688, "xdebug_link": null}, {"message": "[\n  ability => class-teacher,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2058041323 data-indent-pad=\"  \"><span class=sf-dump-note>class-teacher </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">class-teacher</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2058041323\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.433296, "xdebug_link": null}, {"message": "[\n  ability => class-teacher,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-277743618 data-indent-pad=\"  \"><span class=sf-dump-note>class-teacher </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">class-teacher</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-277743618\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.433898, "xdebug_link": null}, {"message": "[\n  ability => class-teacher,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2136742197 data-indent-pad=\"  \"><span class=sf-dump-note>class-teacher </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">class-teacher</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2136742197\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.435913, "xdebug_link": null}, {"message": "[\n  ability => class-teacher,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1324642508 data-indent-pad=\"  \"><span class=sf-dump-note>class-teacher </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">class-teacher</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1324642508\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.43768, "xdebug_link": null}, {"message": "[\n  ability => attendance-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-606863189 data-indent-pad=\"  \"><span class=sf-dump-note>attendance-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">attendance-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-606863189\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.43857, "xdebug_link": null}, {"message": "[\n  ability => attendance-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1627047216 data-indent-pad=\"  \"><span class=sf-dump-note>attendance-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">attendance-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1627047216\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.439399, "xdebug_link": null}, {"message": "[\n  ability => announcement-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-741126242 data-indent-pad=\"  \"><span class=sf-dump-note>announcement-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">announcement-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-741126242\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.442017, "xdebug_link": null}, {"message": "[\n  ability => exam-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2070839418 data-indent-pad=\"  \"><span class=sf-dump-note>exam-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">exam-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2070839418\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.4429, "xdebug_link": null}, {"message": "[\n  ability => exam-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-299394757 data-indent-pad=\"  \"><span class=sf-dump-note>exam-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">exam-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-299394757\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.443543, "xdebug_link": null}, {"message": "[\n  ability => exam-upload-marks,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-432874563 data-indent-pad=\"  \"><span class=sf-dump-note>exam-upload-marks </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">exam-upload-marks</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-432874563\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.444205, "xdebug_link": null}, {"message": "[\n  ability => exam-result,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1316593886 data-indent-pad=\"  \"><span class=sf-dump-note>exam-result </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">exam-result</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1316593886\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.444837, "xdebug_link": null}, {"message": "[\n  ability => grade-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-56064745 data-indent-pad=\"  \"><span class=sf-dump-note>grade-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">grade-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-56064745\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.445431, "xdebug_link": null}, {"message": "[\n  ability => online-exam-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1301110959 data-indent-pad=\"  \"><span class=sf-dump-note>online-exam-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">online-exam-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1301110959\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.447058, "xdebug_link": null}, {"message": "[\n  ability => online-exam-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-65128848 data-indent-pad=\"  \"><span class=sf-dump-note>online-exam-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">online-exam-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-65128848\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.448732, "xdebug_link": null}, {"message": "[\n  ability => online-exam-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-426320969 data-indent-pad=\"  \"><span class=sf-dump-note>online-exam-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">online-exam-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-426320969\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.45089, "xdebug_link": null}, {"message": "[\n  ability => fees-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1560404772 data-indent-pad=\"  \"><span class=sf-dump-note>fees-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">fees-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1560404772\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.451842, "xdebug_link": null}, {"message": "[\n  ability => fees-type-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1930592598 data-indent-pad=\"  \"><span class=sf-dump-note>fees-type-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">fees-type-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1930592598\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.45247, "xdebug_link": null}, {"message": "[\n  ability => fees-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1935783598 data-indent-pad=\"  \"><span class=sf-dump-note>fees-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">fees-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1935783598\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.453216, "xdebug_link": null}, {"message": "[\n  ability => fees-paid,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-6873268 data-indent-pad=\"  \"><span class=sf-dump-note>fees-paid </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">fees-paid</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-6873268\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.454216, "xdebug_link": null}, {"message": "[\n  ability => fees-type-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1369322475 data-indent-pad=\"  \"><span class=sf-dump-note>fees-type-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">fees-type-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1369322475\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.454887, "xdebug_link": null}, {"message": "[\n  ability => schools-list,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1424469589 data-indent-pad=\"  \"><span class=sf-dump-note>schools-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">schools-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1424469589\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.462158, "xdebug_link": null}, {"message": "[\n  ability => schools-create,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2122043151 data-indent-pad=\"  \"><span class=sf-dump-note>schools-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">schools-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2122043151\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.462632, "xdebug_link": null}, {"message": "[\n  ability => schools-edit,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2109534156 data-indent-pad=\"  \"><span class=sf-dump-note>schools-edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">schools-edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2109534156\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.46307, "xdebug_link": null}, {"message": "[\n  ability => schools-delete,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2108605681 data-indent-pad=\"  \"><span class=sf-dump-note>schools-delete </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">schools-delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2108605681\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.463561, "xdebug_link": null}, {"message": "[\n  ability => package-list,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-50924254 data-indent-pad=\"  \"><span class=sf-dump-note>package-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">package-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-50924254\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.464941, "xdebug_link": null}, {"message": "[\n  ability => package-create,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1801975300 data-indent-pad=\"  \"><span class=sf-dump-note>package-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">package-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1801975300\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.465443, "xdebug_link": null}, {"message": "[\n  ability => package-edit,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-663282915 data-indent-pad=\"  \"><span class=sf-dump-note>package-edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">package-edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-663282915\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.465878, "xdebug_link": null}, {"message": "[\n  ability => package-delete,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1133566735 data-indent-pad=\"  \"><span class=sf-dump-note>package-delete </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">package-delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1133566735\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.466382, "xdebug_link": null}, {"message": "[\n  ability => package-list,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-827678932 data-indent-pad=\"  \"><span class=sf-dump-note>package-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">package-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-827678932\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.466906, "xdebug_link": null}, {"message": "[\n  ability => package-create,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1468322200 data-indent-pad=\"  \"><span class=sf-dump-note>package-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">package-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1468322200\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.467311, "xdebug_link": null}, {"message": "[\n  ability => package-edit,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-342107531 data-indent-pad=\"  \"><span class=sf-dump-note>package-edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">package-edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-342107531\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.467721, "xdebug_link": null}, {"message": "[\n  ability => package-delete,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1988752706 data-indent-pad=\"  \"><span class=sf-dump-note>package-delete </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">package-delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1988752706\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.468118, "xdebug_link": null}, {"message": "[\n  ability => addons-list,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1170840320 data-indent-pad=\"  \"><span class=sf-dump-note>addons-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">addons-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1170840320\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.46852, "xdebug_link": null}, {"message": "[\n  ability => addons-create,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-120888860 data-indent-pad=\"  \"><span class=sf-dump-note>addons-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">addons-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-120888860\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.469036, "xdebug_link": null}, {"message": "[\n  ability => addons-edit,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-554994536 data-indent-pad=\"  \"><span class=sf-dump-note>addons-edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">addons-edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-554994536\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.469486, "xdebug_link": null}, {"message": "[\n  ability => addons-delete,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-449177922 data-indent-pad=\"  \"><span class=sf-dump-note>addons-delete </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">addons-delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-449177922\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.469955, "xdebug_link": null}, {"message": "[\n  ability => addons-list,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-443879378 data-indent-pad=\"  \"><span class=sf-dump-note>addons-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">addons-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-443879378\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.47052, "xdebug_link": null}, {"message": "[\n  ability => addons-create,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2090191252 data-indent-pad=\"  \"><span class=sf-dump-note>addons-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">addons-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2090191252\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.471297, "xdebug_link": null}, {"message": "[\n  ability => addons-edit,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-108568485 data-indent-pad=\"  \"><span class=sf-dump-note>addons-edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">addons-edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-108568485\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.471826, "xdebug_link": null}, {"message": "[\n  ability => addons-delete,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-937691911 data-indent-pad=\"  \"><span class=sf-dump-note>addons-delete </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">addons-delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-937691911\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.472248, "xdebug_link": null}, {"message": "[\n  ability => package-list,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1626125436 data-indent-pad=\"  \"><span class=sf-dump-note>package-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">package-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1626125436\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.472671, "xdebug_link": null}, {"message": "[\n  ability => package-create,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-733566032 data-indent-pad=\"  \"><span class=sf-dump-note>package-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">package-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-733566032\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.473072, "xdebug_link": null}, {"message": "[\n  ability => package-edit,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1441824810 data-indent-pad=\"  \"><span class=sf-dump-note>package-edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">package-edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1441824810\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.473486, "xdebug_link": null}, {"message": "[\n  ability => package-delete,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1741712359 data-indent-pad=\"  \"><span class=sf-dump-note>package-delete </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">package-delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1741712359\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.473877, "xdebug_link": null}, {"message": "[\n  ability => subscription-view,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1997302983 data-indent-pad=\"  \"><span class=sf-dump-note>subscription-view </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">subscription-view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1997302983\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.474363, "xdebug_link": null}, {"message": "[\n  ability => expense-category-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-14514093 data-indent-pad=\"  \"><span class=sf-dump-note>expense-category-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">expense-category-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-14514093\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.474942, "xdebug_link": null}, {"message": "[\n  ability => expense-category-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-411352895 data-indent-pad=\"  \"><span class=sf-dump-note>expense-category-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">expense-category-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-411352895\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.475539, "xdebug_link": null}, {"message": "[\n  ability => expense-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-945149296 data-indent-pad=\"  \"><span class=sf-dump-note>expense-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">expense-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-945149296\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.47638, "xdebug_link": null}, {"message": "[\n  ability => payroll-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2125434571 data-indent-pad=\"  \"><span class=sf-dump-note>payroll-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">payroll-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2125434571\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.478303, "xdebug_link": null}, {"message": "[\n  ability => payroll-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-603716433 data-indent-pad=\"  \"><span class=sf-dump-note>payroll-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">payroll-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-603716433\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.479032, "xdebug_link": null}, {"message": "[\n  ability => Expense Management,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-380392646 data-indent-pad=\"  \"><span class=sf-dump-note>Expense Management </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Expense Management</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-380392646\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.479842, "xdebug_link": null}, {"message": "[\n  ability => payroll-settings-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1414010897 data-indent-pad=\"  \"><span class=sf-dump-note>payroll-settings-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">payroll-settings-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1414010897\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.481297, "xdebug_link": null}, {"message": "[\n  ability => gallery-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2090387212 data-indent-pad=\"  \"><span class=sf-dump-note>gallery-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">gallery-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2090387212\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.482572, "xdebug_link": null}, {"message": "[\n  ability => certificate-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1716991419 data-indent-pad=\"  \"><span class=sf-dump-note>certificate-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">certificate-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1716991419\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.483623, "xdebug_link": null}, {"message": "[\n  ability => certificate-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1763315676 data-indent-pad=\"  \"><span class=sf-dump-note>certificate-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">certificate-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1763315676\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.484708, "xdebug_link": null}, {"message": "[\n  ability => certificate-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-260888832 data-indent-pad=\"  \"><span class=sf-dump-note>certificate-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">certificate-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-260888832\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.485827, "xdebug_link": null}, {"message": "[\n  ability => certificate-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-950758177 data-indent-pad=\"  \"><span class=sf-dump-note>certificate-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">certificate-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-950758177\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.486583, "xdebug_link": null}, {"message": "[\n  ability => id-card-settings,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1054155941 data-indent-pad=\"  \"><span class=sf-dump-note>id-card-settings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">id-card-settings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1054155941\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.487299, "xdebug_link": null}, {"message": "[\n  ability => student-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1979405632 data-indent-pad=\"  \"><span class=sf-dump-note>student-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">student-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1979405632\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.488615, "xdebug_link": null}, {"message": "[\n  ability => staff-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1626390355 data-indent-pad=\"  \"><span class=sf-dump-note>staff-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">staff-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1626390355\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.489239, "xdebug_link": null}, {"message": "[\n  ability => role-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1784255699 data-indent-pad=\"  \"><span class=sf-dump-note>role-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">role-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1784255699\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.489614, "xdebug_link": null}, {"message": "[\n  ability => role-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1377000489 data-indent-pad=\"  \"><span class=sf-dump-note>role-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">role-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1377000489\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.489929, "xdebug_link": null}, {"message": "[\n  ability => staff-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-626723880 data-indent-pad=\"  \"><span class=sf-dump-note>staff-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">staff-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-626723880\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.490653, "xdebug_link": null}, {"message": "[\n  ability => staff-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-120925745 data-indent-pad=\"  \"><span class=sf-dump-note>staff-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">staff-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-120925745\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.491608, "xdebug_link": null}, {"message": "[\n  ability => approve-leave,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1876028461 data-indent-pad=\"  \"><span class=sf-dump-note>approve-leave </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">approve-leave</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1876028461\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.493206, "xdebug_link": null}, {"message": "[\n  ability => approve-leave,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1064357659 data-indent-pad=\"  \"><span class=sf-dump-note>approve-leave </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">approve-leave</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1064357659\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.493927, "xdebug_link": null}, {"message": "[\n  ability => faqs-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1243814648 data-indent-pad=\"  \"><span class=sf-dump-note>faqs-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">faqs-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1243814648\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.494738, "xdebug_link": null}, {"message": "[\n  ability => web-settings,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-35920262 data-indent-pad=\"  \"><span class=sf-dump-note>web-settings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">web-settings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-35920262\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.495526, "xdebug_link": null}, {"message": "[\n  ability => school-web-settings,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-521176587 data-indent-pad=\"  \"><span class=sf-dump-note>school-web-settings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">school-web-settings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-521176587\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.496258, "xdebug_link": null}, {"message": "[\n  ability => class-group-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-452767763 data-indent-pad=\"  \"><span class=sf-dump-note>class-group-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">class-group-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-452767763\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.497037, "xdebug_link": null}, {"message": "[\n  ability => faqs-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-132893155 data-indent-pad=\"  \"><span class=sf-dump-note>faqs-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">faqs-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-132893155\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.497918, "xdebug_link": null}, {"message": "[\n  ability => app-settings,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-356536754 data-indent-pad=\"  \"><span class=sf-dump-note>app-settings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">app-settings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-356536754\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.498829, "xdebug_link": null}, {"message": "[\n  ability => language-list,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-5463636 data-indent-pad=\"  \"><span class=sf-dump-note>language-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">language-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-5463636\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.499324, "xdebug_link": null}, {"message": "[\n  ability => school-setting-manage,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2012240877 data-indent-pad=\"  \"><span class=sf-dump-note>school-setting-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">school-setting-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2012240877\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.500093, "xdebug_link": null}, {"message": "[\n  ability => app-settings,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2134785445 data-indent-pad=\"  \"><span class=sf-dump-note>app-settings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">app-settings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2134785445\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.500622, "xdebug_link": null}, {"message": "[\n  ability => school-setting-manage,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1352025301 data-indent-pad=\"  \"><span class=sf-dump-note>school-setting-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">school-setting-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1352025301\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.501176, "xdebug_link": null}, {"message": "[\n  ability => student-id-card-settings,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1570175142 data-indent-pad=\"  \"><span class=sf-dump-note>student-id-card-settings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">student-id-card-settings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1570175142\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.502102, "xdebug_link": null}, {"message": "[\n  ability => system-setting-manage,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1364598601 data-indent-pad=\"  \"><span class=sf-dump-note>system-setting-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">system-setting-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1364598601\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.502582, "xdebug_link": null}, {"message": "[\n  ability => subscription-settings,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1589871832 data-indent-pad=\"  \"><span class=sf-dump-note>subscription-settings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">subscription-settings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1589871832\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.503225, "xdebug_link": null}, {"message": "[\n  ability => guidance-create,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-810301406 data-indent-pad=\"  \"><span class=sf-dump-note>guidance-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">guidance-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-810301406\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.50396, "xdebug_link": null}, {"message": "[\n  ability => guidance-list,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1871881046 data-indent-pad=\"  \"><span class=sf-dump-note>guidance-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">guidance-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1871881046\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.50466, "xdebug_link": null}, {"message": "[\n  ability => guidance-edit,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-74067147 data-indent-pad=\"  \"><span class=sf-dump-note>guidance-edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">guidance-edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-74067147\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.505575, "xdebug_link": null}, {"message": "[\n  ability => guidance-delete,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-15270599 data-indent-pad=\"  \"><span class=sf-dump-note>guidance-delete </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">guidance-delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-15270599\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.506571, "xdebug_link": null}, {"message": "[\n  ability => language-list,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-592571596 data-indent-pad=\"  \"><span class=sf-dump-note>language-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">language-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-592571596\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.507113, "xdebug_link": null}, {"message": "[\n  ability => fcm-setting-manage,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1994567763 data-indent-pad=\"  \"><span class=sf-dump-note>fcm-setting-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">fcm-setting-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1994567763\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.507746, "xdebug_link": null}, {"message": "[\n  ability => school-setting-manage,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1673639547 data-indent-pad=\"  \"><span class=sf-dump-note>school-setting-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">school-setting-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1673639547\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.508321, "xdebug_link": null}, {"message": "[\n  ability => email-setting-create,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-878803528 data-indent-pad=\"  \"><span class=sf-dump-note>email-setting-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">email-setting-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-878803528\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.508912, "xdebug_link": null}, {"message": "[\n  ability => email-setting-create,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1277487400 data-indent-pad=\"  \"><span class=sf-dump-note>email-setting-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">email-setting-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1277487400\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.509409, "xdebug_link": null}, {"message": "[\n  ability => email-template,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-495373035 data-indent-pad=\"  \"><span class=sf-dump-note>email-template </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">email-template</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-495373035\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.510164, "xdebug_link": null}, {"message": "[\n  ability => school-setting-manage,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-435648471 data-indent-pad=\"  \"><span class=sf-dump-note>school-setting-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">school-setting-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-435648471\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.511193, "xdebug_link": null}, {"message": "[\n  ability => system-setting-manage,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-701300859 data-indent-pad=\"  \"><span class=sf-dump-note>system-setting-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">system-setting-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-701300859\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.513015, "xdebug_link": null}, {"message": "[\n  ability => privacy-policy,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-21690731 data-indent-pad=\"  \"><span class=sf-dump-note>privacy-policy </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">privacy-policy</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-21690731\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.513879, "xdebug_link": null}, {"message": "[\n  ability => contact-us,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1140520291 data-indent-pad=\"  \"><span class=sf-dump-note>contact-us </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">contact-us</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1140520291\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.51449, "xdebug_link": null}, {"message": "[\n  ability => about-us,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1426506873 data-indent-pad=\"  \"><span class=sf-dump-note>about-us </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">about-us</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1426506873\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.514979, "xdebug_link": null}, {"message": "[\n  ability => terms-condition,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-243522405 data-indent-pad=\"  \"><span class=sf-dump-note>terms-condition </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">terms-condition</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-243522405\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.515495, "xdebug_link": null}, {"message": "[\n  ability => school-terms-condition,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-804425078 data-indent-pad=\"  \"><span class=sf-dump-note>school-terms-condition </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">school-terms-condition</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-804425078\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.516179, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "https://schola.test/payroll", "action_name": "payroll.index", "controller_action": "App\\Http\\Controllers\\PayrollController@index", "uri": "GET payroll", "controller": "App\\Http\\Controllers\\PayrollController@index<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=48\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=48\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/PayrollController.php:48-60</a>", "middleware": "web, Role, auth, checkSchoolStatus, status, language", "duration": "5.26s", "peak_memory": "94MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2096019250 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2096019250\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1741762481 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1741762481\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-723424606 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">schola.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">https://schola.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"742 characters\">XSRF-TOKEN=eyJpdiI6IitkeWRIdEZTdHdnZjdRQlRJekRwSFE9PSIsInZhbHVlIjoiTjhSRytZRkxtNW1PeUowVDVHVXZvM09CdHlDUVR0RUtnZHNiQkxZemJJRlU2anhDR2UwZk1vaU9FUElacSttRXppNEZPSmtaVUNuNjFJeXFsNG11SmR4Q2Exc2lSYXA3N3Z6bXV2VXdtM2tmU2tSTEgxUGpaTWNHY0pMOStBeXkiLCJtYWMiOiJkODQ5MzQwMjdmODAwOGEyN2YwYzI4YTQ2MjI4ZWUzYTk3OGUyNTlmYTdiMTU0OTg0NzBiOTZhOGRmMTlhMDFkIiwidGFnIjoiIn0%3D; schola_saas_school_management_system_session=eyJpdiI6IkNDYUlnZGNWd0xLVENLcitQVk5ja3c9PSIsInZhbHVlIjoiZ1BoTk9VSlU1dTJvNXpCWmowTlh2c3ZkR1RKYWlQamQ1RDZuT2YvWldYdFJDUDZSbGhPT3ZzNkUvTUtDb1lLSktPblhKdUszWi84bkl0TW5QbDg4TlVHZDhiekhQdVZNRkV4TXlhSzRNY2ZzUTk4VUlsc054Z1dETmRHQmR1aUciLCJtYWMiOiI4ZmEzYmU2MzdiZDEwMGQzZDVhZDJiMWE0ZTEyODRiMTUxNTcwZjgzNDAzZjI0OGViYjBhYmFiODNhOTZiNDEzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-723424606\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1316252806 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0nsyLuN5aAz6u8lgTG1r68KyFeDp1NRQbXnBoOH</span>\"\n  \"<span class=sf-dump-key>schola_saas_school_management_system_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IQrLeRaEfTugtoRzBcuJ4wOlCeh4PG6VwaBH418U</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1316252806\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1482119080 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 10 Jun 2025 03:11:14 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1482119080\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1886373003 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0nsyLuN5aAz6u8lgTG1r68KyFeDp1NRQbXnBoOH</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">https://schola.test/payroll/custom-payroll</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>landing_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>language</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Language\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Language</span></span> {<a class=sf-dump-ref>#3546</a><samp data-depth=2 class=sf-dump-compact>\n    #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">languages</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n    +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n    #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n    +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n    +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n    +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">English</span>\"\n      \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"7 characters\">en.json</span>\"\n      \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>is_rtl</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">English</span>\"\n      \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"7 characters\">en.json</span>\"\n      \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>is_rtl</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n    +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n    +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">code</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">is_rtl</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n    </samp>]\n  </samp>}\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1749518683</span>\n  </samp>]\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1886373003\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://schola.test/payroll", "action_name": "payroll.index", "controller_action": "App\\Http\\Controllers\\PayrollController@index"}, "badge": null}}