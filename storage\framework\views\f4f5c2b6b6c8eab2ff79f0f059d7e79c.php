<script src="<?php echo e(asset('/assets/js/Chart.min.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/js/jquery.validate.min.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/jquery-toast-plugin/jquery.toast.min.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/select2/select2.min.js')); ?>"></script>

<script src="<?php echo e(asset('/assets/js/off-canvas.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/js/hoverable-collapse.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/js/misc.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/js/settings.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/js/todolist.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/js/ekko-lightbox.min.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/js/jquery.tagsinput.min.js')); ?>"></script>

<script src="<?php echo e(asset('/assets/js/apexcharts.js')); ?>"></script>






<script src="https://unpkg.com/bootstrap-table@1.22.1/dist/bootstrap-table.min.js"></script>
<script src="<?php echo e(asset('/assets/bootstrap-table/bootstrap-table-mobile.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/bootstrap-table/bootstrap-table-export.min.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/bootstrap-table/fixed-columns.min.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/bootstrap-table/tableExport.min.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/bootstrap-table/jspdf.min.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/bootstrap-table/jspdf.plugin.autotable.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/bootstrap-table/reorder-rows.min.js')); ?>"></script>
<script src="https://cdn.sheetjs.com/xlsx-0.20.1/package/dist/xlsx.full.min.js"></script>
<script src="<?php echo e(asset('/assets/bootstrap-table/jquery.tablednd.min.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/bootstrap-table/loadash.min.js')); ?>"></script>

<script src="<?php echo e(asset('/assets/js/jquery.cookie.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/js/sweetalert2.all.min.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/js/datepicker.min.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/js/jquery.repeater.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/tinymce/tinymce.min.js')); ?>"></script>

<script src="<?php echo e(asset('/assets/color-picker/jquery-asColor.min.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/color-picker/color.min.js')); ?>"></script>

<script src="<?php echo e(asset('/assets/js/custom/validate.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/js/jquery-additional-methods.min.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/js/custom/function.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/js/custom/common.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/js/custom/custom.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/js/custom/bootstrap-table/actionEvents.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/js/custom/bootstrap-table/formatter.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/js/custom/bootstrap-table/queryParams.js')); ?>"></script>

<script src="<?php echo e(asset('/assets/ckeditor-4/ckeditor.js')); ?>"></script>
<script src="<?php echo e(asset('/assets/ckeditor-4/adapters/jquery.js')); ?>" async></script>

<script src="<?php echo e(asset('/assets/js/momentjs.js')); ?>"></script>
<script type='text/javascript'>
    <?php if($errors->any()): ?>
    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    $.toast({
        text: '<?php echo e($error); ?>',
        showHideTransition: 'slide',
        icon: 'error',
        loaderBg: '#f2a654',
        position: 'top-right'
    });
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <?php endif; ?>

    <?php if(Session::has('success')): ?>
    $.toast({
        text: '<?php echo e(Session::get('success')); ?>',
        showHideTransition: 'slide',
        icon: 'success',
        loaderBg: '#f96868',
        position: 'top-right'
    });
    <?php endif; ?>

    <?php if(Session::has('error')): ?>
    $.toast({
        text: '<?php echo e(Session::get('error')); ?>',
        showHideTransition: 'slide',
        icon: 'error',
        loaderBg: '#f2a654',
        position: 'top-right'
    });
    <?php endif; ?>
</script>
<script>
    let date_format = '<?php echo e($schoolSettings['date_format'] ?? $systemSettings['date_format']); ?>'.replace('Y', 'YYYY').replace('m', 'MM').replace('d', 'DD');

    let date_time_format = '<?php echo e($schoolSettings['date_format'] ?? $systemSettings['date_format']); ?> <?php echo e($schoolSettings['time_format'] ?? $systemSettings['time_format']); ?>'.replace('Y', 'YYYY').replace('m', 'MM').replace('d', 'DD').replace('h', 'hh').replace('H', 'HH').replace('i', 'mm').replace('a', 'a').replace('A', 'A');

    let time_format = '<?php echo e($schoolSettings['time_format'] ?? $systemSettings['time_format']); ?>'.replace('h', 'hh').replace('H', 'HH').replace('i', 'mm').replace('a', 'a').replace('A', 'A');



    function isRTL() {
        var dir = $('html').attr('dir');
        if (dir === 'rtl') {
            return true;
        } else {
            return false;
        }
        return false;
        return dir === 'rtl';
    }
    let toast_position = 'top-right';
    if (isRTL()) {
        toast_position = 'top-left';
    } else {
        toast_position = 'top-right';
    }

    
    setTimeout(() => {
        
        $(document).ready(function() {
            var targetNode = document.querySelector('thead');

            // Apply initial styles
            $('th[data-field="operate"]').addClass('action-column');

            // Create an observer instance linked to the callback function
            var observer = new MutationObserver(function(mutationsList, observer) {
                for (var mutation of mutationsList) {
                    if (mutation.type === 'childList') {
                        // Reapply the class when changes are detected
                        $('th[data-field="operate"]').addClass('action-column');
                    }
                }
            });

            // Start observing the target node for configured mutations
            observer.observe(targetNode, { childList: true, subtree: true });
        });

    }, 500);
    

    // razorpay-payment-button
    setTimeout(() => {
        $('.razorpay-payment-button').addClass('btn btn-info');
    }, 100);



    document.addEventListener("DOMContentLoaded", function () {
        var isMobile = window.matchMedia("only screen and (max-width: 768px)").matches;
        //var table = document.getElementsByClassName('reorder-table-row');
        var tables = document.getElementsByClassName('reorder-table-row');
        // if (table) {
        //     if (isMobile) {
        //         table.removeAttribute('data-reorderable-rows');
        //     } else {
        //         table.setAttribute('data-reorderable-rows', 'true');
        //     }
        // }
        if (tables.length > 0) {
            // Convert HTMLCollection to Array and iterate through each table
            Array.from(tables).forEach(function(table) {
                if (isMobile) {
                    table.removeAttribute('data-reorderable-rows');
                } else {
                    table.setAttribute('data-reorderable-rows', 'true');
                }
            });
        }
        // Initialize the table
        $('.reorder-table-row').bootstrapTable();
    });



    document.addEventListener("DOMContentLoaded", function() {
        // Add the event listener for the button to initiate the payment

/*
        setTimeout(() => {
            document.getElementById('razorpay-button').onclick = function(e) {
                let baseUrl = window.location.origin;
                var order_id = '';
                var paymentTransactionId = '';

                $.ajax({
                    type: "post",
                    url: baseUrl + '/subscriptions/create/order-id',
                    data: {
                        amount : $('.bill_amount').val(),
                        currency : "<?php echo e($system_settings['currency_code'] ?? 'INR'); ?>",

                        type : $('.type').val(),
                        package_type : $('.package_type').val(),
                        package_id : $('.package_id').val(),
                        upcoming_plan_type : $('.upcoming_plan_type').val(),
                        subscription_id : $('.subscription_id').val(),
                        feature_id : $('.feature_id').val(),
                        end_date : $('.end_date').val(),
                        
                    },
                    success: function (response) {
                        if (response.data) {
                            order_id = response.data.order.id;
                            paymentTransactionId = response.data.paymentTransaction.id;
                            var options = {
                                "key": "<?php echo e($paymentConfiguration->api_key ?? ''); ?>", // Enter the Key ID generated from the Dashboard
                                "amount": $('.bill_amount').val() * 100, // Amount is in currency subunits. Default currency is INR. Hence, 100 refers to 1 INR
                                "currency": "<?php echo e($system_settings['currency_code'] ?? 'MYR'); ?>",
                                "name": "<?php echo e($system_settings['system_name'] ?? 'Schola Saas'); ?>",
                                "description": "Razorpay",
                                "order_id": order_id,
                                "handler": function(response) {
                                    // Set the response data in the form
                                    $('.razorpay_payment_id').val(response.razorpay_payment_id);
                                    $('.razorpay_signature').val(response.razorpay_signature);
                                    $('.razorpay_order_id').val(response.razorpay_order_id);
                                    $('.paymentTransactionId').val(paymentTransactionId);

                                    // Submit the form
                                    document.querySelector('.razorpay-form').submit();
                                }
                            };

                            var rzp1 = new Razorpay(options);
                            rzp1.open();
                        } else {
                            Swal.fire({icon: 'error', text: response.message});
                        }
                    }
                });
                e.preventDefault();
            }
        }, 100); 
*/
    });

</script>
<?php /**PATH D:\laragon\www\schola\resources\views/layouts/footer_js.blade.php ENDPATH**/ ?>