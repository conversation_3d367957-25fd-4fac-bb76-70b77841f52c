1749526877O:19:"App\Models\Semester":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:9:"semesters";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:2;s:4:"name";s:12:"1st Semester";s:11:"start_month";i:1;s:9:"end_month";i:6;s:9:"school_id";i:1;s:10:"created_at";s:19:"2023-11-29 20:21:15";s:10:"updated_at";s:19:"2025-01-02 16:09:43";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:2;s:4:"name";s:12:"1st Semester";s:11:"start_month";i:1;s:9:"end_month";i:6;s:9:"school_id";i:1;s:10:"created_at";s:19:"2023-11-29 20:21:15";s:10:"updated_at";s:19:"2025-01-02 16:09:43";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:3:{i:0;s:7:"current";i:1;s:16:"start_month_name";i:2;s:14:"end_month_name";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:11:"start_month";i:2;s:9:"end_month";i:3;s:9:"school_id";i:4;s:10:"created_at";i:5;s:10:"updated_at";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * forceDeleting";b:0;}