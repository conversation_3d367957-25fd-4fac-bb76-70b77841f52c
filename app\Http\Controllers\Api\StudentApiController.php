<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\TimetableCollection;
use App\Http\Resources\UserDataResource;
use App\Models\AssignmentSubmission;
use App\Models\SubjectAttendance;
use App\Models\Subject;
use App\Models\StudentFeesPaid;
use App\Repositories\Announcement\AnnouncementInterface;
use App\Repositories\Assignment\AssignmentInterface;
use App\Repositories\AssignmentSubmission\AssignmentSubmissionInterface;
use App\Repositories\Attendance\AttendanceInterface;
use App\Repositories\Exam\ExamInterface;
use App\Repositories\ExamResult\ExamResultInterface;
use App\Repositories\Files\FilesInterface;
use App\Repositories\Holiday\HolidayInterface;
use App\Repositories\Lessons\LessonsInterface;
use App\Repositories\OnlineExam\OnlineExamInterface;
use App\Repositories\OnlineExamQuestionChoice\OnlineExamQuestionChoiceInterface;
use App\Repositories\OnlineExamStudentAnswer\OnlineExamStudentAnswerInterface;
use App\Repositories\SessionYear\SessionYearInterface;
use App\Repositories\Sliders\SlidersInterface;
use App\Repositories\Student\StudentInterface;
use App\Repositories\StudentOnlineExamStatus\StudentOnlineExamStatusInterface;
use App\Repositories\StudentSubject\StudentSubjectInterface;
use App\Repositories\SubjectTeacher\SubjectTeacherInterface;
use App\Repositories\Timetable\TimetableInterface;
use App\Repositories\Topics\TopicsInterface;
use App\Repositories\User\UserInterface;
use App\Services\CachingService;
use App\Services\FeaturesService;
use App\Services\ResponseService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use JetBrains\PhpStorm\NoReturn;
use App\Repositories\StudentFeesPaid\StudentFeesPaidInterface;
use App\Repositories\PaymentConfiguration\PaymentConfigurationInterface;
use Throwable;
use DateTime;
use Barryvdh\DomPDF\Facade\Pdf;

class StudentApiController extends Controller {
    private StudentInterface $student;
    private UserInterface $user;
    private AssignmentInterface $assignment;
    private AssignmentSubmissionInterface $assignmentSubmission;
    private FilesInterface $files;
    private CachingService $cache;
    private StudentSubjectInterface $studentSubject;
    private TimetableInterface $timetable;
    private ExamInterface $exam;
    private ExamResultInterface $examResult;
    private LessonsInterface $lesson;
    private TopicsInterface $lessonTopic;
    private AttendanceInterface $attendance;
    private HolidayInterface $holiday;
    private SessionYearInterface $sessionYear;
    private SubjectTeacherInterface $subjectTeacher;
    private AnnouncementInterface $announcement;
    private OnlineExamInterface $onlineExam;
    private StudentOnlineExamStatusInterface $studentOnlineExamStatus;
    private OnlineExamQuestionChoiceInterface $onlineExamQuestionChoice;
    private OnlineExamStudentAnswerInterface $onlineExamStudentAnswer;
    private PaymentConfigurationInterface $paymentConfigurations;
    private SlidersInterface $sliders;
    private FeaturesService $featureService;
    private SubjectTeacherInterface $subjectTeachers;
    private StudentFeesPaidInterface $studentFeesPaid;

    public function __construct(StudentInterface $student, UserInterface $user, AssignmentInterface $assignment,PaymentConfigurationInterface $paymentConfigurations, AssignmentSubmissionInterface $assignmentSubmission, FilesInterface $files, CachingService $cache, StudentSubjectInterface $studentSubject, TimetableInterface $timetable, ExamInterface $exam, ExamResultInterface $examResult, LessonsInterface $lesson, TopicsInterface $lessonTopic, AttendanceInterface $attendance, HolidayInterface $holiday, SessionYearInterface $sessionYear, SubjectTeacherInterface $subjectTeacher, AnnouncementInterface $announcement, OnlineExamInterface $onlineExam, StudentOnlineExamStatusInterface $studentOnlineExamStatus, OnlineExamQuestionChoiceInterface $onlineExamQuestionChoice, OnlineExamStudentAnswerInterface $onlineExamStudentAnswer, SlidersInterface $sliders, FeaturesService $featuresService, StudentFeesPaidInterface $studentFeesPaid) {
        $this->student = $student;
        $this->user = $user;
        $this->assignment = $assignment;
        $this->assignmentSubmission = $assignmentSubmission;
        $this->files = $files;
        $this->cache = $cache;
        $this->studentSubject = $studentSubject;
        $this->timetable = $timetable;
        $this->exam = $exam;
        $this->examResult = $examResult;
        $this->lesson = $lesson;
        $this->lessonTopic = $lessonTopic;
        $this->attendance = $attendance;
        $this->holiday = $holiday;
        $this->sessionYear = $sessionYear;
        $this->subjectTeacher = $subjectTeacher;
        $this->announcement = $announcement;
        $this->onlineExam = $onlineExam;
        $this->studentOnlineExamStatus = $studentOnlineExamStatus;
        $this->onlineExamQuestionChoice = $onlineExamQuestionChoice;
        $this->onlineExamStudentAnswer = $onlineExamStudentAnswer;
        $this->paymentConfigurations = $paymentConfigurations;
        $this->sliders = $sliders;
        $this->featureService = $featuresService;
        $this->studentFeesPaid = $studentFeesPaid;
    }


    #[NoReturn] public function login(Request $request) {
        $validator = Validator::make($request->all(), [
            'gr_number' => 'required', 'password' => 'required',
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        if (Auth::attempt(['email' => $request->gr_number, 'password' => $request->password, 'status' => 1])) {
            //Here Email Field is referenced as a GR Number for Student
            $auth = Auth::user();
            if (!$auth->hasRole('Student')) {
                ResponseService::errorResponse('Invalid Login Credentials', null, config('constants.RESPONSE_CODE.INVALID_LOGIN'));
            }
            // Check school status is activated or not
            if ($auth->school->status == 0) {
                ResponseService::errorResponse('Your account has been deactivated', null, config('constants.RESPONSE_CODE.INVALID_LOGIN'));
            }
            $token = $auth->createToken($auth->first_name)->plainTextToken;
            $user = $auth->load(['student.class_section' => function ($q) {
                $q->with('section', 'class', 'medium');
            }, 'student.guardian', 'school']);

            // child.user', 'child.class_section.class', 'child.class_section.section', 'child.class_section.medium', 'child.user.school
            // $user = $auth->load(['student.guardian.child' => function($q) {
            //     $q->with('user.school','class_section.class','class_section.section', 'class_section.medium');
            // }]);

            if ($request->fcm_id) {
                $auth->fcm_id = $request->fcm_id;
                $auth->save();
            }
            ResponseService::successResponse('User logged-in!', new UserDataResource($user), ['token' => $token]);
        }
        ResponseService::errorResponse('Invalid Login Credentials', null, config('constants.RESPONSE_CODE.INVALID_LOGIN'));
    }

    public function forgotPassword(Request $request) {
        $validator = Validator::make($request->all(), [
            'gr_no' => 'required',
            'dob'   => 'required|date',
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $user = $this->user->builder()->whereHas('student', function ($query) use ($request) {
                $query->where('admission_no', $request->gr_no);
            })->whereDate('dob', '=', date('Y-m-d', strtotime($request->dob)))->first();

            if ($user) {
                /*NOTE : Revert this if needed */
                //$this->user->update($user->id, ['reset_request' => 1,'school_id' => $user->school_id]);
                $this->user->update($user->id, ['reset_request' => 1, 'school_id' => $user->school_id]);
                ResponseService::successResponse("Request Send Successfully");
            } else {
                ResponseService::errorResponse("Invalid user Details", null, config('constants.RESPONSE_CODE.INVALID_USER_DETAILS'));
            }
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function classSubjects(Request $request) {
        try {
            $user = $request->user();
            $subjects = $user->student->currentSemesterClassSubjects();
            ResponseService::successResponse('Class Subject Fetched Successfully.', $subjects);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function subjects(Request $request) {
        try {
            $user = $request->user();
            $subjects = $user->student->currentSemesterSubjects();

            $query = DB::table('student_subjects as ss')
                ->join('class_subjects as cs', 'ss.class_subject_id', '=', 'cs.id')
                ->select('ss.*', 'cs.*')
                ->where('ss.student_id', $user->id)
                ->where('cs.type', 'Elective')
                ->get();

            $count = $query->count();
            if($count == 0){
                unset($subjects['elective_subject']);
            }
            ResponseService::successResponse('Student Subject Fetched Successfully.', $subjects);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function selectSubjects(Request $request) {
        $validator = Validator::make($request->all(), [
            'subject_group.*.id'               => 'required',
            'subject_group.*.class_subject_id' => 'required|array',
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            DB::beginTransaction();
            $sessionYear = $this->cache->getDefaultSessionYear(); // Default Session Year From Cache
            $student = $request->user()->student; // Logged in Student Details
            $classSection = $student->class_section; // Class Section Data
            $studentSubject = array();

            // Loop to Subject Group
            foreach ($request->subject_group as $subjectGroup) {
                // Loop to Subject's ID
                foreach ($subjectGroup['class_subject_id'] as $classSubjectId) {
                    // Create Two Dimensional Student Subject Array
                    $studentSubject[] = array(
                        'student_id'       => $student->user_id,
                        'class_subject_id' => $classSubjectId,
                        'class_section_id' => $classSection->id,
                        'session_year_id'  => $sessionYear->id,
                    );
                }
            }

            // Update OR Create Data
            $this->studentSubject->upsert($studentSubject, ['student_id', 'class_subject_id', 'class_section_id', 'session_year_id'], ['student_id', 'class_subject_id', 'class_section_id', 'session_year_id',]);
            DB::commit();
            ResponseService::successResponse("Subject Selected Successfully");
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "StudentApiController :- selectSubject Method");
            ResponseService::errorResponse();
        }
    }

    public function getGuardianDetails(Request $request) {
        try {
            $student = $request->user()->student->load(['guardian']);
            $data = array(
                'guardian' => (!empty($student->guardian)) ? $student->guardian : (object)[]
            );
            ResponseService::successResponse("Guardian Details Fetched Successfully", $data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getTimetable(Request $request) {
        try {
            $student = $request->user()->student;
            $timetable = $this->timetable->builder()->where('class_section_id', $student->class_section_id)->with('subject_teacher.subject:id,name,type,code,bg_color,image', 'subject_teacher.teacher:id,first_name,last_name')
            ->with('subject')
            ->orderBy('day')->orderBy('start_time')->get();
            ResponseService::successResponse("Timetable Fetched Successfully", new TimetableCollection($timetable));
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }
    public function getStudentsFees(Request $request) {
        try {
            
            // Check if session_year_id is provided and valid
            if ($request->has('session_year_id') && !is_numeric($request->session_year_id)) {
                ResponseService::validationError("The session_year_id must be numeric.");
            }
    
            $student = DB::table('students')->where('user_id',Auth::user()->id)->value('class_section_id');
            $classId = DB::table('students')->join('class_sections', 'students.class_section_id', '=', 'class_sections.id')->where('students.id')->value('class_sections.class_id');
            $schoolId = DB::table('students')->join('users', 'students.user_id', '=', 'users.id')->where('students.id') ->value('users.school_id');
            // var_dump(Auth::user());

    
            $classId = $student->class_section->class_id;
            $schoolId = $student->user->school_id;
    
            $currentSessionYear = $this->cache->getDefaultSessionYear($schoolId);
            $sessionYearId = $request->session_year_id ?? $currentSessionYear->id;
    
            $fees = $this->studentFees->builder()->where('class_id', $classId)
                ->with([
                    'student_fees_details', 'student_fees_paid', 'session_year',
                    'class.medium', 'class.stream'
                ])
                ->where([
                    'session_year_id' => $sessionYearId,
                    'student_id' => $student->id,
                    'status' => 'published'
                ])
                ->whereDoesntHave('student_fees_details', function ($query) {
                    $query->whereIn('fees_type_name', ['Overdue Fees', 'Early Discount']);
                })
                ->orderByDesc('created_at')
                ->get();
    
            $currentDateTimestamp = new DateTime(date('Y-m-d'));
    
            foreach ($fees as $fee) {
                $feesDateTimestamp = new DateTime($fee->due_date);
    
                if (count($fee->student_fees_details) > 0) {
                    $fee->is_overdue = $currentDateTimestamp > $feesDateTimestamp; 
                    if ($fee->is_overdue && $fee->due_charges > 0) {
                        $fee->due_charges_amount = $fee->total_compulsory_fees * $fee->due_charges / 100;
                    }
                }
    
                $fee->is_fully_paid = 0;
                if (count($fee->student_fees_paid) > 0) {
                    $fee->is_fully_paid = $fee->student_fees_paid[0]->is_fully_paid;
                }
            }
    
            $outFees = $fees->sortBy([
                ["is_fully_paid", "asc"],
                ["created_at", "desc"],
            ]);
    
            $fmtFees = [];
            foreach ($outFees as $out) {
                if ($request->type == '0') {
                    if ($out->is_fully_paid == 0) {
                        $fmtFees[] = $out;
                    }
                } else if ($request->type == '1') {
                    if ($out->is_fully_paid == 1) {
                        $fmtFees[] = $out;
                    }
                } else if ($request->type == '2') {
                    // No specific action for type '2' defined
                } else {
                    $fmtFees[] = $out;
                }
            }
    
            ResponseService::successResponse("Fees Fetched Successfully", $fmtFees);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }
    
    //Get Fees Details
    public function getStudentsFeesByPage(Request $request) {
        $validator = Validator::make($request->all(), [
            'session_year_id' => 'nullable|numeric',
        ]);
    
        if ($validator->fails()) {
            return ResponseService::validationError($validator->errors()->first());
        }
    
        $student = DB::table('students')->where('user_id', Auth::user()->id)->first();
        if (!$student) {
            return ResponseService::errorResponse(
                "Child's Account is not Active. Contact School Support", 
                null, 
                config('constants.RESPONSE_CODE.INACTIVE_CHILD')
            );
        }
    
        $currentSessionYear = $this->cache->getDefaultSessionYear(Auth::user()->school_id);
        $sessionYearId = $request->session_year_id ?? $currentSessionYear->id;
        // $classId = $student->class_section->class_id;
        // $schoolId = $student->user->school_id;
        $sessionYearId = $request->session_year_id ?? $currentSessionYear->id;
        try {
            $query = null;
            if($request->status){
                if($request->status == 'unpaid'){
                    $query = DB::table('student_fees as sf')
                        ->join('classes as c', 'c.id', '=', 'sf.class_id')
                        ->leftJoin('student_fees_paids as sfp', 'sf.id', '=', 'sfp.student_fees_id')
                        ->where('sf.student_id', $student->id)
                        ->whereNull('sf.deleted_at')
                        ->where('sf.status', 'published')
                        ->whereNull('sfp.student_fees_id')
                        ->select(
                            'sf.early_date',
                            'sf.early_offer',
                            'sf.early_offer_amount',
                            'sf.id',
                            'sf.name',
                            'sf.due_date',
                            'sf.due_charges',
                            'sf.due_charges_amount',
                            'sf.created_at',
                            'c.name as class',
                            'sfp.payment_detail',
                            'sf.uid',
                            'sf.tax_type',
                            'sf.tax_percentage'
                        )
                        ->orderBy('sf.created_at', 'DESC')
                        ->paginate(10);
                }
                else if($request->status == 'pending'){
                    $query = DB::table('student_fees as sf')
                        ->join('classes as c', 'c.id', '=', 'sf.class_id')
                        ->leftJoin('student_fees_paids as sfp', 'sf.id', '=', 'sfp.student_fees_id')
                        ->where('sf.student_id', $student->id)
                        ->whereNull('sf.deleted_at')
                        ->where('sf.status', 'published')
                        ->where('sfp.status', 2) // Ensure student_fees_paids.status = 2
                        ->select(
                            'sf.early_date',
                            'sf.early_offer',
                            'sf.early_offer_amount',
                            'sf.id',
                            'sf.name',
                            'sf.due_date',
                            'sf.due_charges',
                            'sf.due_charges_amount',
                            'sf.created_at',
                            'c.name as class',
                            'sfp.payment_detail',
                            'sf.uid',
                            'sf.tax_type',
                            'sf.tax_percentage'
                        )
                        ->orderBy('sf.created_at', 'DESC')
                        ->paginate(10);
                }
                else if($request->status == 'paid'){
                    $query = DB::table('student_fees as sf')
                        ->join('classes as c', 'c.id', '=', 'sf.class_id')
                        ->leftJoin('student_fees_paids as sfp', 'sf.id', '=', 'sfp.student_fees_id')
                        ->where('sf.student_id', $student->id)
                        ->whereNull('sf.deleted_at')
                        ->where('sf.status', 'published')
                        ->where('sfp.status', 1) // Ensure student_fees_paids.status = 2
                        ->select(
                            'sf.early_date',
                            'sf.early_offer',
                            'sf.early_offer_amount',
                            'sf.id',
                            'sf.name',
                            'sf.due_date',
                            'sf.due_charges',
                            'sf.due_charges_amount',
                            'sf.created_at',
                            'c.name as class',
                            'sfp.payment_detail',
                            'sf.uid',
                            'sf.tax_type',
                            'sf.tax_percentage'
                        )
                        ->orderBy('sf.created_at', 'DESC')
                        ->paginate(10);
    
                    //$query = DB::table('student_fees as sf')
                        // ->join('classes as c', 'c.id', '=', 'sf.class_id')
                        // ->leftJoin('student_fees_paids as sfp', 'sf.id', '=', 'sfp.student_fees_id')
                        // ->where('sf.student_id', $student->id)
                        // ->whereNull('sf.deleted_at')
                        // ->where('sf.status', 'published')
                        // ->where('sfp.status', 1) // Ensure student_fees_paids.status = 1
                        // ->select(
                        //     'sf.early_date',
                        //     'sf.early_offer',
                        //     'sf.early_offer_amount',
                        //     'sf.id',
                        //     'sf.name',
                        //     'sf.due_date',
                        //     'sf.due_charges',
                        //     'sf.due_charges_amount',
                        //     'sf.created_at',
                        //     'c.name as class',
                        //     'sfp.payment_detail'
                        // )
                        // ->orderBy('sf.created_at', 'DESC')
                        // ->paginate(10);
                }
                else{

                }
            }
            else{
                if($request->is_paid){
                    // $query = DB::table('student_fees AS sf')
                    //     ->join('classes AS c', 'c.id', '=', 'sf.class_id')
                    //     ->join('student_fees_paids AS sfp', 'sfp.student_fees_id', '=', 'sf.id')
                    //     ->select(
                    //         'sf.early_date',
                    //         'sf.early_offer',
                    //         'sf.early_offer_amount',
                    //         'sf.id',
                    //         'sf.name',
                    //         'sf.due_date',
                    //         'sf.due_charges',
                    //         'sf.due_charges_amount',
                    //         'sf.created_at',
                    //         'c.name AS class'
                    //     )
                    //     ->whereNull('sf.deleted_at')
                    //     //->where('sf.session_year_id', $sessionYearId)
                    //     ->where('sf.status', 'published')
                    //     ->where('sfp.status',1)
                    //     ->where('sf.student_id', $student->id)
                    //     ->orderByDesc('sf.created_at')->paginate(10);
                    $query = DB::table('student_fees as sf')
                        ->join('student_fees_paids as sfp', 'sfp.student_fees_id', '=', 'sf.id')
                        ->join('classes as c', 'c.id', '=', 'sf.class_id')
                        ->select(
                            'sf.early_date',
                            'sf.early_offer',
                            'sf.early_offer_amount',
                            'sf.id',
                            'sf.name',
                            'sf.due_date',
                            'sf.due_charges',
                            'sf.due_charges_amount',
                            'sf.created_at',
                            'c.name AS class',
                            'sf.tax_type',
                            'sf.tax_percentage'
                        )
                        ->where('sf.student_id', $student->id)
                        ->whereNull('sf.deleted_at')
                        ->where('sf.status', 'published')
                        ->where(function($query) {
                            $query->whereNull('sfp.is_fully_paid')
                                ->orWhere('sfp.status', '=', 1);
                        })
                        ->orderBy('sf.created_at', 'DESC')
                        ->paginate(10);
                }
                else{
                    // $query = DB::table('student_fees AS sf')
                    //     ->join('classes AS c', 'c.id', '=', 'sf.class_id')
                    //     ->leftJoin('student_fees_paids AS sfp','sfp.student_fees_id','=','sf.id')
                    //     ->select(
                    //         'sf.early_date',
                    //         'sf.early_offer',
                    //         'sf.early_offer_amount',
                    //         'sf.id',
                    //         'sf.name',
                    //         'sf.due_date',
                    //         'sf.due_charges',
                    //         'sf.due_charges_amount',
                    //         'sf.created_at',
                    //         'c.name AS class'
                    //     )
                    //     ->whereNull('sf.deleted_at')
                    //     //->where('sf.session_year_id', $sessionYearId)
                    //     ->where('sf.status', 'published')
                    //     ->where('sf.student_id', $student->id)
                    //     ->whereNull('sfp.is_fully_paid')
                    //     ->orWhere('sfp.status','!=',1)
                    //     // ->whereNotIn(
                    //     //     'sf.id',
                    //     //     DB::table('student_fees_paids')->pluck('student_fees_id')
                    //     // )
                    //     ->orderByDesc('sf.created_at')->paginate(10);



                    // $query = DB::table('student_fees as sf')
                    //     ->join('student_fees_paids as sfp', 'sfp.student_fees_id', '=', 'sf.id')
                    //     ->join('classes as c', 'c.id', '=', 'sf.class_id')
                    //     ->select(
                    //         'sf.early_date',
                    //         'sf.early_offer',
                    //         'sf.early_offer_amount',
                    //         'sf.id',
                    //         'sf.name',
                    //         'sf.due_date',
                    //         'sf.due_charges',
                    //         'sf.due_charges_amount',
                    //         'sf.created_at',
                    //         'c.name AS class'
                    //     )
                    //     ->where('sf.student_id', $student->id)
                    //     ->whereNull('sf.deleted_at')
                    //     ->where('sf.status', 'published')
                    //     ->where(function($query) {
                    //         $query->whereNull('sfp.is_fully_paid')
                    //             ->orWhere('sfp.status', '!=', 1);
                    //     })
                    //     ->orderBy('sf.created_at', 'DESC')
                    //     ->paginate(10);

                    // $query = DB::table('student_fees')
                    //     ->join('classes as c', 'c.id', '=', 'student_fees.class_id')
                    //     ->leftJoin('student_fees_paids as sfp', 'student_fees.id', '=', 'sfp.student_fees_id')
                    //     ->select(
                    //         'student_fees.early_date',
                    //         'student_fees.early_offer',
                    //         'student_fees.early_offer_amount',
                    //         'student_fees.id',
                    //         'student_fees.name',
                    //         'student_fees.due_date',
                    //         'student_fees.due_charges',
                    //         'student_fees.due_charges_amount',
                    //         'student_fees.created_at',
                    //         'c.name as class'
                    //     )
                    //     ->where('student_fees.student_id', $student->id)
                    //     ->whereNull('student_fees.deleted_at')
                    //     ->where('student_fees.status', 'published')
                    //     ->whereNull('sfp.student_fees_id') // Ensure student_fees.id does not exist in student_fees_paids.student_fees_id
                    //     ->orderBy('student_fees.created_at', 'DESC')
                    //     ->paginate(10);

                    $query = DB::table('student_fees as sf')
                    ->join('classes as c', 'c.id', '=', 'sf.class_id')
                    ->leftJoin('student_fees_paids as sfp', 'sf.id', '=', 'sfp.student_fees_id')
                    ->where('sf.student_id', $student->id)
                    ->whereNull('sf.deleted_at')
                    ->where('sf.status', 'published')
                    ->where(function ($query) {
                        $query->whereNull('sfp.student_fees_id')
                            ->orWhere(function ($subQuery) {
                                $subQuery->whereNotNull('sfp.student_fees_id')
                                        ->where('sfp.status', 2);
                            });
                    })
                    ->select(
                        'sf.early_date',
                        'sf.early_offer',
                        'sf.early_offer_amount',
                        'sf.id',
                        'sf.name',
                        'sf.due_date',
                        'sf.due_charges',
                        'sf.due_charges_amount',
                        'sf.created_at',
                        'c.name as class',
                        'sfp.payment_detail',
                        'sf.tax_type',
                        'sf.tax_percentage'
                    )
                    ->orderBy('sf.created_at', 'DESC')
                    ->paginate(10);
                
                }
            }
            
            $currentDate = date('Y-m-d');
            for($i = 0; $i < COUNT($query); $i++){
                $feesPaid = DB::select('SELECT * FROM student_fees_paids WHERE student_fees_id = ?', [$query[$i]->id]);
                $currentDateTime = new DateTime('now');
                $dueDate = new DateTime($query[$i]->due_date);
                $isDue = $dueDate < $currentDateTime;
                $query[$i]->total_due_charges = 0;
                
                $dueCharges = 0;
                
                
                $totalCompulsoryFees = 0;
               
                $queryInner = DB::select(
                    'SELECT fees_type_name AS name, fees_type_amount AS amount, quantity, discount, tax 
                    FROM student_fees_details 
                    WHERE student_fees_id = ? AND fees_type_name NOT IN (?, ?)', 
                    [$query[$i]->id, 'Overdue Fees', 'Early Discount']
                );
                
                for($j = 0; $j < COUNT($queryInner); $j++){
                    $discount = 0;
                    $tax = 0;
                    if($queryInner[$j]->discount > 0){
                        $discount = ($queryInner[$j]->amount * $queryInner[$j]->quantity) * $queryInner[$j]->discount / 100;
                        $queryInner[$j]->amount -= $discount;
                    }
                    if($queryInner[$j]->tax > 0){
                        $tax = ($queryInner[$j]->amount * $queryInner[$j]->quantity) * $queryInner[$j]->tax / 100;
                        $queryInner[$j]->amount += $tax;
                    }
                    $totalCompulsoryFees += ($queryInner[$j]->amount * $queryInner[$j]->quantity);
                }
                // Extra Tax added into feesDetail to show
                if(isset($query[$i]->tax_percentage) && $query[$i]->tax_percentage > 0){
                    $taxTypeJSON = json_decode(file_get_contents('assets/JSON/eInvoice/TaxTypes.json'),true);
                    $extraTax = 0;
                    $taxType = '';
                    foreach ($taxTypeJSON as $tax) {
                        if (isset($tax['Code']) && $tax['Code'] === $query[$i]->tax_type) {
                            $taxType = $tax['Description'];
                            break;
                        }
                    }
                    $extraTax = ($totalCompulsoryFees * ($query[$i]->tax_percentage / 100));
                    $totalCompulsoryFees += $extraTax;

                    $queryInner[] = (object)[
                        'name' => $taxType,
                        'amount' => round((float) $extraTax, 2),
                        'quantity' => 1,
                    ];
                }
                $query[$i]->total_fee_amount = $totalCompulsoryFees;
                if(COUNT($feesPaid) == 0 && $isDue){
                    if (!$query[$i]->due_charges) {
                        $dueCharges += $query[$i]->due_charges_amount;
                    } else {
                        $dueCharges += ($totalCompulsoryFees * ($query[$i]->due_charges / 100));
                    }
                
                    $query[$i]->total_due_charges = $dueCharges;
                }
                else if (COUNT($feesPaid) > 0 && $isDue){
                    if (!$query[$i]->due_charges) {
                        $dueCharges += $query[$i]->due_charges_amount;
                    } else {
                        $dueCharges += ($totalCompulsoryFees * ($query[$i]->due_charges / 100));
                    }
                    $query[$i]->total_due_charges = $dueCharges;
                }

                $early_date = $query[$i]->early_date;
                $early_date = date("Y-m-d",strtotime($early_date));
                
                $earlyFee = 0;
                $isEarly = false;
                if($currentDate <= $early_date && !$feesPaid){
                    if(!$query[$i]->early_offer){
                        $earlyFee = $query[$i]->early_offer_amount;
                    }else{
                        $earlyFee = ($totalCompulsoryFees * ($query[$i]->early_offer / 100));
                    }
                    $isEarly = true;
                }

                $query[$i]->is_paid = 0;
                if(COUNT($feesPaid) > 0){
                    if($feesPaid[0]->status == 1){
                        $query[$i]->is_paid = 1;
                    }
                }
                $query[$i]->paid_date = COUNT($feesPaid) > 0 ? $feesPaid[0]->date : '';
                $query[$i]->is_due = $isDue;
                $query[$i]->early_fee = $earlyFee;
                $query[$i]->is_early = $isEarly;
                $query[$i]->total_fee_amount = $totalCompulsoryFees;
                $query[$i]->total_final_amount = $totalCompulsoryFees + $dueCharges - $earlyFee;
                $query[$i]->fee_detail = $queryInner;
                if(COUNT($feesPaid) > 0){
                    $query[$i]->total_fee_amount = $feesPaid[0]->amount;
                    $query[$i]->total_final_amount = $feesPaid[0]->amount;
                    if($feesPaid[0]->mode == 4 && !empty($feesPaid[0]->payment_detail)){
                        $query[$i]->image = asset("storage/".$feesPaid[0]->payment_detail);
                    }
                }
                $query[$i]->student_id = $student->id;

                $eInvoiceValid = DB::table('student_fees_einvoice')->where('student_fees_id',$query[$i]->id)->where('status',1)->first();
                if(isset($eInvoiceValid)){
                    $query[$i]->e_invoice_valid = true;
                }
            }

             ResponseService::successResponse("Fees Fetched Successfully", $query);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    //Get Fees Credit Note Details
    public function getStudentsFeesCreditNoteByPage(Request $request) {
            $validator = Validator::make($request->all(), [
                'session_year_id' => 'nullable|numeric',
            ]);
        
            if ($validator->fails()) {
                return ResponseService::validationError($validator->errors()->first());
            }
        
            $student = DB::table('students')->where('user_id', Auth::user()->id)->first();
            if (!$student) {
                return ResponseService::errorResponse(
                    "Child's Account is not Active. Contact School Support", 
                    null, 
                    config('constants.RESPONSE_CODE.INACTIVE_CHILD')
                );
            }
        
            $currentSessionYear = $this->cache->getDefaultSessionYear(Auth::user()->school_id);
            $sessionYearId = $request->session_year_id ?? $currentSessionYear->id;
            // $classId = $student->class_section->class_id;
            // $schoolId = $student->user->school_id;
            $sessionYearId = $request->session_year_id ?? $currentSessionYear->id;
            try {
                $creditNotes = DB::table('credit_note AS cn')
                ->join('student_fees AS sf', 'cn.student_fee_id', '=', 'sf.id')
                ->join('classes AS c', 'c.id', '=', 'cn.class_id')
                ->select(
                    'cn.id AS note_id',
                    'cn.date AS note_date',
                    'cn.uid AS note_uid',
                    'sf.name AS fee_name',
                    'sf.created_at',
                    'c.name AS class',
                    'cn.tax_type',
                    'cn.tax_percentage',
                    DB::raw("'credit' AS note_type") // To differentiate
                )
                ->whereNull('cn.deleted_at')
                ->where('cn.status', 'published')
                ->where('cn.student_id', $student->id)
                ->orderByDesc('cn.created_at')
                ->get(); 
            
                // Get debit notes
                $debitNotes = DB::table('debit_note AS dn')
                    ->join('student_fees AS sf', 'dn.student_fee_id', '=', 'sf.id')
                    ->join('classes AS c', 'c.id', '=', 'dn.class_id')
                    ->select(
                        'dn.id AS note_id',
                        'dn.date AS note_date',
                        'dn.uid AS note_uid',
                        'sf.name AS fee_name',
                        'sf.created_at',
                        'c.name AS class',
                        'dn.tax_type',
                        'dn.tax_percentage',
                        DB::raw("'debit' AS note_type") // To differentiate
                    )
                    ->whereNull('dn.deleted_at')
                    ->where('dn.status', 'published')
                    ->where('dn.student_id', $student->id)
                    ->orderByDesc('dn.created_at')
                    ->get(); // Using get() instead of paginate() here for merging
    
                $refundNotes = DB::table('refund_note AS rn')
                ->join('student_fees AS sf', 'rn.student_fee_id', '=', 'sf.id')
                ->join('classes AS c', 'c.id', '=', 'rn.class_id')
                ->select(
                    'rn.id AS note_id',
                    'rn.date AS note_date',
                    'rn.uid AS note_uid',
                    'sf.name AS fee_name',
                    'sf.created_at',
                    'c.name AS class',
                    'rn.tax_type',
                    'rn.tax_percentage',
                    DB::raw("'refund' AS note_type") // To differentiate
                )
                ->whereNull('rn.deleted_at')
                ->where('rn.status', 'published')
                ->where('rn.student_id', $student->id)
                ->orderByDesc('rn.created_at')
                ->get(); // Using get() instead of paginate() here for merging
                
                // Merge the credit and debit notes
                $notes = $creditNotes->merge($debitNotes)->merge($refundNotes);
    
                    // Calculate total amounts for each note (credit or debit)
                    foreach ($notes as $note) {
                        // Determine the appropriate table for fetching note details
                        switch ($note->note_type) {
                            case 'credit':
                                $queryInner = DB::select('SELECT credit_note_name AS name, credit_note_amount AS amount, quantity,tax FROM credit_note_details WHERE credit_note_id = ?', [$note->note_id]);
                                break;
                            
                            case 'debit':
                                $queryInner = DB::select('SELECT debit_note_name AS name, debit_note_amount AS amount, quantity,tax FROM debit_note_details WHERE debit_note_id = ?', [$note->note_id]);
                                break;
                            
                            case 'refund':
                                $queryInner = DB::select('SELECT refund_note_name AS name, refund_note_amount AS amount, quantity,tax FROM refund_note_details WHERE refund_note_id = ?', [$note->note_id]);
                                break;
                            
                            default:
                                $queryInner = []; // Default to empty array if the note type is unknown
                                break;
                        }
                    
                        // Calculate the total amount for this note
                        $note->total_amount = 0;
                        foreach ($queryInner as $item) {
                            $note->total_amount += $item->amount * $item->quantity;
                            if($item->tax > 0){
                                $tax = ($item->amount * $item->quantity) * $item->tax / 100;
                                $note->total_amount += $tax;
                            }
                        }

                        if(isset($note->tax_percentage) && $note->tax_percentage > 0){
                            $taxTypeJSON = json_decode(file_get_contents('assets/JSON/eInvoice/TaxTypes.json'),true);
                            $extraTax = 0;
                            $taxType = '';
                            foreach ($taxTypeJSON as $tax) {
                                if (isset($tax['Code']) && $tax['Code'] === $note->tax_type) {
                                    $taxType = $tax['Description'];
                                    break;
                                }
                            }
                            $extraTax = ($note->total_amount * ($note->tax_percentage / 100));
                            $note->total_amount += $extraTax;
    
                            $queryInner[] = (object)[
                                'name' => $taxType,
                                'amount' => round((float) $extraTax, 2),
                                'quantity' => 1,
                            ];
                        }
                    
                        // Store the details of the note
                        $note->fee_detail = $queryInner;
                    }
    
                // Paginate the result after merging (using collection paginate)
                $paginatedNotes = new \Illuminate\Pagination\LengthAwarePaginator(
                    $notes->forPage($request->page ?? 1, 10), // Define current page and items per page
                    $notes->count(), // Total count of items
                    10, // Items per page
                    $request->page ?? 1, // Current page
                    ['path' => \Illuminate\Pagination\Paginator::resolveCurrentPath()] // Maintain the path
                );
    
                 ResponseService::successResponse("Fees Fetched Successfully", $paginatedNotes);
            }catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function payStudentsCompulsoryFees(Request $request) {
        $validator = Validator::make($request->all(), [
            'fees_id'           => 'required',
            'payment_method'    => 'required|in:Stripe,Fiuu',
        ]);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }

        
        if($request->payment_method == 'Fiuu'){
             ResponseService::successResponse("", [
                 "payment_intent" => array(
                     "id"=> "pi_3PnaJIB4WN9Jq10p1zZg7ryI",
                    "object"=> "payment_intent",
                    "amount"=> 20000,
                    "amount_capturable"=> 0,
                    "amount_details"=> array(
                        "tip"=> []
                    ),
                    "amount_received"=> 0,
                    "application"=> null,
                    "application_fee_amount"=> null,
                    "automatic_payment_methods"=> null,
                    "canceled_at"=> null,
                    "cancellation_reason"=> null,
                    "capture_method"=> "automatic",
                    "charges"=> array(
                        "object"=> "list",
                        "data"=> [],
                        "has_more"=> false,
                        "total_count"=> 0,
                        "url"=> "/v1/charges?payment_intent=pi_3PnaJIB4WN9Jq10p1zZg7ryI"
                    ),
                    "client_secret"=> "pi_3PnaJIB4WN9Jq10p1zZg7ryI_secret_TcmRY8Fn3m3e3d4fSxns4bB3W",
                    "confirmation_method"=> "automatic",
                    "created"=> 1723615432,
                    "currency"=> "myr",
                    "customer"=> null,
                    "description"=> null,
                    "invoice"=> null,
                    "last_payment_error"=> null,
                    "latest_charge"=> null,
                    "livemode"=> true,
                    "metadata"=> array(
                        "dueChargesAmount"=> "0",
                        "is_fully_paid"=> "1",
                        "parent_id"=> "8",
                        "payment_transaction_id"=> "146",
                        "school_id"=> "1",
                        "session_year_id"=> "9",
                        "student_fees_id"=> "470",
                        "student_id"=> "1",
                        "total_amount"=> "200",
                        "type"=> "fees"
                    ),
                    "next_action"=> null,
                    "on_behalf_of"=> null,
                    "payment_method"=> null,
                    "payment_method_configuration_details"=> null,
                    "payment_method_options"=> array(
                        "card"=> array(
                            "installments"=> null,
                            "mandate_options"=> null,
                            "network"=> null,
                            "request_three_d_secure"=> "automatic"
                        )
                    ),
                    "payment_method_types"=> [
                        "card"
                    ],
                    "processing"=> null,
                    "receipt_email"=> null,
                    "review"=> null,
                    "setup_future_usage"=> null,
                    "shipping"=> null,
                    "source"=> null,
                    "statement_descriptor"=> null,
                    "statement_descriptor_suffix"=> null,
                    "status"=> "requires_payment_method",
                    "transfer_data"=> null,
                    "transfer_group"=> null,
                    "payment_transaction_id"=> 146
                 ),
                 "payment_transaction" => 
                     array(
                         "id"=> 146,
                        "user_id"=> 8,
                        "amount"=> 200,
                        "payment_gateway"=> "Stripe",
                        "order_id"=> "pi_3PnaJIB4WN9Jq10p1zZg7ryI",
                        "payment_id"=> null,
                        "payment_signature"=> null,
                        "payment_status"=> "pending",
                        "school_id"=> 1,
                        "created_at"=> "2024-08-14T06:03:51.000000Z",
                        "updated_at"=> "2024-08-14T06:03:52.000000Z"
                     )
             ]);   
        }



        try {
            DB::beginTransaction();

            $parentId = Auth::user()->id;

            $studentData = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($studentData)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }
            $schoolId = $studentData->user->school_id;
            $paymentConfigurations = $this->paymentConfigurations->builder()->where(['status' => 1, 'payment_method' => $request->payment_method, 'school_id' => $schoolId])->first();

            if (empty($paymentConfigurations)) {
                ResponseService::errorResponse("Payment is not Enabled", [], config('constants.RESPONSE_CODE.ENABLE_PAYMENT_GATEWAY'));
            }


            $sessionYear = $this->cache->getDefaultSessionYear($schoolId);

            $fees = $this->studentFees->builder()
                ->where('id', $request->fees_id)
                ->with(['student_fees_paid','student_fees_details' => function ($query) {
                    $query->whereNotIn('fees_type_name', ['Overdue Fees', 'Early Discount']);
                    }
                ])
                ->firstOrFail();

            if (!empty($fees->student_fees_paid) && !empty($fees->student_fees_paid->is_fully_paid)) {
                ResponseService::errorResponse("Student Fees Already Paid", "", config('constants.RESPONSE_CODE.FEE_ALREADY_PAID'));
            }


            /* Full Payment */
            $dueChargesAmount = 0;
            $amount = $fees->student_fees_details->sum("fees_type_amount");

            if (new DateTime(date('Y-m-d')) > new DateTime($fees->due_date)) {
                $dueChargesAmount = $fees->due_charges_amount;
                $amount += $dueChargesAmount;
            }


            $finalAmount = $amount;
            //Add Payment Data to Payment Transactions Table
            $paymentTransactionData = $this->paymentTransaction->create([
                'user_id'         => $parentId,
                'amount'          => $finalAmount,
                'payment_gateway' => 'Stripe',
                'payment_status'  => 'Pending',
                'school_id'       => $schoolId,
                'order_id'        => null
            ]);

            $paymentIntent = PaymentService::create($request->payment_method, $schoolId)->createPaymentIntent(round($finalAmount, 2), [
                'student_fees_id'                => $request->fees_id,
                'student_id'             => $studentData->id,
                'parent_id'              => $parentId,
                'session_year_id'        => $sessionYear->id,
                'payment_transaction_id' => $paymentTransactionData->id,
                'total_amount'           => $finalAmount,
                'dueChargesAmount'       => $dueChargesAmount,
                'school_id'              => $schoolId,
                'type'                   => 'fees',
                'is_fully_paid'          => 1
            ]);
            $this->paymentTransaction->update($paymentTransactionData->id, ['order_id' => $paymentIntent->id, 'school_id' => $schoolId]);

            $paymentTransactionData = $this->paymentTransaction->findById($paymentTransactionData->id);
            // Custom Array to Show as response
            $paymentGatewayDetails = array(
                ...$paymentIntent->toArray(),
                'payment_transaction_id' => $paymentTransactionData->id,
            );


            DB::commit();
            ResponseService::successResponse("", ["payment_intent" => $paymentGatewayDetails, "payment_transaction" => $paymentTransactionData]);
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }



    public function studentsFeesPaidReceiptPDF(Request $request) {
        $validator = Validator::make($request->all(), [
            'fees_id'  => 'required|integer'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }

        if($request->cn_id != ''){
            try {
                $credit_note = DB::table('credit_note')->where('id',$request->cn_id)->first();
                $credit_note_details = DB::table('credit_note_details')->where('credit_note_id',$request->cn_id)->get();
                $student = DB::table('students as s')
                    ->join('users as u','u.id','=','s.user_id')
                    ->join('class_sections as cs','cs.id','=','s.class_section_id')
                    ->join('classes as c','c.id','=','cs.class_id')
                    ->where('s.id',$credit_note->student_id)
                    ->select(DB::raw("CONCAT_WS(' ', u.first_name, u.last_name) AS full_name"),'s.*','c.name as class_name','u.current_address')
                    ->first();
                $parent = DB::table('users')->where('id',$student->guardian_id)->first();
                $school = $this->cache->getSchoolSettings();
                $schoolSettings = DB::select("SELECT data FROM school_settings WHERE name = 'horizontal_logo' AND school_id = ".$student->school_id);
                $schoolLogo = '';
                if(COUNT($schoolSettings)){
                    $schoolLogo = $schoolSettings[0]->data;
                }
                $output = Pdf::loadView('student-fee-types.credit_note_invoice', compact('credit_note', 'credit_note_details', 'student', 'parent', 'school', 'schoolSettings', 'schoolLogo'))->output();
                $response = array(
                    'error' => false,
                    'pdf'   => base64_encode($output),
                );
            } catch (Throwable $e) {
                ResponseService::logErrorResponse($e);
                // Return more specific error information
                return response()->json([
                    'error' => true,
                    'message' => $e->getMessage(),
                    'code' => 103,
                    'details' => $e->getTraceAsString()
                ]);
            }
            return response()->json($response);
        }

        try {
            // Get the authenticated student user
            $student = Auth::user();
            
            // Get the student record
            $studentRecord = $this->student->builder()
                ->where('user_id', $student->id)
                ->first();
                
            if (empty($studentRecord)) {
                ResponseService::errorResponse("Student account is not active. Contact School Support");
            }

            $studentUser = DB::select("SELECT * FROM users WHERE id = ".$student->id);
            $studentDetail = $studentUser[0];
            $parent = DB::select("SELECT * FROM users WHERE id = ".$studentRecord->guardian_id);
            $parentDetail = $parent[0];
            $feesPaid = $this->studentFeesPaid->builder()
            ->with([
                'student_fees',
                'student_fees.class',
                'student_fees.student_fees_details'
            ])
            ->where('student_fees_id', $request->fees_id)
            ->first();
            $studentFeesDetail = DB::select(
                "SELECT * FROM student_fees_details WHERE student_fees_id = ? AND fees_type_name NOT IN ('Overdue Fees', 'Early Discount')",
                [$request->fees_id]
            );
            $e_invoice_school = DB::table('e_invoice')->where('school_id', $student->school_id)->first();
            $e_invoice_guardian = DB::table('e_invoice_guardian')->where('guardian_id', $studentRecord->guardian_id)->first();
            $dueCharges = 0;
            $earlyoffer = 0;
            $earlyy = 0;
            $due_fees = 0;

            $totalDiscount=[];
            $totalTax=[];
            $sumDiscount = 0;
            $sumTax = 0;
            $i=0;

            foreach($studentFeesDetail as $studentFeeDetail){

                // Initialize array elements if not set
                if (!isset($totalDiscount[$i])) {
                    $totalDiscount[$i] = 0;
                }
                if (!isset($totalTax[$i])) {
                    $totalTax[$i] = 0;
                }

                // calculate discount for each item
                if($studentFeeDetail->discount > 0){
                    $itemDiscount = $studentFeeDetail->fees_type_amount * $studentFeeDetail->discount / 100;
                    $totalDiscount[$i] += $itemDiscount * $studentFeeDetail->quantity;
                    $sumDiscount += $totalDiscount[$i];
                  
                }

                // calculate tax for each item
                if($studentFeeDetail->tax > 0){
                    $itemTax = $studentFeeDetail->fees_type_amount * $studentFeeDetail->tax / 100;
                    $totalTax[$i] += $itemTax * $studentFeeDetail->quantity;
                    $sumTax += $totalTax[$i];
                    
                }

                $i++;
            }


            $studentFees = DB::select("SELECT * FROM student_fees WHERE id = ".$request->fees_id);
            if ($studentFees) {
                $studentFees[0]->total_due_charges = 0;
                $dueFee = DB::select('SELECT due_date FROM student_fees WHERE id= ? AND due_date < ?',[$studentFees[0]->id, date('Y-m-d')]);
                $totalCompulsoryFees = DB::table('student_fees_details')
                        ->where('student_fees_id', $studentFees[0]->id)
                        ->whereNotIn('fees_type_name', ['Overdue Fees', 'Early Discount'])
                        ->sum(DB::raw('ROUND((fees_type_amount - (fees_type_amount * COALESCE(discount, 0) / 100) + (fees_type_amount * COALESCE(tax, 0) / 100)) * quantity)'));
                if(!isset($feesPaid) && $dueFee){
                    if (!$studentFees[0]->due_charges) {
                        $dueCharges += $studentFees[0]->due_charges_amount;
                    } else {
                        $dueCharges += ($totalCompulsoryFees * ($studentFees[0]->due_charges / 100));
                    }
                 
                    $studentFees[0]->total_due_charges = $dueCharges;
                    $due_fees = $dueCharges;
                }
                else if (isset($feesPaid) && $dueFee){
                    if ($feesPaid->date > $dueFee[0]->due_date){
                        if (!$studentFees[0]->due_charges) {
                            $dueCharges += $studentFees[0]->due_charges_amount;
                        } else {
                            $dueCharges += ($totalCompulsoryFees * ($studentFees[0]->due_charges / 100));
                        }
                        $studentFees[0]->total_due_charges = $dueCharges;
                        $due_fees = $dueCharges;
                    }   
                }
            }
            if($studentFees) {
                $studentFees[0]->total_discount_charges = 0;
                $student_early_discount = DB::table("student_fees_details")
                ->select("fees_type_amount")
                ->where("fees_type_name", "Early Discount")
                ->where("student_fees_id", $studentFees[0]->id) 
                ->first();
                $earlyFee = DB::select('SELECT early_date FROM student_fees WHERE id= ? AND early_date >= ?',[$studentFees[0]->id, date('Y-m-d')]); 
                $totalCompulsoryFees = DB::table('student_fees_details')
                        ->where('student_fees_id', $studentFees[0]->id)
                        ->whereNotIn('fees_type_name', ['Overdue Fees', 'Early Discount'])
                        ->sum(DB::raw('ROUND((fees_type_amount - (fees_type_amount * COALESCE(discount, 0) / 100) + (fees_type_amount * COALESCE(tax, 0) / 100)) * quantity, 2)'));
                if(!isset($feesPaid) && $earlyFee){
                    if (!$studentFees[0]->early_offer) {
                        $earlyoffer -= $studentFees[0]->early_offer_amount;
                    } else {
                        $earlyoffer -= ($totalCompulsoryFees * ($studentFees[0]->early_offer / 100));
                    }
                 
                    $studentFees[0]->total_discount_charges = $earlyoffer;
                    $earlyy = $earlyoffer;
                }
                else if (isset($feesPaid) && $earlyFee){
                    if ($feesPaid->date <= $earlyFee[0]->early_date){
                        if (!$studentFees[0]->early_offer) {
                            $earlyoffer -= $studentFees[0]->early_offer_amount;
                        } else {
                            $earlyoffer -= ($totalCompulsoryFees * ($studentFees[0]->early_offer / 100));
                        }
                        $studentFees[0]->total_discount_charges = $earlyoffer;
                        $earlyy = $earlyoffer;
                    }   
                }
            }

            $school = $this->cache->getSchoolSettings();
            $schoolSettings = DB::select("SELECT data FROM school_settings WHERE name = 'horizontal_logo' AND school_id = ".$student->school_id);
            $schoolLogo = '';
            if(COUNT($schoolSettings)){
                $schoolLogo = $schoolSettings[0]->data;
            }
            
            $dueQuery = DB::select('SELECT due_date FROM student_fees WHERE id= ?',[$studentFees[0]->id]);
            $dueDate = COUNT($dueQuery) ? $dueQuery[0]->due_date : '';
            $isPaid = null;
            if($request->is_paid && $feesPaid){
                if($request->is_paid == "true"){
                    $isPaid = true;
                } else {
                    $isPaid = false;
                }
            }

            $studentFeeEinvoice = DB::table('student_fees_einvoice')->where('student_fees_id',$studentFees[0]->id)->orderByDesc('id')->first();
            if(isset($studentFeeEinvoice) && $studentFeeEinvoice->status != 0){
                $documentSummary = json_decode($studentFeeEinvoice->document_summary);
                // dd($documentSummary);
                $url = env('E_INVOICE_URL').'/'.$studentFeeEinvoice->uuid.'/share/'.$documentSummary[0]->longId;
                // dd($url);
                $studentFeeEinvoice->e_invoice_url = (new QRCode)->render($url);
            }

            // $studentFeeEinvoice = DB::table('student_fees_einvoice')
            //     ->where('student_fees_id', $request->fees_id)
            //     ->first();
            $student = $studentRecord;
            $output = Pdf::loadView('student-fees.student_fees_receipt', compact('student','school', 'feesPaid', 'student', 'studentDetail', 'parentDetail', 'studentFeesDetail', 'studentFees', 'schoolLogo', 'due_fees','earlyy','dueDate','e_invoice_school','e_invoice_guardian','totalTax','totalDiscount','sumTax','sumDiscount','isPaid', 'studentFeeEinvoice'))->output();

            $response = array(
                'error' => false,
                'pdf'   => base64_encode($output),
            );
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            // Return more specific error information
            return response()->json([
                'error' => true,
                'message' => $e->getMessage(),
                'code' => 103,
                'details' => $e->getTraceAsString()
            ]);
        }
        return response()->json($response);
    }

    public function uploadFileFee(Request $request){
        $validator = Validator::make($request->all(), [
            'fee_id' => 'required'
        ]);
    
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
    
        $schoolId = Auth::user()->school_id;
        $res = DB::select("
            SELECT      sf.id AS student_fees_id,
                        s.id AS student_id,
                        s.school_id,
                        CONCAT(u.first_name , u.last_name) AS full_name,
                        s.guardian_id,
                        (
                           SELECT  SUM((fees_type_amount - (fees_type_amount * COALESCE(discount,0) / 100) + 
                            (fees_type_amount * COALESCE(tax,0) /100)) * quantity)
                            FROM    student_fees_details
                            WHERE   student_fees_id = sf.id
                            AND     fees_type_name NOT IN ('Early Discount', 'Overdue Fees')
                        ) AS total_compulsory_fees,
                        c.name AS class_name,
                        sfp.is_fully_paid,
                        sfp.mode,
                        sfp.cheque_no,
                        sfp.date
            FROM        users u
            JOIN        students s
            ON          u.id = s.user_id
            JOIN        class_sections cs
            ON          cs.id = s.class_section_id
            JOIN        classes c
            ON          c.id = cs.class_id
            JOIN        student_fees sf
            ON          sf.student_id = s.id
            AND         sf.status = 'published'
            LEFT JOIN   student_fees_paids sfp
            ON          sfp.student_fees_id = sf.id
            WHERE       sf.id = ?
            AND         sf.deleted_at IS NULL
            ORDER BY    sfp.is_fully_paid, sf.created_at desc
        ", [$request->fee_id]);
    
        if (empty($res)) {
            ResponseService::errorResponse("No data found");
        }
    
        $student = $res[0];
        $currentDate = date('Y-m-d');
    
        $due_date = DB::table('student_fees')->where('id', $student->student_fees_id)->value('due_date');
        $early_date = DB::table('student_fees')->where('id', $student->student_fees_id)->value('early_date');
    
        $due_date = date("Y-m-d", strtotime($due_date));
        $early_date = date("Y-m-d", strtotime($early_date));
    
        $total_compulsory_fees = floatval(str_replace(',', '', $student->total_compulsory_fees));
    
        if ($student->is_fully_paid == 0 && $currentDate > $due_date) {
            $student_overdue_fees = DB::table("student_fees_details")
                ->where("fees_type_name", "Overdue Fees")
                ->where("student_fees_id", $student->student_fees_id) 
                ->value("fees_type_amount");
    
            if ($student_overdue_fees) {
                $total_compulsory_fees += $student_overdue_fees;
                $student->due_charges = number_format($student_overdue_fees, 2);
                $student->total_compulsory_fees = $total_compulsory_fees;   
            }
        }
    
        if ($student->is_fully_paid == 0 && $currentDate <= $early_date && $currentDate < $due_date) {
            $student_early_discount = DB::table("student_fees_details")
                ->where("fees_type_name", "Early Discount")
                ->where("student_fees_id", $student->student_fees_id) 
                ->value("fees_type_amount");
    
            if ($student_early_discount) {
                $total_compulsory_fees += $student_early_discount;
                $student->early_offer = number_format($student_early_discount, 2);
                $student->total_compulsory_fees = $total_compulsory_fees;
            }
        }
    
        try {
            DB::beginTransaction();
    
            $feesPaid = $this->studentFeesPaid->builder()->where([
                'student_fees_id' => $request->fee_id,
                'school_id' => $schoolId
            ])->first();
    
            if (empty($feesPaid)) {
                if ($request->hasFile('file')) {
                    $file = $request->file('file');
                    $filePath = $file->store('bank_transfer', 'public');
                }
    
                $this->studentFeesPaid->create([
                    'date'                => date('Y-m-d'),
                    'school_id'           => $schoolId,
                    'is_fully_paid'       => 1,
                    'student_fees_id'     => $request->fee_id,
                    'mode'                => 4,
                    'amount'              => (double)number_format(str_replace(',', '', $student->total_compulsory_fees), 2, '.', ''),
                    'payment_detail'      => $filePath ?? null,
                    'due_charges'         => $student->due_charges ?? null,
                    'status'              => 2
                ]);
            } else {
                if ($request->hasFile('file')) {
                    if ($feesPaid->mode == '4' && !empty($feesPaid->payment_detail)) {
                        Storage::disk('public')->delete($feesPaid->payment_detail);
                    }
                    $file = $request->file('file');
                    $filePath = $file->store('bank_transfer', 'public');
                    DB::table('student_fees_paids')->where('id', $feesPaid->id)->update(['payment_detail' => $filePath]);
                }
            }
    
            $compulsoryFee = $student->total_compulsory_fees;
            $students = DB::table('students')
                ->join('users', 'students.user_id', '=', 'users.id')
                ->where('students.id', $student->student_id)
                ->whereNull('users.deleted_at')
                ->get(['students.user_id', 'students.guardian_id', 'users.first_name', 'users.last_name']);
    
            foreach ($students as $student) {
                $notifyUser = [$student->user_id];
    
                if ($student->guardian_id) {
                    $notifyUser[] = $student->guardian_id;
                }
    
                $title = 'Payment Successfully Processed';
                $body = 'Dear ' . $student->first_name . ' ' . $student->last_name . ', your payment of ' . 
                        number_format($compulsoryFee, 2) . ' has been successfully uploaded. It is now under review and approval.';
                $type = "Payment Notification";
    
                send_notification($notifyUser, $title, $body, $type);
    
                DB::table('admission_notification')->insert([
                    'user_id'   => $student->user_id,
                    'school_id' => Auth::user()->school_id, 
                    'date'      => now(), 
                    'status'    => 0, 
                    'type'      => 6
                ]);
            }
    
            DB::commit();
            ResponseService::successResponse("Data Updated Successfully");
        } catch (Throwable $e) {
            DB::rollback();
            ResponseService::logErrorResponse($e, 'StudentFeesController -> uploadFileFees method ');
            ResponseService::errorResponse();
        }
    }
    
    


    public function getLessons(Request $request) {
        $validator = Validator::make($request->all(), [
            'lesson_id'        => 'nullable|numeric',
            'class_subject_id' => 'required',
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $student = $request->user()->student;
            $lessonQuery = $this->lesson->builder()->where(['class_section_id' => $student->class_section_id, 'class_subject_id' => $request->class_subject_id])->with('topic', 'file');
            if ($request->lesson_id) {
                $lessonQuery->where('id', $request->lesson_id);
            }
            $lessonData = $lessonQuery->get();

            ResponseService::successResponse("Lessons Fetched Successfully", $lessonData);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getStudentProgress(Request $request) {
        $childId = Auth::user()->id;

        $student = DB::table('students')->where('user_id',$childId)->first();

        if (empty($student)) {
            ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
        }

        try {
            $query = DB::table('student_progress AS sp')
                ->join('students AS s', 's.id', '=', 'sp.student_id')
                ->join('users AS u', 'u.id', '=', 's.user_id')
                ->join('class_subjects AS cs', 'cs.id', '=', 'sp.class_subject_id')
                ->join('subjects AS sub', 'sub.id', '=', 'cs.subject_id')
                ->join('users AS t', 't.id', '=', 'sp.teacher_id') 
                ->select(
                    'sp.*',
                    'u.first_name',
                    'u.last_name',
                    'sp.class_subject_id',
                    's.id AS student_id',
                    'sub.name as subject_name',
                    'sub.type',
                    'sp.files',
                    't.first_name AS teacher_first_name',
                    't.last_name AS teacher_last_name'
                )
                ->whereNull('u.deleted_at')
                ->where('s.id', $student->id)
                ->whereIn('sp.status', ['approve', 'auto-approve']) 
                ->orderByDesc('created_at')->paginate(10);

            for($i = 0; $i < COUNT($query); $i++){
                $query[$i]->files = $query[$i]->files != '' ? 'https://schola.one/storage/'.$query[$i]->files: '';
            }

            $subjects = Subject::where('school_id', '=', $student->school_id)->get();

            ResponseService::successResponse("Attendance Fetched Successfully", $query);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getLessonTopics(Request $request) {
        $validator = Validator::make($request->all(), [
            'lesson_id' => 'required|numeric',
            'topic_id'  => 'nullable|numeric',
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $lessonTopicQuery = $this->lessonTopic->builder()->where('lesson_id', $request->lesson_id)->with('file');
            if ($request->topic_id) {
                $lessonTopicQuery->where('id', $request->topic_id);
            }
            $lessonTopicData = $lessonTopicQuery->get();
            ResponseService::successResponse("Topics Fetched Successfully", $lessonTopicData);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "StudentApiController :- getLessonTopics Method");
            ResponseService::errorResponse();
        }
    }

    public function getAssignments(Request $request) {
        $validator = Validator::make($request->all(), [
            'assignment_id'    => 'nullable|numeric',
            'class_subject_id' => 'nullable|numeric',
            'is_submitted'     => 'nullable|numeric',
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }

        try {
            $student = $request->user()->student;
            $data = $this->assignment->builder()->where('class_section_id', $student->class_section_id)->with(['file', 'class_subject.subject', 'submission' => function ($query) use ($student) {
                $query->where('student_id', $student->user_id)->with('file');
            }]);
            if ($request->assignment_id) {
                $data->where('id', $request->assignment_id);
            }
            if ($request->class_subject_id) {
                $data->where('class_subject_id', $request->class_subject_id);
            }
            if (isset($request->is_submitted)) {
                if ($request->is_submitted) {
                    $data->whereHas('submission', function ($q) use ($student) {
                        $q->where('student_id', $student->user_id);
                    });
                } else {
                    $data->whereDoesntHave('submission', function ($q) use ($student) {
                        $q->where('student_id', $student->user_id);
                    });
                }
            }
            $data = $data->orderBy('id', 'desc')->paginate(15);
            ResponseService::successResponse("Assignments Fetched Successfully", $data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "Student Api Controller -> getAssignment Method");
            ResponseService::errorResponse();
        }
    }

    public function submitAssignment(Request $request) {
        $validator = Validator::make($request->all(), [
            'assignment_id' => 'required|numeric',
            'subject_id'    => 'nullable|numeric',
            'files'         => 'required|array',
            'files.*'       => 'mimes:jpeg,png,jpg,gif,svg,webp,pdf,doc,docx,xml',
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }

        try {
            DB::beginTransaction();
            $assignmentSubmissionData = array();
            $student = $request->user()->student;
            $sessionYear = $this->cache->getDefaultSessionYear();

            $assignment = $this->assignment->builder()->where(['id' => $request->assignment_id, 'class_section_id' => $student->class_section_id])->first();
            $assignmentSubmissionQuery = $this->assignmentSubmission->builder()->where(['assignment_id' => $assignment->id, 'student_id' => $student->user_id])->first();
            if (empty($assignmentSubmissionQuery)) {
                $assignmentSubmissionData = array(
                    'assignment_id'   => $request->assignment_id,
                    'student_id'      => $student->user_id,
                    'session_year_id' => $sessionYear->id
                );
            } else if ($assignmentSubmissionQuery->status == 2 && $assignment->resubmission) {
                // if assignment submission is rejected and
                // Assignment has resubmission allowed then change the status to resubmitted
                $assignmentSubmissionData = array(
                    'id'     => $assignmentSubmissionQuery->id,
                    'status' => 3
                );
                // Check Old Files and Delete it
                if ($assignmentSubmissionQuery->file) {
                    foreach ($assignmentSubmissionQuery->file as $file) {
                        if (Storage::disk('public')->exists($file->getRawOriginal('file_url'))) {
                            Storage::disk('public')->delete($file->getRawOriginal('file_url'));
                        }
                    }
                }
                $assignmentSubmissionQuery->file()->delete();
            } else {
                ResponseService::errorResponse("You already have submitted your assignment.", null, config('constants.RESPONSE_CODE.ASSIGNMENT_ALREADY_SUBMITTED'));
            }
            $assignmentSubmission = $this->assignmentSubmission->updateOrCreate(['id' => $assignmentSubmissionData['id'] ?? null], $assignmentSubmissionData);

            //If File Exists
            if ($request->hasFile('files')) {
                $fileData = array(); // Empty FileData Array
                // Create A File Model Instance
                $assignmentSubmissionModelAssociate = $this->files->model()->modal()->associate($assignmentSubmission); // Get the Association Values of File with Assignment Submission
                foreach ($request->file('files') as $file_upload) {
                    // Create Temp File Data Array
                    $tempFileData = array(
                        'modal_type' => $assignmentSubmissionModelAssociate->modal_type,
                        'modal_id'   => $assignmentSubmissionModelAssociate->modal_id,
                        'file_name'  => $file_upload->getClientOriginalName(),
                        'type'       => 1,
                        'file_url'   => $file_upload
                    );
                    $fileData[] = $tempFileData; // Store Temp File Data in Multi-Dimensional File Data Array
                }
                $this->files->createBulk($fileData); // Store File Data
            }
            $submittedAssignment = $this->assignmentSubmission->builder()->where('id', $assignmentSubmission->id)->with('file')->get();
            DB::commit();
            ResponseService::successResponse("Assignments Submitted Successfully", $submittedAssignment);
        } catch (Throwable $e) {
            DB::rollback();
            ResponseService::logErrorResponse($e, "Student Api Controller -> submitAssignment Method");
            ResponseService::errorResponse();
        }
    }

    public function deleteAssignmentSubmission(Request $request) {
        $validator = Validator::make($request->all(), [
            'assignment_submission_id' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            DB::beginTransaction();
            $student = $request->user()->student;
            $assignment_submission = AssignmentSubmission::where('id', $request->assignment_submission_id)->where('student_id', $student->id)->with('file')->first();

            if (!empty($assignment_submission) && $assignment_submission->status == 0) {
                foreach ($assignment_submission->file as $file) {
                    if (Storage::disk('public')->exists($file->file_url)) {
                        Storage::disk('public')->delete($file->file_url);
                    }
                }
                $assignment_submission->file()->delete();
                $assignment_submission->delete();
                DB::commit();
                ResponseService::successResponse("Assignments Deleted Successfully");
            } else {
                ResponseService::errorResponse("You can not delete assignment");
            }
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Student Api Controller -> deleteAssignmentSubmission Method");
            ResponseService::errorResponse();
        }
    }

    public function getAttendance(Request $request) {
        $validator = Validator::make($request->all(), [
            'month' => 'nullable|numeric',
            'year'  => 'nullable|numeric',
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $student = $request->user()->student;
            $sessionYear = $this->cache->getDefaultSessionYear();

            $attendance = $this->attendance->builder()->where(['student_id' => $student->user_id, 'session_year_id' => $sessionYear->id]);
            $holidays = $this->holiday->builder();
            $session_year_data = $this->sessionYear->findById($sessionYear->id);
            if (isset($request->month)) {
                $attendance = $attendance->whereMonth('date', $request->month);
                $holidays = $holidays->whereMonth('date', $request->month);
            }

            if (isset($request->year)) {
                $attendance = $attendance->whereYear('date', $request->year);
                $holidays = $holidays->whereYear('date', $request->year);
            }
            $attendance = $attendance->get();
            $holidays = $holidays->get();


            $data = ['attendance' => $attendance, 'holidays' => $holidays, 'session_year' => $session_year_data];

            ResponseService::successResponse("Attendance Details Fetched Successfully", $data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "Student Api Controller -> getAttendance Method");
            ResponseService::errorResponse();
        }
    }

    public function getAnnouncements(Request $request) {
        $validator = Validator::make($request->all(), [
            'type'             => 'nullable|in:subject,noticeboard,class',
            'class_subject_id' => 'required_if:type,subject|numeric'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $student = $request->user()->student;
            $classSectionId = $student->class_section->id;
            $sessionYear = $this->cache->getDefaultSessionYear();
            if (isset($request->type) && $request->type == "subject") {
                // TODO : There might be some mistake in this code
                $table = $this->subjectTeacher->builder()->where(['class_section_id' => $student->class_section_id, 'class_subject_id' => $request->class_subject_id])->pluck('id');
                if ($table === null) {
                    ResponseService::errorResponse("Invalid Subject ID", null, config('constants.RESPONSE_CODE.INVALID_SUBJECT_ID'));
                }
            }

            $announcementData = $this->announcement->builder()->with('file', 'announcement_class')->where('session_year_id', $sessionYear->id);

            if (isset($request->type) && $request->type == "class") {
                $announcementData = $announcementData->whereHas('announcement_class', function ($query) use ($classSectionId) {
                    $query->where(['class_section_id' => $classSectionId, 'class_subject_id' => null]);
                });
            }

            if (isset($request->type) && $request->type == "subject") {
                $announcementData = $announcementData->whereHas('announcement_class', function ($query) use ($classSectionId, $request) {
                    $query->where(['class_section_id' => $classSectionId, 'class_subject_id' => $request->class_subject_id]);
                });
            }

            $announcementData = $announcementData->orderBy('id', 'desc')->paginate(15);
            ResponseService::successResponse("Announcement Details Fetched Successfully", $announcementData);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "StudentApiController :- getAnnouncements Method");
            ResponseService::errorResponse();
        }
    }

    public function getExamList(Request $request) {
        try {
            $studentId = Auth::user()->student->id;
            $student = $this->student->findById($studentId, ['*'], ['class_section']);
            $classId = $student->class_section->class_id;
            $currentSessionYear = $this->cache->getDefaultSessionYear();
            $exam = $this->exam->builder()
                ->where(['class_id' => $classId, 'session_year_id' => $currentSessionYear->id])
                ->whereHas('timetable', function ($query) {
                    $query->owner();
                })
                ->with([
                    'timetable' => function ($query) {
                        $query->owner()->selectRaw('* , SUM(total_marks) as total_marks')
                            ->groupBy('exam_id');
                    }
                ])->get();


            $exam_data = array();
            foreach ($exam as $data) {
                if (isset($request->status) && $request->status != $data->exam_status && $request->status != 3) {
                    continue;
                }

                $exam_data[] = [
                    'id'                 => $data->id,
                    'name'               => $data->name,
                    'description'        => $data->description,
                    'publish'            => $data->publish,
                    'session_year'       => $data->session_year->name,
                    'exam_starting_date' => $data->start_date,
                    'exam_ending_date'   => $data->end_date,
                    'exam_status'        => $data->exam_status,
                ];
            }


            ResponseService::successResponse("", $exam_data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "StudentApiController :- getExamList Method");
            ResponseService::errorResponse();
        }
    }

    public function getBookingSlot(Request $request){
        // $childId = Auth::user()->id;
        $student = DB::table('students')->where('user_id')->first();
    
        if (empty($student)) {
            ResponseService::errorResponse(
                "Child's Account is not Active. Contact School Support", 
                NULL, 
                config('constants.RESPONSE_CODE.INACTIVE_CHILD')
            );
        }
    
        $sessionYear = $this->cache->getDefaultSessionYear($student->school_id);
        $allSlot = DB::table("booking_slot")
            ->where("teacher_id", $request['teacher_id'])
            ->where("subject_id", $request["subject_id"])
            ->where("status", "approve")
            ->get();
    
        // Calculate capacity for booked slots
        $bookedSlotCounts = DB::table('booking')
            ->whereIn('slot_id', $allSlot->pluck('id')->toArray())
            ->select('slot_id', DB::raw('COUNT(*) as count'))
            ->groupBy('slot_id')
            ->pluck('count', 'slot_id')
            ->toArray();
    
        $canceledSlotIds = DB::table('booking')
            ->whereIn('slot_id', $allSlot->pluck('id')->toArray())
            ->whereNotNull('deleted_at')  
            ->pluck('slot_id')
            ->toArray();
    
        $allSlot = $allSlot->map(function ($slot) use ($bookedSlotCounts, $canceledSlotIds) {
            // Reduce capacity by the number of bookings for this slot
            if (isset($bookedSlotCounts[$slot->id])) {
                $slot->capacity -= $bookedSlotCounts[$slot->id];
            }
    
            $slot->is_canceled = in_array($slot->id, $canceledSlotIds);
    
            return $slot;
        });
    
        // Fetch slots booked by the student
        $bookedSlot = DB::table("booking_slot")
            ->where("teacher_id", $request['teacher_id'])
            ->where("subject_id", $request["subject_id"])
            ->whereIn('id', function($query) use ($request) {
                $query->select('slot_id') 
                    ->from('booking')
                    ->where('student_id', $request['child_id'])
                    ->where('teacher_id', $request['teacher_id'])
                    ->where('subject_id', $request['subject_id']);
            })
            ->get();
    
        $isCanceled = DB::table('booking')
            ->where('student_id', $request['child_id'])
            ->whereIn('slot_id', $bookedSlot->pluck('id')->toArray())  
            ->whereNotNull('deleted_at') 
            ->pluck('slot_id')
            ->toArray();
    
        $bookedSlot = $bookedSlot->map(function ($slot) use ($isCanceled) {
            $slot->is_canceled = in_array($slot->id, $isCanceled);
            return $slot;
        });
    
        $data = [
            'allSlot' => $allSlot,
            'sessionYear' => $sessionYear,
            'bookedSlot' => $bookedSlot
        ];
    
        ResponseService::successResponse("Booking Slot Fetched Successfully", $data);
    }
    

    public function bookSlot(Request $request)
    {
        try {
            DB::beginTransaction();

            // Get the authenticated user's ID (no need for child_id anymore)
            $userId = Auth::user()->id;

            // Fetch the student record associated with the authenticated user
            $student = DB::table('students')->where('user_id', $userId)->first();


            // Get school ID from student record
            $schoolId = Auth::user()->school_id;

            // Get slot_id from the request
            $slot_id = $request->slot_id;

            // Ensure slot_id exists before proceeding
            if (!$slot_id) {
                return ResponseService::errorResponse(
                    "Slot ID is required to book a slot."
                );
            }           

            // Insert the booking into the 'booking' table
            DB::table('booking')->insert([
                'slot_id' => $slot_id,
                'student_id' => $student->id, // Use the student's ID directly
                'school_id' => $schoolId,
            ]);

            // Insert an admission notification for the student
            $data = [
                'user_id' => $userId, // Use the authenticated user's ID
                'school_id' => $schoolId,
                'date' => now(),
                'status' => 0,
                'type' => 2,
            ];
            DB::table('admission_notification')->insert($data);

            // Commit the transaction
            DB::commit();

            return ResponseService::successResponse("Slot booked successfully.");
        } catch (\Exception $e) {
            // Rollback in case of an error
            DB::rollBack();
            return ResponseService::errorResponse(
                "An error occurred while booking the slot.", 
                $e->getMessage(), 
                500
            );
        }
    }

    public function getExamDetails(Request $request) {
        $validator = Validator::make($request->all(), [
            'exam_id' => 'required|nullable'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $studentData = Auth::user()->student;
            $classId = $studentData->class_section->class_id;
            $examData = $this->exam->builder()
                ->where([
                    'id'       => $request->exam_id,
                    'class_id' => $classId
                ])
                ->with([
                    'timetable' => function ($query) {
                        $query->owner()->with(['class_subject.subject'])->orderby('date');
                    }
                ])->first();


            if (!$examData) {
                ResponseService::successResponse("", []);
            }


            foreach ($examData->timetable as $data) {
                $exam_data[] = array(
                    'exam_timetable_id' => $data->id,
                    'total_marks'       => $data->total_marks,
                    'passing_marks'     => $data->passing_marks,
                    'date'              => $data->date,
                    'starting_time'     => $data->start_time,
                    'ending_time'       => $data->end_time,
                    'subject'           => array(
                        'id'               => $data->class_subject->subject->id,
                        'class_subject_id' => $data->class_subject_id,
                        'name'             => $data->class_subject->subject->name,
                        'bg_color'         => $data->class_subject->subject->bg_color,
                        'image'            => $data->class_subject->subject->image,
                        'type'             => $data->class_subject->subject->type,
                    )
                );
            }
            ResponseService::successResponse("", $exam_data ?? []);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "StudentApiController :- getExamDetails Method");
            ResponseService::errorResponse();
        }
    }

    public function getExamMarks() {
        try {
            $studentData = Auth::user()->student->load('class_section.class:id,name', 'class_section.section:id,name', 'class_section.medium:id,name');

            // Exam Result Data
            $examResultDB = $this->examResult->builder()->with([
                'user'       => function ($q) {
                    $q->select('id', 'first_name', 'last_name')->with('student:id,user_id,roll_number');
                },
                'exam.timetable:id,exam_id,start_time,end_time',
                'session_year',
                'exam.marks' => function ($q) use ($studentData) {
                    $q->where('student_id', $studentData->user_id);
                }
            ])->where('student_id', $studentData->user_id)->get();


            // Check that Exam Result DB is not empty
            if (count($examResultDB)) {
                foreach ($examResultDB as $examResultData) {
                    $exam_result = array(
                        'result_id'      => $examResultData->id,
                        'exam_id'        => $examResultData->exam_id,
                        'exam_name'      => $examResultData->exam->name,
                        'class_name'     => $studentData->class_section->full_name,
                        'student_name'   => $examResultData->user->full_name,
                        'exam_date'      => $examResultData->exam->start_date,
                        'total_marks'    => $examResultData->total_marks,
                        'obtained_marks' => $examResultData->obtained_marks,
                        'percentage'     => $examResultData->percentage,
                        'grade'          => $examResultData->grade,
                        'session_year'   => $examResultData->session_year->name,
                    );
                    $exam_marks = array();
                    foreach ($examResultData->exam->marks as $marks) {
                        $exam_marks[] = array(
                            'marks_id'       => $marks->id,
                            'subject_name'   => $marks->class_subject->subject->name,
                            'subject_type'   => $marks->class_subject->subject->type,
                            'total_marks'    => $marks->timetable->total_marks,
                            'passing_marks'    => $marks->timetable->passing_marks,
                            'obtained_marks' => $marks->obtained_marks,
                            'teacher_review' => $marks->teacher_review,
                            'grade'          => $marks->grade,
                        );
                    }
                    $data[] = array(
                        'result'     => $exam_result,
                        'exam_marks' => $exam_marks,
                    );
                }
                ResponseService::successResponse("Exam Result Fetched Successfully", $data ?? null);
            } else {
                ResponseService::successResponse("Exam Result Fetched Successfully", []);
            }
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getProfileDetails() {
        try {
            $studentData = Auth::user()->load(['student' => function ($query) {
                $query->with(['class_section' => function ($query) {
                    $query->with('section', 'class', 'medium', 'class.shift', 'class.stream');
                }], 'guardian');
            }, 'extra_student_details.form_field', 'school']);

            $data = array(
                'id'                => $studentData->id,
                'first_name'        => $studentData->first_name,
                'last_name'         => $studentData->last_name,
                'mobile'            => $studentData->mobile,
                'roll_number'       => $studentData->student->roll_number,
                'admission_no'      => $studentData->student->admission_no,
                'admission_date'    => $studentData->student->admission_date,
                'gender'            => $studentData->gender,
                'image'             => $studentData->image,
                'dob'               => $studentData->dob,
                'current_address'   => $studentData->current_address,
                'permanent_address' => $studentData->permanent_address,
                'occupation'        => $studentData->occupation,
                'status'            => $studentData->status,
                'fcm_id'            => $studentData->fcm_id,
                'school_id'         => $studentData->school_id,
                'session_year_id'   => $studentData->student->session_year_id,
                'email_verified_at' => $studentData->email_verified_at,
                'created_at'        => $studentData->created_at,
                'updated_at'        => $studentData->updated_at,
                'class_section'     => $studentData->student->class_section,
                'guardian'          => $studentData->student->guardian,
                'extra_details'     => $studentData->extra_student_details,
                'school'            => $studentData->school,
            );

            ResponseService::successResponse('Data Fetched Successfully', $data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }

    }

    public function getSessionYear() {
        try {
            $sessionYear = $this->cache->getDefaultSessionYear();
            ResponseService::successResponse("Session Year Fetched Successfully", $sessionYear);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getOnlineExamList(Request $request) {
        $validator = Validator::make($request->all(), [
            'class_subject_id' => 'nullable|numeric'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $student = Auth::user()->student;
            $classSectionId = $student->class_section->id;
            $sessionYear = $this->cache->getDefaultSessionYear();

            if (env('DEMO_MODE')) {
                $check_student_status = $this->studentOnlineExamStatus->builder()->where('student_id', $student->user_id);
                if ($check_student_status->count()) {
                    $status_id = $check_student_status->pluck('id');
                    $this->studentOnlineExamStatus->builder()->whereIn('id', $status_id)->delete();
                }

                $check_student_answers = $this->onlineExamStudentAnswer->builder()->where('student_id', $student->user_id);
                if ($check_student_answers->count()) {
                    $status_id = $check_student_answers->pluck('id');
                    $this->onlineExamStudentAnswer->builder()->whereIn('id', $status_id)->delete();
                }
            }

            $onlineExamData = $this->onlineExam->builder()
                ->where(['class_section_id' => $classSectionId, 'session_year_id' => $sessionYear->id])
                ->where('end_date', '>=', now())
                ->has('question_choice')
                ->with('class_subject', 'question_choice:id,online_exam_id,marks')
                ->whereDoesntHave('student_attempt', function ($q) use ($student) {
                    $q->where('student_id', $student->user_id);
                })
                ->when($request->class_subject_id, function ($query, $classSubjectId) {
                    return $query->where('class_subject_id', $classSubjectId);
                })
                ->orderby('start_date')
                ->paginate(15);

            ResponseService::successResponse('Data Fetched Successfully', $onlineExamData);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getOnlineExamQuestions(Request $request) {
        $validator = Validator::make($request->all(), [
            'exam_id'  => 'required',
            'exam_key' => 'required',
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $student = Auth::user()->student;
            // Checks Student Exam Status
            if ($this->studentOnlineExamStatus->builder()
                ->where(['online_exam_id' => $request->exam_id, 'student_id' => $student->user_id])
                ->exists()) {
                ResponseService::errorResponse('Student already attempted exam', null, config('constants.RESPONSE_CODE.STUDENT_ALREADY_ATTEMPTED_EXAM'));
            }

            $onlineExam = $this->onlineExam->builder();

            // Checks Exam Key
            if (!$onlineExam->where(['id' => $request->exam_id, 'exam_key' => $request->exam_key])->exists()) {
                ResponseService::errorResponse("Invalid Exam Key");
            }


            // Checks that Exam Started or Not
            if ($onlineExam->where('id', $request->exam_id)->whereDate('start_date', '>', now())->exists()) {
                ResponseService::successResponse('Exam not started yet');
            }

            // Add Student Status Entry
            $this->studentOnlineExamStatus->create([
                'student_id'     => $student->user_id,
                'online_exam_id' => $request->exam_id,
                'status'         => 1,
            ]);

            $onlineExamQuestionChoice = $this->onlineExamQuestionChoice->builder();

            // Get Total Questions
            $totalQuestions = $onlineExamQuestionChoice->where('online_exam_id', $request->exam_id)->count();

            // Get Questions Data
            $examQuestionData = $onlineExamQuestionChoice->where('online_exam_id', $request->exam_id)->with('questions')->get();
            $totalMarks = 0;
            $questionData = [];
            foreach ($examQuestionData as $examQuestion) {
                $totalMarks += $examQuestion->marks;

                // Make Options Array
                $optionData = $examQuestion->questions->options->map(function ($optionsData) {
                    return [
                        'id'        => $optionsData->id,
                        'option'    => htmlspecialchars_decode($optionsData->option),
                        'is_answer' => $optionsData->is_answer == 1 ? 1 : 0
                    ];
                });


                // Make Question Array Data
                $questionData[] = [
                    'id'       => $examQuestion->id,
                    'question' => htmlspecialchars_decode($examQuestion->questions->question),
                    'options'  => $optionData,
                    'marks'    => $examQuestion->marks,
                    'image'    => $examQuestion->questions->image_url,
                    'note'     => $examQuestion->questions->note,
                ];
            }
            ResponseService::successResponse('Data Fetched Successfully', $questionData, [
                'total_questions' => $totalQuestions, 'total_marks' => $totalMarks
            ]);

        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function submitOnlineExamAnswers(Request $request) {
        $validator = Validator::make($request->all(), [
            'online_exam_id' => 'required|numeric',
            'answers_data'   => 'nullable|array',
            //            'answers_data.*.question_id'    => 'required|numeric',
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $student = Auth::user()->student;

            DB::beginTransaction();
            // Check Online Exam Exists or not
//            $onlineExamData = $this->onlineExam->findById($request->online_exam_id);
//            if (!$onlineExamData) {
//                ResponseService::errorResponse('Invalid online exam id');
//            }

            // Check if answers already submitted
            if ($this->onlineExamStudentAnswer->builder()->where(['student_id' => $student->id, 'online_exam_id' => $request->online_exam_id])->exists()) {
                ResponseService::errorResponse('Answers already submitted');
            }
            $answers = [];
            foreach ($request->answers_data ?? [] as $answerData) {

                // checks the question exists with provided exam id
                $questionChoice = $this->onlineExamQuestionChoice->findById($answerData['question_id']);
                if (!$questionChoice || $questionChoice->online_exam_id != $request->online_exam_id) {
                    ResponseService::errorResponse('Invalid question id');
                }

                foreach ($answerData['option_id'] as $optionId) {
                    // checks the option exists with provided question
//                    if (!$this->onlineExamQuestionOption->findById($optionId)->exists()) {
//                        ResponseService::errorResponse('Invalid option id');
//                    }

                    // add the data of answers
                    $answers[] = [
                        'student_id'     => $student->user_id,
                        'online_exam_id' => $request->online_exam_id,
                        'question_id'    => $answerData['question_id'],
                        'option_id'      => $optionId,
                        'submitted_date' => now()->toDateString(),
                    ];
                }
            }
            if (count($answers) > 0) {
                $this->onlineExamStudentAnswer->createBulk($answers);
            }
            // Update student exam status
            $this->studentOnlineExamStatus->updateOrCreate(
                ['student_id' => $student->user_id, 'online_exam_id' => $request->online_exam_id],
                ['status' => 2]
            );
            DB::commit();
            ResponseService::successResponse('Data Stored Successfully');

        } catch (Throwable $e) {
            DB::rollback();
            ResponseService::logErrorResponse($e, "StudentApiController submitOnlineExamAnswers Method");
            ResponseService::errorResponse();
        }
    }

    public function getOnlineExamResultList(Request $request) {
        $validator = Validator::make($request->all(), [
            'class_subject_id' => 'nullable|numeric'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $student = Auth::user()->student;
            $classSectionId = $student->class_section_id;
            $sessionYear = $this->cache->getDefaultSessionYear();

            // Get Online Exam Data Where Logged in Student have attempted data and Relation Data with Question Choice , Student's answer with user submitted question with question and its option
            $onlineExamData = $this->onlineExam->builder()
                ->when($request->class_subject_id, function ($query) use ($request) {
                    $query->where('class_subject_id', $request->class_subject_id);
                })
                ->where(['class_section_id' => $classSectionId, 'session_year_id' => $sessionYear->id])
                ->whereHas('student_attempt', function ($q) use ($student) {
                    $q->where('student_id', $student->user_id);
                })
                ->with('question_choice:id,online_exam_id,marks', 'student_answers.user_submitted_questions.questions:id', 'student_answers.user_submitted_questions.questions.options:id,question_id,is_answer', 'class_subject.subject:id,name,type,code,bg_color,image')
                ->paginate(15)->toArray();

            $examListData = array(); // Initialized Empty examListData Array

            // Loop through Exam data
            foreach ($onlineExamData['data'] as $data) {

                // Get Total Marks of Particular Exam
                $totalMarks = collect($data['question_choice'])->sum('marks');

                // Initialized totalObtainedMarks with 0
                $totalObtainedMarks = 0;

                // Group Student's Answers by question_id
                $grouped_answers = [];
                foreach ($data['student_answers'] as $student_answer) {
                    $examSubmittedDate = date('Y-m-d', strtotime($student_answer['submitted_date']));
                    $grouped_answers[$student_answer['question_id']][] = $student_answer;
                }

                // Loop through Student's Grouped answers
                foreach ($grouped_answers as $student_answers) {

                    // Filter the options whose is_answer values is 1
                    $correct_option_ids = array_filter($student_answers[0]['user_submitted_questions']['questions']['options'], static function ($option) {
                        return $option['is_answer'] == 1;
                    });

                    // Get All Correct Options
                    $correct_option_ids = array_column($correct_option_ids, 'id');

                    // Get Student's Correct Options
                    $student_option_ids = array_column($student_answers, 'option_id');

                    // Check if the student's answers exactly match the correct answers then add marks with totalObtainedMarks
                    if (!array_diff($correct_option_ids, $student_option_ids) && !array_diff($student_option_ids, $correct_option_ids)) {
                        $totalObtainedMarks += $student_answers[0]['user_submitted_questions']['marks'];
                    }
                }

                // Make Exam List Data
                $examListData[] = array(
                    'online_exam_id'      => $data['id'],
                    'subject'             => array(
                        'id'   => $data['class_subject']['subject']['id'],
                        'name' => $data['class_subject']['subject']['name'] . ' - ' . $data['class_subject']['subject']['type'],
                    ),
                    'title'               => $data['title'],
                    'obtained_marks'      => $totalObtainedMarks,
                    'total_marks'         => $totalMarks ?? "0",
                    'exam_submitted_date' => $examSubmittedDate ?? date('Y-m-d', strtotime($data['end_date']))
                );
            }

            $examList = array(
                'current_page' => $onlineExamData['current_page'],
                'data'         => $examListData,
                'from'         => $onlineExamData['from'],
                'last_page'    => $onlineExamData['last_page'],
                'per_page'     => $onlineExamData['per_page'],
                'to'           => $onlineExamData['to'],
                'total'        => $onlineExamData['total'],
            );

            ResponseService::successResponse("", $examList);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "StudentApiController :- getOnlineExamList Method");
            ResponseService::errorResponse();
        }
    }

    public function getOnlineExamResult(Request $request) {
        $validator = Validator::make($request->all(), [
            'online_exam_id' => 'required|numeric'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $student = Auth::user()->student;

            // Online Exam Data
            $onlineExam = $this->onlineExam->builder()
                ->where('id', $request->online_exam_id)
                ->whereHas('student_attempt', function ($q) use ($student) {
                    $q->where('student_id', $student->user_id);
                })
                ->with([
                    'question_choice:id,online_exam_id,marks',
                    'student_answers.user_submitted_questions.questions:id',
                    'student_answers.user_submitted_questions.questions.options:id,question_id,is_answer',
                ])
                ->first();
            if (isset($onlineExam) && $onlineExam != null) {

                //Get Total Question Count and Total Marks
                $totalQuestions = $onlineExam->question_choice->count();
                $totalMarks = $onlineExam->question_choice->sum('marks');

                // Group Student's Answers by question_id
                $grouped_answers = [];
                foreach ($onlineExam->student_answers as $student_answer) {
                    $grouped_answers[$student_answer['question_id']][] = $student_answer->toArray();
                }

                // Initialized the variables
                $correctQuestionData = array();
                $correctQuestions = 0;
                $totalObtainedMarks = "0";

                // Loop through Student's Grouped answers
                foreach ($grouped_answers as $student_answers) {

                    // Filter the options whose is_answer values is 1
                    $correct_option_ids = array_filter($student_answers[0]['user_submitted_questions']['questions']['options'], static function ($option) {
                        return $option['is_answer'] == 1;
                    });

                    // Get All Correct Options
                    $correct_option_ids = array_column($correct_option_ids, 'id');

                    // Get Student's Correct Options
                    $student_option_ids = array_column($student_answers, 'option_id');

                    // Check if the student's answers exactly match the correct answers then add marks with totalObtainedMarks
                    if (!array_diff($correct_option_ids, $student_option_ids) && !array_diff($student_option_ids, $correct_option_ids)) {

                        // Sum Question marks with ObtainedMarks
                        $totalObtainedMarks += $student_answers[0]['user_submitted_questions']['marks'];

                        // Get Correct Questions Ids
                        $correctQuestionIds[] = $student_answers[0]['user_submitted_questions']['id'];

                        // Increment Correct Question by 1
                        ++$correctQuestions;

                        // Correct Question Data
                        $correctQuestionData[] = array(
                            'question_id' => $student_answers[0]['user_submitted_questions']['id'],
                            'marks'       => $student_answers[0]['user_submitted_questions']['marks']
                        );
                    }
                }


                // Check correctQuestionIds exists and not empty
                if (!empty($correctQuestionIds)) {
                    // Get Incorrect Questions Excluding Correct answer using correctQuestionIds
                    $incorrectQuestionsData = $onlineExam->question_choice->whereNotIn('id', $correctQuestionIds);
                } else {
                    // Get All Question Choice as incorrectQuestionsData
                    $incorrectQuestionsData = $onlineExam->question_choice;
                }

                // Total Incorrect Questions
                $incorrectQuestions = $incorrectQuestionsData->count();

                // Incorrect Question Data
                $inCorrectQuestionData = array();
                foreach ($incorrectQuestionsData as $incorrectData) {
                    $inCorrectQuestionData[] = array(
                        'question_id' => $incorrectData->id,
                        'marks'       => $incorrectData->marks
                    );
                }

                // Final Array Data
                $onlineExamResult = array(
                    'total_questions'      => $totalQuestions,
                    'correct_answers'      => array(
                        'total_questions' => $correctQuestions,
                        'question_data'   => $correctQuestionData
                    ),
                    'in_correct_answers'   => array(
                        'total_questions' => $incorrectQuestions,
                        'question_data'   => $inCorrectQuestionData
                    ),
                    'total_obtained_marks' => $totalObtainedMarks,
                    'total_marks'          => $totalMarks ?? '0'
                );
                ResponseService::successResponse("", $onlineExamResult);
            } else {
                ResponseService::successResponse("", []);
            }
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "StudentApiController getOnlineExamResult Method");
            ResponseService::errorResponse();
        }
    }

    public function getOnlineExamReport(Request $request) {
        $validator = Validator::make($request->all(), [
            'class_subject_id' => 'nullable|numeric'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $student = Auth::user()->student;
            $sessionYear = $this->cache->getDefaultSessionYear();

            $onlineExams = $this->onlineExam->builder()
                ->has('question_choice')
                ->where(['class_section_id' => $student->class_section_id, 'class_subject_id' => $request->class_subject_id, 'session_year_id' => $sessionYear->id])
                ->whereHas('student_attempt', function ($q) use ($student) {
                    $q->where('student_id', $student->user_id);
                })
                ->with([
                    'question_choice:id,online_exam_id,marks',
                    'student_answers.user_submitted_questions.questions:id',
                    'student_answers.user_submitted_questions.questions.options:id,question_id,is_answer',
                    'class_subject.subject:id,name,type,code,bg_color,image'
                ])
                ->paginate(10);

            if ($onlineExams->count() > 0) {
                $totalExamIds = $onlineExams->pluck('id')->toArray();
                $totalExamsAttempted = $this->user->builder()->role('Student')->where('id', $student->user_id)->has('online_exam_attempts')->count();

                $examList = array();
                foreach ($onlineExams->toArray()['data'] as $onlineExam) {
                    $totalMarks = collect($onlineExam['question_choice'])->sum('marks');

                    // Initialized totalObtainedMarks with 0
                    $totalObtainedMarks = "0";

                    // Group Student's Answers by question_id
                    $grouped_answers = [];
                    foreach ($onlineExam['student_answers'] as $student_answer) {
                        $grouped_answers[$student_answer['question_id']][] = $student_answer;
                    }

                    // Loop through Student's Grouped answers
                    foreach ($grouped_answers as $student_answers) {

                        // Filter the options whose is_answer values is 1
                        $correct_option_ids = array_filter($student_answers[0]['user_submitted_questions']['questions']['options'], static function ($option) {
                            return $option['is_answer'] == 1;
                        });

                        // Get All Correct Options
                        $correct_option_ids = array_column($correct_option_ids, 'id');

                        // Get Student's Correct Options
                        $student_option_ids = array_column($student_answers, 'option_id');

                        // Check if the student's answers exactly match the correct answers then add marks with totalObtainedMarks
                        if (!array_diff($correct_option_ids, $student_option_ids) && !array_diff($student_option_ids, $correct_option_ids)) {
                            $totalObtainedMarks += $student_answers[0]['user_submitted_questions']['marks'];
                        }
                    }

                    // Add exam to the list
                    $examList[] = [
                        'online_exam_id' => $onlineExam['id'],
                        'title'          => $onlineExam['title'],
                        'obtained_marks' => (string)$totalObtainedMarks,
                        'total_marks'    => (string)$totalMarks,
                    ];
                }


                // Calculate Percentage
                if ($totalMarks > 0) {
                    // Avoid division by zero error
                    $percentage = number_format(($totalObtainedMarks * 100) / max($totalMarks, 1), 2);
                } else {
                    // If total marks is zero, then percentage is also zero
                    $percentage = 0;
                }

                // Build the final data array
                $onlineExamReportData = array(
                    'total_exams'          => count($totalExamIds),
                    'attempted'            => $totalExamsAttempted,
                    'missed_exams'         => count($totalExamIds) - $totalExamsAttempted,
                    'total_marks'          => (string)$totalMarks,
                    'total_obtained_marks' => (string)$totalObtainedMarks,
                    'percentage'           => (string)$percentage,
                    'exam_list'            => [
                        'current_page' => (string)$onlineExams->currentPage(),
                        'data'         => array_values($examList),
                        'from'         => (string)$onlineExams->firstItem(),
                        'last_page'    => (string)$onlineExams->lastPage(),
                        'per_page'     => (string)$onlineExams->perPage(),
                        'to'           => (string)$onlineExams->lastItem(),
                        'total'        => (string)$onlineExams->total(),
                    ],
                );
            } else {
                $onlineExamReportData = [];
            }


            // Return the response
            ResponseService::successResponse("", $onlineExamReportData);

        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getAssignmentReport(Request $request) {
        $validator = Validator::make($request->all(), [
            'class_subject_id' => 'nullable|numeric'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $student = Auth::user()->student;
            $sessionYear = $this->cache->getDefaultSessionYear();

            // Assignment Data
            $assignments = $this->assignment->builder()
                ->where(['class_section_id' => $student->class_section_id, 'session_year_id' => $sessionYear->id, 'class_subject_id' => $request->class_subject_id])->whereNotNull('points')
                ->get();

            // Get the assignment submissions
            $submitted_assignment_ids = $this->assignmentSubmission->builder()->where('student_id', $student->user_id)->whereIn('assignment_id', $assignments->pluck('id'))->pluck('assignment_id');

            // Calculate various statistics
            $total_assignments = $assignments->count();
            $total_submitted_assignments = $submitted_assignment_ids->count();
            $total_assignment_submitted_points = $assignments->sum('points');
            $total_points_obtained = $this->assignmentSubmission->builder()->whereIn('assignment_id', $submitted_assignment_ids)->sum('points');

            // Calculate the percentage
            $percentage = $total_assignment_submitted_points ? number_format(($total_points_obtained * 100) / $total_assignment_submitted_points, 2) : 0;

            // Get the submitted assignment data with points (using pagination manually)
            $perPage = 15;
            $currentPage = $request->input('page', 1);
            $offset = ($currentPage - 1) * $perPage;
            $submitted_assignment_data_with_points = $assignments->filter(function ($assignment) use ($submitted_assignment_ids) {
                return $assignment->points !== null && $submitted_assignment_ids->contains($assignment->id);
            })->slice($offset, $perPage)->map(function ($assignment) {
                return [
                    'assignment_id'   => $assignment->id,
                    'assignment_name' => $assignment->name,
                    'obtained_points' => optional($assignment->submission)->points ?? 0,
                    'total_points'    => $assignment->points
                ];
            });

            $assignment_report = [
                'assignments'                           => $total_assignments,
                'submitted_assignments'                 => $total_submitted_assignments,
                'unsubmitted_assignments'               => $total_assignments - $total_submitted_assignments,
                'total_points'                          => $total_assignment_submitted_points,
                'total_obtained_points'                 => $total_points_obtained,
                'percentage'                            => $percentage,
                'submitted_assignment_with_points_data' => [
                    'current_page' => $currentPage,
                    'data'         => array_values($submitted_assignment_data_with_points->toArray()),
                    'from'         => $offset + 1,
                    'to'           => $offset + $submitted_assignment_data_with_points->count(),
                    'per_page'     => $perPage,
                    'total'        => $total_submitted_assignments,
                    'last_page'    => ceil($total_submitted_assignments / $perPage),
                ],
            ];


            ResponseService::successResponse("Data Fetched Successfully", $assignment_report);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }

    }

    public function getSchoolSettings() {
        try {
            $settings = $this->cache->getSchoolSettings();
            $sessionYear = $this->cache->getDefaultSessionYear();
            $semester = $this->cache->getDefaultSemesterData();
            $features = FeaturesService::getFeatures();
            $specialSchoolIds = [11,51,57,58,59,60,61,62,63,64];
            if (in_array(Auth::user()->school_id, $specialSchoolIds)) {
                unset($features[27]); // Unset the key 29: Booking Management
                if($child->user->school_id == 11){
                    unset($features[16]);
                }
                // "features": {
                //     "1": "Student Management",
                //     "2": "Academics Management",
                //     "3": "Slider Management",
                //     "4": "Teacher Management",
                //     "5": "Session Year Management",
                //     "6": "Holiday Management",
                //     "7": "Timetable Management",
                //     "8": "Attendance Management",
                //     "9": "Exam Management",
                //     "10": "Lesson Management",
                //     "11": "Assignment Management",
                //     "12": "Announcement Management",
                //     "13": "Staff Management",
                //     "14": "Expense Management",
                //     "15": "Staff Leave Management",
                //     "16": "Fees Management",
                //     "17": "School Gallery Management",
                //     "18": "ID Card - Certificate Generation",
                //     "19": "Website Management",
                //     "20": "Student Progress",
                //     "22": "Reporting",
                //     "23": "Credit Management",
                //     "24": "Commission Management",
                //     "25": "Package Management",
                //     "26": "Reward Management",
                //     "27": "Chat System",
                //     "28": "Statement Management",
                //     "29": "Booking Management"
                // },
            }
            $paymentGateways = $this->paymentConfigurations->builder()->select(['id', 'payment_method', 'api_key', 'currency_code', 'verify_key', 'merchant_id', 'secret_key', 'app_name', 'channel'])->where('status', 1)->get();
            for($i = 0; $i < COUNT($paymentGateways); $i++){
                $paymentGateways[$i]->dev_mode = false;
                $paymentGateways[$i]->username = 'RMSxdk_2022';
                $paymentGateways[$i]->password = 'RMSpwd@2022';
                //$paymentGateways[$i]->merchant_id = 'leapseed_Dev';
                //$paymentGateways[$i]->app_name = 'leapseed_Dev';
                //$paymentGateways[$i]->app_name = 'Learnn';
                //$paymentGateways[$i]->app_name = 'schola';
                $paymentGateways[$i]->currency = 'MYR';
                $paymentGateways[$i]->country = 'MY';
                //$paymentGateways[$i]->verify_key = '6218787ae63e5fc166a138fed185bc22';
            }
            $data = [
                'school_id'    => Auth::user()->school_id,
                'session_year' => $sessionYear,
                'semester'     => $semester,
                'settings'     => $settings,
                'features'     => (count($features) > 0) ? $features : (object)[],
                'payment_gateway' => $paymentGateways
            ];
            ResponseService::successResponse('Settings Fetched Successfully.', $data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getSliders() {
        try {
            $studentData = Auth::user();
            $data = $this->sliders->builder()->where('school_id', $studentData->school_id)->whereIn('type',[1,3])->orderByDesc('created_at')->get();
            ResponseService::successResponse("Sliders Fetched Successfully", $data);
        } catch (Throwable $e) {
            ResponseService::successResponse("Sliders Fetched Successfully", []);
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getScanAttendance(Request $request) {        
        try {
            $student = $request->user()->student;
            $attendances = SubjectAttendance::where('user_id', '=', $student->user_id)
            ->orderByDesc('created_at')->paginate(10);
            for($i = 0; $i < COUNT($attendances); $i++){
                $attendances[$i]->remark_picture = $attendances[$i]->remark_picture != '' ? 'https://schola.one/storage/'.$attendances[$i]->remark_picture: '';
            }

            $subjects = Subject::where('school_id', '=', $student->school_id)->get();

            ResponseService::successResponse("Attendance Fetched Successfully", ['attendance' => $attendances, 'subjects' => $subjects]);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getStudentCreditDetails(Request $request)
    {

        // $studentDetails = DB::table('students as s')
        //     ->where('s.id', $request->child_id)
        //     ->first();

        // if (!$studentDetails) {
        //     ResponseService::errorResponse("Student not found.");
        // }

        $creditDetails = DB::table('credit_system as c')
            ->where('c.user_id', AUth::user()->id)
            ->select(
                'c.credit_amount',
                'c.balance',
                'c.detail',
            )
            ->get();

        $recentCredit = DB::table('credit_system as c')
            ->where('c.user_id', AUth::user()->id)
            ->orderBy('c.updated_at', 'desc')
            ->select(
                'c.credit_amount',
                'c.balance',
            )
            ->first();

        $data = ['creditDetails' => $creditDetails, 'recentCredit' => $recentCredit];

        ResponseService::successResponse("Document Retrieved Successfully", $data);
    }

    public function getStudentDocument(Request $request)
    {
        $query = DB::table('documents')
            ->join('document_classes', 'documents.id', '=', 'document_classes.document_id')
            ->join('files', function ($join) {
                $join->on('documents.id', '=', 'files.modal_id')
                    ->where('files.modal_type', '=', 'App\Models\Document');
            });

        $student = DB::table('students')->where('user_id',Auth::user()->id)->whereNUll('deleted_at')->first();
        $query->where('document_classes.student_id', '=', $student->id);

        $documents = $query->select(
            'documents.title',
            'documents.description',
            'files.type',
            'files.file_name',
            'files.file_url'
        )->get();

        foreach ($documents as $item) {
            $item->file_url = asset("storage/" . $item->file_url);
        }

        ResponseService::successResponse("Documents Retrieved Successfully", $documents);
    }


    public function getSubjectTeachers(Request $request){
        $validator = Validator::make($request->all(), [
            'subject_id' => 'required',
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try{

            $children = $request->user()->student;

            /// Fetch Teachers based on subject and child id
            $class_subject_id = $children->selectedStudentSubjects()->pluck('class_subject_id');
            $subjectTeachers = DB::table('subject_teachers')
            ->join('users','users.id','=','subject_teachers.teacher_id')
            ->select('subject_teachers.id', 'subject_teachers.subject_id', 
            'subject_teachers.teacher_id', 'subject_teachers.school_id','users.first_name',
            'users.last_name','users.image','users.mobile')
            ->whereIn('subject_teachers.class_subject_id', $class_subject_id)
            ->where('subject_teachers.class_section_id', $children['class_section_id'])
            ->where('subject_teachers.subject_id', $request->subject_id)           
            ->get();
            ResponseService::successResponse("Teacher Details Fetched Successfully", $subjectTeachers);
        }catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }
    public function getStudentBookingSlot(Request $request){

        // if ($validator->fails()) {
        //     ResponseService::validationError($validator->errors()->first());
        // }

        $sessionYear = $this->cache->getDefaultSessionYear(Auth::user()->school_id);
        $student=DB::table('students')->where('user_id',Auth::user()->id)->whereNUll('deleted_at')->first();

        $allSlot = DB::table("booking_slot")
            ->where("teacher_id", $request['teacher_id'])
            ->where("subject_id", $request["subject_id"])
            ->where("status", "approve")
            ->get();

        /// All Student Booked Slot Calculate Capacity
        $bookedSlotCounts = DB::table('booking')
        ->whereIn('slot_id', $allSlot->pluck('id')->toArray())
        ->select('slot_id', DB::raw('COUNT(*) as count'))
        ->groupBy('slot_id')
        ->pluck('count', 'slot_id')
        ->toArray();

        $canceledSlotIds = DB::table('booking')
        ->whereIn('slot_id', $allSlot->pluck('id')->toArray())
        ->whereNotNull('deleted_at')  
        ->pluck('slot_id')
        ->toArray();

        $allSlot = $allSlot->map(function ($slot) use ($bookedSlotCounts, $canceledSlotIds) {
            // Reduce capacity by the number of bookings for this slot
            if (isset($bookedSlotCounts[$slot->id])) {
                $slot->capacity -= $bookedSlotCounts[$slot->id];
            }
        
            $slot->is_canceled = in_array($slot->id, $canceledSlotIds);
        
            return $slot;
        });
            
        $allSlot->each(function ($slot) {
            if (isset($slot->booking_category_id)) {
                $categoryTitle = DB::table('booking_category')
                    ->select('title')
                    ->where('id', $slot->booking_category_id)
                    ->first();
        
                $slot->title = $categoryTitle->title ?? null;
            }
        });
        
        /// Student Booked Slot
        $bookedSlot = DB::table("booking_slot")
            ->where("teacher_id", $request['teacher_id'])
            ->where("subject_id", $request["subject_id"])
            ->whereIn('id', function($query) use ($request,$student) {
                $query->select('slot_id') // Assuming slot_id is the relevant field in the booking table
                    ->from('booking')
                    ->where('student_id', $student->id)
                    ->where('teacher_id', $request['teacher_id'])
                    ->where('subject_id', $request['subject_id']);
            })
            ->get();


            
            $isCanceled = DB::table('booking')
            ->where('student_id',$student->id)
            ->whereIn('slot_id', $bookedSlot->pluck('id')->toArray())  
            ->whereNotNull('deleted_at') 
            ->pluck('slot_id')
            ->toArray();

            $bookedSlot = $bookedSlot->map(function ($slot) use ($isCanceled) {
                // Set is_canceled to true if the slot_id is in the $isCanceled array
                $slot->is_canceled = in_array($slot->id, $isCanceled);
                return $slot;
            });


        $data = ['allSlot'=>$allSlot,'sessionYear'=>$sessionYear,'bookedSlot' => $bookedSlot];
        
        ResponseService::successResponse("Booking Slot Fetched Successfully", $data);

    }

    public function StudentBookSlot(Request $request){
        $validator = Validator::make($request->all(), [
            'slot_id' => 'required',
        ]);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }

        try{
            DB::beginTransaction();
            
            // Get the authenticated student user
            $student = DB::table('students')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->first();
            
            if (empty($student)) {
                ResponseService::errorResponse(
                    "Student account is not active. Contact School Support", 
                    NULL, 
                    config('constants.RESPONSE_CODE.INACTIVE_CHILD')
                );
            }
            
            $schoolId = Auth::user()->school_id;
            $slot_id = $request->slot_id;

            // Insert booking record
            DB::table('booking')->insert([
                'slot_id'     => $slot_id,
                'student_id'  => $student->id,
                'school_id'   => $schoolId,
            ]);

            // Add notification
            $data = [
                'user_id'   => Auth::user()->id,
                'school_id' => $schoolId, 
                'date'      => now(), 
                'status'    => 0, 
                'type'      => 2,
            ];
            DB::table('admission_notification')->insert($data);
                    
            DB::commit();
            return ResponseService::successResponse("Slot booked successfully.");
        } catch (\Exception $e) {
            DB::rollBack();
            return ResponseService::errorResponse("An error occurred while booking the slot.", $e->getMessage(), 500);
        }
    }

}
