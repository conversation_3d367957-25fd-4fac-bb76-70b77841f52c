<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use App\Models\User;
use App\Models\School;
use App\Models\Students;
use App\Models\Attendance;
use App\Models\SessionYear;
use Illuminate\Console\Command;
use App\Services\CachingService;
use App\Models\SubjectAttendance;
use Illuminate\Support\Facades\Log;
use App\Models\AttendanceAutoAbsent;

class AutoMarkAbsentCommand extends Command
{
    private CachingService $cache;
    public function __construct(CachingService $cache)
    {
        parent::__construct();
        $this->cache = $cache;
    }
    
    protected $signature = 'auto:mark-absent';
    protected $description = 'Automatically mark students as absent based on school settings';

    public function handle()
    {
        $today = Carbon::today();
        $now = Carbon::now();

        // Get all schools with auto_mark_absent enabled
        $allSchools = School::all();
        $schools = [];
        foreach ($allSchools as $school) {
            $settings = $this->cache->getSchoolSettings(['*'], $school->id);
        
            if (isset($settings['auto_mark_absent']) && $settings['auto_mark_absent'] == 1) {
                $schools[] = $school;
            }
        }

        foreach ($schools as $school) {
            // Get auto_mark_absent_time setting
            $schoolSettings = $this->cache->getSchoolSettings(['*'], $school->id);
            $sessionYear = SessionYear::where('school_id', $school->id)->where('default', 1)->first();

            if (!isset($schoolSettings['auto_mark_absent_time'])) continue;

            $markTime = Carbon::parse($schoolSettings['auto_mark_absent_time']);

            // Check if current time is >= auto_mark_absent_time
            if ($now->lt($markTime)) continue;

            // // Check if already processed today
            $existingLog = AttendanceAutoAbsent::where('school_id', $school->id)
                ->whereDate('date', $today)
                ->first();

            if ($existingLog) continue;

            // // Get students without attendance for today
            $students = Students::where('school_id', $school->id)
                ->where('session_year_id', $sessionYear->id)
                ->whereHas('user', function ($query) {
                    $query->whereNull('deleted_at');
                })
                ->get();

            $studentIds = $students->pluck('user_id');

            $attendanceCount = Attendance::whereIn('student_id', $studentIds)
                ->where('type','!=','0')
                ->whereDate('date', $today)
                ->count();

            if ($attendanceCount === 0) {
                // No attendance at all — likely a holiday or no class day
                return;
            }

            foreach ($students as $student) {
                $hasAttendanceToday = Attendance::where('student_id', $student->user_id)
                    ->whereDate('date', $today)
                    ->exists();
            
                if ($hasAttendanceToday) {
                    continue; // already marked
                }
                
                $this->addAbsentAttendance($student,$sessionYear->id,$school->id);
            
                $this->sendNotification($student);
            }

            // // Log the action
            AttendanceAutoAbsent::create([
                'date' => $today,
                'marked_at' => $now,
                'school_id' => $school->id,
            ]);
        }

        $this->info('Auto mark absent process completed');
    }

    protected function sendNotification($student)
    {
        $today = Carbon::today();
        $notifyUser = array();
        $notifyUser[] = $student->guardian_id;
        $notifyUser[] = $student->user_id;
        $userData = User::find($student->user_id);
        $userName = $userData->first_name . " " . $userData->last_name;
        $title = 'Absent';
        $type = 'Attendance';
        $body = $userName . ' is absent on ' . $today->format('d-m-Y');
        send_notification($notifyUser, $title, $body, $type);
    }

    protected function addAbsentAttendance(Students $student,int $sessionYearId, int $schoolId): bool
    {
        try {
            $today = Carbon::today();

            // Check if either attendance or subject_attendance already exists for today
            $attendanceExists = Attendance::where('student_id', $student->user_id)
            ->whereDate('date', $today)
            ->exists();

            $subjectAttendanceExists = SubjectAttendance::where('user_id', $student->user_id)
                ->whereDate('date', $today)
                ->exists();

            if ($attendanceExists || $subjectAttendanceExists) {
                // Record(s) already exist, skip to avoid duplicates
                return false;
            }
            
            // // Create attendance record
            Attendance::create([
                'student_id' => $student->user_id,
                'class_section_id' => $student->class_section_id,
                'date' => $today,
                'session_year_id' => $sessionYearId,
                'type' => 0,
                'school_id' => $schoolId,
            ]);

            // // Update subject_attendances table
            SubjectAttendance::create(
                [
                    'user_id' => $student->user_id,
                    'subject_id' => null,
                    'date' => $today,
                    'status' => 0,
                    'school_id' => $schoolId,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            );

            return true;
        } catch (\Exception $e) {
            Log::error('Error adding attendance: ' . $e->getMessage());
            return false;
        }
    }
}
