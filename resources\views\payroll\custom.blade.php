@extends('layouts.master')

@section('title')
    {{ __('Custom Payroll') }}
@endsection

@section('content')
<style>
    .contribution-card {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        background: #f8f9fa;
    }
    .contribution-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #e0e0e0;
    }
    .contribution-item:last-child {
        border-bottom: none;
    }
    .contribution-label {
        font-weight: 500;
        color: #333;
    }
    .contribution-amount {
        font-weight: 600;
        color: #007bff;
        font-size: 1.1em;
    }
    .teacher-info {
        background: #fff;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .info-row {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
    }
    .info-row:last-child {
        border-bottom: none;
    }
    .info-label {
        font-weight: 500;
        color: #666;
    }
    .info-value {
        color: #333;
    }
    .section-title {
        font-size: 1.2em;
        font-weight: 600;
        color: #333;
        margin-bottom: 15px;
        border-bottom: 2px solid #007bff;
        padding-bottom: 5px;
    }
</style>

<div class="content-wrapper">
    <div class="page-header">
        <h3 class="page-title">
            {{ __('Custom Payroll - Teacher Contributions') }}
        </h3>
    </div>

    <div class="row">
        <div class="col-md-12 grid-margin stretch-card">
            <div class="card">
                <div class="card-body">
                    <h4 class="card-title">{{ __('Select Teacher') }}</h4>

                    <div class="form-group">
                        <label for="teacher_select">{{ __('Choose Teacher') }} <span class="text-danger">*</span></label>
                        <select class="form-control" id="teacher_select" name="teacher_id">
                            <option value="">{{ __('Select a teacher...') }}</option>
                            @foreach($teachers as $teacher)
                                <option value="{{ $teacher->user_id }}">
                                    {{ $teacher->user->full_name }} - {{ $teacher->position ?? 'Teacher' }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div id="loading" style="display: none;" class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <p class="mt-2">{{ __('Loading teacher information...') }}</p>
                    </div>

                    <div id="teacher-details" style="display: none;">
                        <!-- Teacher Information Section -->
                        <div class="teacher-info">
                            <div class="section-title">{{ __('Teacher Information') }}</div>
                            <div class="info-row">
                                <span class="info-label">{{ __('Name') }}:</span>
                                <span class="info-value" id="teacher-name">-</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">{{ __('Position') }}:</span>
                                <span class="info-value" id="teacher-position">-</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">{{ __('IC Number') }}:</span>
                                <span class="info-value" id="teacher-ic">-</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">{{ __('EPF Number') }}:</span>
                                <span class="info-value" id="teacher-epf-no">-</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">{{ __('SOCSO Number') }}:</span>
                                <span class="info-value" id="teacher-socso-no">-</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">{{ __('Basic Salary') }}:</span>
                                <span class="info-value" id="teacher-salary">-</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">{{ __('Age') }}:</span>
                                <span class="info-value" id="teacher-age">-</span>
                            </div>
                        </div>

                        <!-- Contributions Section -->
                        <div class="row">
                            <!-- EPF Contributions -->
                            <div class="col-md-4">
                                <div class="contribution-card">
                                    <div class="section-title">{{ __('EPF Contributions') }}</div>
                                    <div class="contribution-item">
                                        <span class="contribution-label">{{ __('Employee (11%)') }}:</span>
                                        <span class="contribution-amount" id="employee-epf">RM 0.00</span>
                                    </div>
                                    <div class="contribution-item">
                                        <span class="contribution-label">{{ __('Employer (13%)') }}:</span>
                                        <span class="contribution-amount" id="employer-epf">RM 0.00</span>
                                    </div>
                                    <div class="contribution-item">
                                        <span class="contribution-label"><strong>{{ __('Total EPF') }}:</strong></span>
                                        <span class="contribution-amount" id="total-epf"><strong>RM 0.00</strong></span>
                                    </div>
                                </div>
                            </div>

                            <!-- SOCSO Contributions -->
                            <div class="col-md-4">
                                <div class="contribution-card">
                                    <div class="section-title">{{ __('SOCSO Contributions') }}</div>
                                    <div class="contribution-item">
                                        <span class="contribution-label">{{ __('Employee') }}:</span>
                                        <span class="contribution-amount" id="employee-socso">RM 0.00</span>
                                    </div>
                                    <div class="contribution-item">
                                        <span class="contribution-label">{{ __('Employer') }}:</span>
                                        <span class="contribution-amount" id="employer-socso">RM 0.00</span>
                                    </div>
                                    <div class="contribution-item">
                                        <span class="contribution-label"><strong>{{ __('Total SOCSO') }}:</strong></span>
                                        <span class="contribution-amount" id="total-socso"><strong>RM 0.00</strong></span>
                                    </div>
                                </div>
                            </div>

                            <!-- EIS Contributions -->
                            <div class="col-md-4">
                                <div class="contribution-card">
                                    <div class="section-title">{{ __('EIS Contributions') }}</div>
                                    <div class="contribution-item">
                                        <span class="contribution-label">{{ __('Employee') }}:</span>
                                        <span class="contribution-amount" id="employee-eis">RM 0.00</span>
                                    </div>
                                    <div class="contribution-item">
                                        <span class="contribution-label">{{ __('Employer') }}:</span>
                                        <span class="contribution-amount" id="employer-eis">RM 0.00</span>
                                    </div>
                                    <div class="contribution-item">
                                        <span class="contribution-label"><strong>{{ __('Total EIS') }}:</strong></span>
                                        <span class="contribution-amount" id="total-eis"><strong>RM 0.00</strong></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Summary Section -->
                        <div class="contribution-card" style="background: #e8f4fd;">
                            <div class="section-title" style="color: #0056b3;">{{ __('Total Contributions Summary') }}</div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="contribution-item">
                                        <span class="contribution-label">{{ __('Total Employee Contributions') }}:</span>
                                        <span class="contribution-amount" id="total-employee" style="color: #dc3545;"><strong>RM 0.00</strong></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="contribution-item">
                                        <span class="contribution-label">{{ __('Total Employer Contributions') }}:</span>
                                        <span class="contribution-amount" id="total-employer" style="color: #28a745;"><strong>RM 0.00</strong></span>
                                    </div>
                                </div>
                            </div>
                            <div class="contribution-item" style="border-top: 2px solid #007bff; margin-top: 10px; padding-top: 15px;">
                                <span class="contribution-label" style="font-size: 1.2em;"><strong>{{ __('Grand Total') }}:</strong></span>
                                <span class="contribution-amount" id="grand-total" style="color: #0056b3; font-size: 1.3em;"><strong>RM 0.00</strong></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('script')
<script>
$(document).ready(function() {
    $('#teacher_select').change(function() {
        var teacherId = $(this).val();

        if (teacherId) {
            // Show loading
            $('#loading').show();
            $('#teacher-details').hide();

            // Make AJAX request
            $.ajax({
                url: '{{ route("customPayroll.show") }}',
                type: 'GET',
                data: {
                    teacher_id: teacherId
                },
                success: function(response) {
                    $('#loading').hide();

                    if (response.success) {
                        // Update teacher information
                        $('#teacher-name').text(response.teacher.name || '-');
                        $('#teacher-position').text(response.teacher.position || '-');
                        $('#teacher-ic').text(response.teacher.ic_no || '-');
                        $('#teacher-epf-no').text(response.teacher.epf_no || '-');
                        $('#teacher-socso-no').text(response.teacher.socso_no || '-');
                        $('#teacher-salary').text('RM ' + parseFloat(response.contributions.basic_salary || 0).toFixed(2));
                        $('#teacher-age').text(response.contributions.age + ' years');

                        // Update EPF contributions
                        var employeeEpf = parseFloat(response.contributions.employee_epf || 0);
                        var employerEpf = parseFloat(response.contributions.employer_epf || 0);
                        var totalEpf = employeeEpf + employerEpf;

                        $('#employee-epf').text('RM ' + employeeEpf.toFixed(2));
                        $('#employer-epf').text('RM ' + employerEpf.toFixed(2));
                        $('#total-epf').text('RM ' + totalEpf.toFixed(2));

                        // Update SOCSO contributions
                        var employeeSocso = parseFloat(response.contributions.employee_socso || 0);
                        var employerSocso = parseFloat(response.contributions.employer_socso || 0);
                        var totalSocso = employeeSocso + employerSocso;

                        $('#employee-socso').text('RM ' + employeeSocso.toFixed(2));
                        $('#employer-socso').text('RM ' + employerSocso.toFixed(2));
                        $('#total-socso').text('RM ' + totalSocso.toFixed(2));

                        // Update EIS contributions
                        var employeeEis = parseFloat(response.contributions.employee_eis || 0);
                        var employerEis = parseFloat(response.contributions.employer_eis || 0);
                        var totalEis = employeeEis + employerEis;

                        $('#employee-eis').text('RM ' + employeeEis.toFixed(2));
                        $('#employer-eis').text('RM ' + employerEis.toFixed(2));
                        $('#total-eis').text('RM ' + totalEis.toFixed(2));

                        // Update summary totals
                        var totalEmployee = employeeEpf + employeeSocso + employeeEis;
                        var totalEmployer = employerEpf + employerSocso + employerEis;
                        var grandTotal = totalEmployee + totalEmployer;

                        $('#total-employee').text('RM ' + totalEmployee.toFixed(2));
                        $('#total-employer').text('RM ' + totalEmployer.toFixed(2));
                        $('#grand-total').text('RM ' + grandTotal.toFixed(2));

                        // Show the details
                        $('#teacher-details').show();

                        // Add special note for age 60+ employees
                        if (response.contributions.age >= 60) {
                            if ($('#age-note').length === 0) {
                                $('#teacher-details').append(
                                    '<div id="age-note" class="alert alert-info mt-3">' +
                                    '<i class="fa fa-info-circle"></i> ' +
                                    '<strong>Note:</strong> This employee is 60 years or older. ' +
                                    'SOCSO and EIS contributions may be reduced or exempted according to Malaysian regulations.' +
                                    '</div>'
                                );
                            }
                        } else {
                            $('#age-note').remove();
                        }

                    } else {
                        alert('Error: ' + (response.error || 'Failed to fetch teacher data'));
                    }
                },
                error: function(xhr, status, error) {
                    $('#loading').hide();
                    console.error('AJAX Error:', error);

                    var errorMessage = 'An error occurred while fetching teacher data.';
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMessage = xhr.responseJSON.error;
                    }

                    alert('Error: ' + errorMessage);
                }
            });
        } else {
            // Hide details if no teacher selected
            $('#teacher-details').hide();
            $('#age-note').remove();
        }
    });

    // Initialize with hidden details
    $('#teacher-details').hide();
});
</script>
@endsection