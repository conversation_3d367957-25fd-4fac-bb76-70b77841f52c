<?php echo $__env->yieldContent('js'); ?>
<!-- <PERSON>ton trigger modal -->

<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
<script src="<?php echo e(asset('assets/js/custom/custom.js')); ?>"></script>
<div class="modal fade formModal" id="admissionBackdrop" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="admissionBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered  modal-xl">
        <div class="modal-content row">
            <div class="col-12 rightSide">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="admissionBackdropLabel"><?php echo e(__('admission_form')); ?></h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form class="create-form" action="<?php echo e(url('admission')); ?>" enctype="multipart/form-data" method="post">
                        <?php echo csrf_field(); ?> 
                        <div class="admissionFormWrapper">
                            <div class="formWrapper">
                                <div class="row">
                                    <input type="hidden" id="schoolIdInput" name="school_id">
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                        <label><?php echo e(__('Gr Number')); ?> <span class="text-danger">*</span></label>
                                        <input type="text" id="admission_no" name="admission_no" value="" readonly placeholder="<?php echo e(__('Gr Number')); ?>" class="form-control">
                                    </div>
                                    </div>
                                    <!-- <div class="col-lg-6">
                                        <div class="inputWrapper">
                                        <label for="class_section"><?php echo e(__('class_section')); ?> <span class="text-danger">*</span></label>
                                        <select name="class_section_id" id="class_section" class="form-control select2">
                                            <option value=""><?php echo e(__('select') . ' ' . __('Class') . ' ' . __('section')); ?></option>                     
                                        </select>
                                        </div>
                                    </div> -->
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                        <label for="session_year_id"><?php echo e(__('session_year')); ?> <span class="text-danger">*</span></label>
                                        <select name="session_year_id" id="session_year" class="form-control select2">                                    
                                        </select>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                        <label><?php echo e(__('admission_date')); ?> <span class="text-danger"></span></label>
                                        <input type="date" id="admission_date" name="admission_date" class="form-control">
                                        <span class="input-group-addon input-group-append">
                                        </span>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                        <label><?php echo e(__('first_name')); ?> <span class="text-danger">*</span></label>
                                        <?php echo Form::text('first_name', null, ['required','placeholder' => __('first_name'), 'class' => 'form-control', 'id' => 'first_name']); ?>

                                        <span id="first_name_alert" class="text-warning small"></span> 
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                        <label><?php echo e(__('last_name')); ?> <span class="text-danger">*</span></label>
                                        <?php echo Form::text('last_name', null, ['required','placeholder' => __('last_name'), 'class' => 'form-control', 'id' => 'last_name']); ?>

                                        <span id="last_name_alert" class="text-warning small"></span> 
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label><?php echo e(__('ic_no_2')); ?> <span class="text-danger">*</span></label>
                                            <?php echo Form::text('ic_no_2', null, ['placeholder' => __('ic_no_2'),'class' => 'form-control']); ?>

                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                        <label><?php echo e(__('dob')); ?> <span class="text-danger">*</span></label>
                                        <input type="date" id="dob" name="dob" placeholder="dob" class="form-control" required>
                                        <span class="input-group-addon input-group-append">
                                        </span>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                            <label><?php echo e(__('gender')); ?> <span class="text-danger">*</span></label><br>
                                            <div class="d-flex">
                                                <div class="form-check form-check-inline">
                                                    <label class="form-check-label">
                                                        <?php echo Form::radio('gender', 'male',true); ?>

                                                        <?php echo e(__('male')); ?>

                                                    </label>
                                                </div>
                                                <div class="form-check form-check-inline">
                                                    <label class="form-check-label">
                                                        <?php echo Form::radio('gender', 'female'); ?>

                                                        <?php echo e(__('female')); ?>

                                                    </label>
                                                </div>
                                            </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label for="image"><?php echo e(__('image')); ?> </label>
                                            <input type="file" name="image" class="file-upload-default form-control"/>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label><?php echo e(__('mobile')); ?></label>
                                            <?php echo Form::tel('mobile', null, ['placeholder' => __('mobile'),'class' => 'form-control', 'id' => 'mobile']); ?>

                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label><?php echo e(__('current_address')); ?> <span class="text-danger">*</span></label>
                                            <?php echo Form::textarea('current_address', null, ['required', 'placeholder' => __('current_address'), 'class' => 'form-control', 'rows' => 3,'id' => 'current_address']); ?>

                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label><?php echo e(__('permanent_address')); ?> <span class="text-danger"></span></label>
                                            <?php echo Form::textarea('permanent_address', null, ['placeholder' => __('permanent_address'), 'class' => 'form-control', 'rows' => 3]); ?>   
                                        </div>
                                    </div>
                                    <div class="row" id="extraFieldsContainer"></div>
                                </div>
                                <br>
                                <hr>
                                <div class="row">
                                   
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label><?php echo e(__('guardian') . ' ' . __('first_name')); ?> <span class="text-danger">*</span></label>
                                            <?php echo Form::text('guardian_first_name', null, ['required','placeholder' => __('guardian') . ' ' . __('first_name'), 'class' => 'form-control', 'id' => 'guardian_first_name']); ?>

                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                        <label><?php echo e(__('guardian') . ' ' . __('last_name')); ?> <span class="text-danger">*</span></label>
                                        <?php echo Form::text('guardian_last_name', null, ['required','placeholder' => __('guardian') . ' ' . __('last_name'), 'class' => 'form-control', 'id' => 'guardian_last_name']); ?>

                                        </div>
                                    </div> 
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label for="guardian_email"><?php echo e(__('guardian') . ' ' . __('email')); ?> <span class="text-danger">*</span></label>
                                            <input type="text" id="guardian_email" class="form-control" name="guardian_email" placeholder="<?php echo e(__('guardian') . ' ' . __('email')); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label><?php echo e(__('guardian') . ' ' . __('mobile')); ?> <span class="text-danger">*</span></label>
                                            <?php echo Form::number('guardian_mobile', null, ['required','placeholder' => __('guardian') . ' ' . __('mobile'), 'class' => 'form-control remove-number-increment', 'id' => 'guardian_mobile','min' => 1 ]); ?>

                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                            <label><?php echo e(__('gender')); ?> <span class="text-danger">*</span></label><br>
                                            <div class="d-flex">
                                                <div class="form-check form-check-inline">
                                                    <label class="form-check-label">
                                                        <input type="radio" checked name="guardian_gender" value="male" id="guardian_male">
                                                        <?php echo e(__('male')); ?>

                                                    </label>
                                                </div>
                                                <div class="form-check form-check-inline">
                                                    <label class="form-check-label">
                                                        <input type="radio" name="guardian_gender" value="female" id="guardian_female">
                                                        <?php echo e(__('female')); ?>

                                                    </label>
                                                </div>
                                            </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label for="guardian_image"><?php echo e(__('image')); ?> </label>
                                            <input type="file" name="guardian_image" class="file-upload-default form-control"/>
                                        </div>
                                    </div>
                                </div>
                                <hr>
                                <div class="row">
                                <div class="headingWrapper">
                                    <span><?php echo e(__('e-invoice')); ?></span>
                                </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label><?php echo e(__('name_per_icno')); ?> <span class="text-danger"></span></label>
                                            <?php echo Form::text('name', null, ['placeholder' =>  __('Enter Buyer Name Per NRIC'), 'class' => 'form-control', 'id' => 'name']); ?>

                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label><?php echo e(__('ic_no')); ?> <span class="text-danger"></span></label>
                                            <?php echo Form::text('ic_no', null, ['placeholder' =>  __('Enter Buyer IC Number'), 'class' => 'form-control', 'id' => 'ic_no']); ?>

                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label><?php echo e(__('tax_identification_number')); ?> <span class="text-danger"></span></label>
                                            <?php echo Form::text('tax_identification_number', null, ['placeholder' =>  __('Enter Buyer TIN'), 'class' => 'form-control', 'id' => 'tax_identification_number']); ?>

                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label><?php echo e(__('email')); ?> <span class="text-danger"></span></label>
                                            <?php echo Form::text('email', null, ['placeholder' =>  __('Enter Buyer Email'), 'class' => 'form-control', 'id' => 'email']); ?>

                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label><?php echo e(__('address')); ?> <span class="text-danger"></span></label>
                                            <?php echo Form::text('address', null, ['placeholder' =>  __('Enter Buyer Address'), 'class' => 'form-control', 'id' => 'address']); ?>

                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label><?php echo e(__('city')); ?> <span class="text-danger"></span></label>
                                            <?php echo Form::text('city', null, ['placeholder' =>  __('city'), 'class' => 'form-control', 'id' => 'city']); ?>

                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label><?php echo e(__('postal_code')); ?> <span class="text-danger"></span></label>
                                            <?php echo Form::text('postal_code', null, ['placeholder' =>  __('postal_code'), 'class' => 'form-control', 'id' => 'postal_code']); ?>

                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label><?php echo e(__('country')); ?> <span class="text-danger"></span></label>
                                            <select name="country" id="countryCodes" class="form-control select2-dropdown select2-hidden-accessible" required>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label><?php echo e(__('state')); ?> <span class="text-danger"></span></label>
                                            <select name="state" id="stateCodes" class="form-control select2-dropdown select2-hidden-accessible" required>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label><?php echo e(__('Contact No.')); ?> <span class="text-danger"></span></label>
                                            <?php echo Form::tel('contact_no', null, ['placeholder' => __('Enter Buyer Contact No.'), 'min' => 0 ,'class' => 'form-control remove-number-increment', 'id' => 'contact_no']); ?>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>                                 
                                    <div class="col-12 modalfooter">

                                        <div class="inputWrapper">
                                            
                                        </div>
                                        <div>
                                        <input class="commonBtn" type="submit" value=<?php echo e(__('submit')); ?>>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<script>

        $(document).ready(function() {
            // Function to check for duplicate name
            function checkDuplicateName() {
                var firstName = $('#first_name').val();
                var lastName = $('#last_name').val();

                // Clear previous alerts
                $('#first_name_alert').text('');
                $('#last_name_alert').text('');

                if (firstName && lastName) {
                    $.ajax({
                        url: '<?php echo e(url("check-duplicate-student")); ?>', // Replace with your actual backend route
                        method: 'GET',
                        data: {
                            first_name: firstName,
                            last_name: lastName
                        },
                        success: function(response) {
                            if (response.duplicate) {
                                // Display SweetAlert
                                Swal.fire({
                                    icon: 'warning',
                                    title: 'Duplicate Entry',
                                    text: 'A student with this name already exists.',
                                    toast: true,
                                    position: 'top-end',
                                    showConfirmButton: false,
                                    timer: 5000,
                                    timerProgressBar: true,
                                    didOpen: (toast) => {
                                        toast.addEventListener('mouseenter', Swal.stopTimer)
                                        toast.addEventListener('mouseleave', Swal.resumeTimer)
                                    }
                                });

                                // Optionally, show a message next to the fields
                                $('#first_name_alert').text('Possible duplicate');
                                $('#last_name_alert').text('Possible duplicate');

                            } else {
                                // No duplicate, clear any messages
                                $('#first_name_alert').text('');
                                $('#last_name_alert').text('');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error("Error checking duplicate name:", error);
                            // Handle error if needed
                        }
                    });
                }
            }

            // Attach the checkDuplicateName function to input change/blur events
            $('#first_name, #last_name').on('change blur', function() {
                checkDuplicateName();
            });

            // Intercept form submission
            $('.create-form').on('submit', function(e) {
                // Check if duplicate alerts are visible
                var isDuplicate = $('#first_name_alert').text() !== '' || $('#last_name_alert').text() !== '';

                if (isDuplicate) {
                    e.preventDefault(); // Prevent default form submission

                    Swal.fire({
                        title: 'Duplicate Name Detected',
                        text: "A student with this name might already exist. Do you want to proceed?",
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#3085d6',
                        cancelButtonColor: '#d33',
                        confirmButtonText: 'Yes, proceed!',
                        cancelButtonText: 'Cancel'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // User confirmed, submit the form programmatically
                            $(e.target).unbind('submit').submit(); // Unbind and resubmit
                        }
                    });
                }
                // If not a duplicate, the form will submit normally
            });
        });

        /*
    function updateFullName() {
        var firstName = $('#guardian_first_name').val();
        var lastName = $('#guardian_last_name').val();
        var fullName = firstName + ' ' + lastName;
        $('#name').val(fullName);
    }

    $('#guardian_first_name, #guardian_last_name').on('input', updateFullName);
    
    $('#current_address').on('change',function(){
        $('#address').val($(this).val());
    });
    */
</script>
<?php /**PATH D:\laragon\www\schola\resources\views/admission_form.blade.php ENDPATH**/ ?>