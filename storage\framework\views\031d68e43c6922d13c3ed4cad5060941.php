<!-- partial:../../partials/_sidebar.html -->
<nav class="sidebar sidebar-offcanvas" id="sidebar">
    <ul class="nav">
        
        <li class="nav-item">
            <a href="<?php echo e(url('/dashboard')); ?>" class="nav-link">
                <i class="fa fa-home menu-icon"></i>
                <span class="menu-title"><?php echo e(__('dashboard')); ?></span>
            </a>
        </li>
        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['medium-create', 'section-create', 'subject-create', 'class-create', 'subject-create',
            'promote-student-create', 'transfer-student-create'])): ?>
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#academics-menu" aria-expanded="false"
                    aria-controls="academics-menu">
                    <i class="fa fa-university menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('academics')); ?></span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="academics-menu">
                    <ul class="nav flex-column sub-menu">
                        <?php if(false): ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('medium-create')): ?>
                                <li class="nav-item"><a href="<?php echo e(route('mediums.index')); ?>" class="nav-link">
                                        <?php echo e(__('medium')); ?> <?php echo e(__('(optional)')); ?></a></li>
                            <?php endif; ?>

                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('section-create')): ?>
                                <li class="nav-item"><a href="<?php echo e(route('section.index')); ?>" class="nav-link">
                                        <?php echo e(__('section')); ?> <?php echo e(__('(optional)')); ?></a></li>
                            <?php endif; ?>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('subject-create')): ?>
                            <li class="nav-item"><a href="<?php echo e(route('subject.index')); ?>" class="nav-link"> <?php echo e(__('subject')); ?>

                                </a></li>

                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('class-create')): ?>
                        <li class="nav-item"><a href="<?php echo e(route('class.index')); ?>" class="nav-link"> <?php echo e(__('Class')); ?>

                                </a></li>
                        <li class="nav-item"><a href="<?php echo e(route('class.subject.index')); ?>" class="nav-link">
                        <?php echo e(__('Class Subject')); ?> </a></li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('class-section-create')): ?>
                            <li class="nav-item"><a href="<?php echo e(route('class-section.index')); ?>"
                                    class="nav-link"><?php echo e(__('Class Section & Teachers')); ?> </a></li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('subject-create')): ?>
                            <li class="nav-item"><a href="<?php echo e(route('commission.index')); ?>" class="nav-link">
                                    <?php echo e(__('subject_fees_per_section')); ?></a></li>
                            <!-- <li class="nav-item"><a href="<?php echo e(route('subject-students.index')); ?>" class="nav-link">
                                    <?php echo e(__('Subject Students')); ?> </a></li> -->
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('semester-create')): ?>
                            <li class="nav-item"><a href="<?php echo e(route('semester.index')); ?>" class="nav-link">
                                    <?php echo e(__('semester')); ?> <?php echo e(__('(optional)')); ?></a></li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('stream-create')): ?>
                            <li class="nav-item"><a class="nav-link" href="<?php echo e(route('stream.index')); ?>"> <?php echo e(__('Stream')); ?>

                                    <?php echo e(__('(optional)')); ?></a></li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('shift-create')): ?>
                            <li class="nav-item"><a class="nav-link" href="<?php echo e(route('shift.index')); ?>"> <?php echo e(__('Shift')); ?>

                                    <?php echo e(__('(optional)')); ?></a></li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any('promote-student-create', 'transfer-student-create')): ?>
                            <li class="nav-item"><a href="<?php echo e(route('promote-student.index')); ?>"
                                    class="nav-link text-wrap"><?php echo e(__('Transfer & Promote Students')); ?></a></li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('student-create')): ?>
                            <li class="nav-item"><a href="<?php echo e(route('students.roll-number.index')); ?>"
                                    class="nav-link"><?php echo e(__('assign')); ?> <?php echo e(__('roll_no')); ?></a></li>
                        <?php endif; ?>
                    </ul>
                </div>
            </li>
        <?php endif; ?>

        
        <?php if(\Spatie\Permission\PermissionServiceProvider::bladeMethodWrapper('hasRole', 'Teacher')): ?>
        <?php if(false): ?>
            <li class="nav-item">
                <a class="nav-link" href="<?php echo e(route('class-section.index')); ?>">
                    <i class="fa fa-sticky-note menu-icon"></i>
                    <span class="menu-title"> <?php echo e(__('Class Section')); ?> </span>
                </a>
            </li>
        <?php endif; ?>
        <?php endif; ?>

        
        <?php if(\Spatie\Permission\PermissionServiceProvider::bladeMethodWrapper('hasAnyRole', ['School Admin'])): ?>
            <?php
                $userSchoolBranch = DB::table('school_branch')
                    ->where('school_id', Auth::user()->school_id)
                    ->first();
            ?>
            <?php if($userSchoolBranch): ?>
                <li class="nav-item">
                    <a class="nav-link" href="<?php echo e(route('branch.index')); ?>">
                        <i class="fa fa-building-o menu-icon"></i>
                        <span class="menu-title"> <?php echo e(__('School Branch')); ?> </span>
                    </a>
                </li>
            <?php endif; ?>
        <?php endif; ?>

        
        <?php if(\Spatie\Permission\PermissionServiceProvider::bladeMethodWrapper('hasAnyRole', ['School Admin', 'Teacher'])): ?>
            <li class="nav-item">
                <a class="nav-link" href="<?php echo e(route('students.student-progress')); ?>" data-access="true">
                    <i class="fa fa-list-alt menu-icon"></i>
                    <span class="menu-title"> <?php echo e(__('Student Progress')); ?> </span>
                </a>
            </li>
        <?php endif; ?>

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['student-create', 'student-list', 'student-reset-password', 'class-teacher', 'form-fields-list',
            'form-fields-create', 'form-fields-edit', 'form-fields-delete', 'guardian-create'])): ?>
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#student-menu" aria-expanded="false"
                    aria-controls="academics-menu">
                    <i class="fa fa-graduation-cap menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('students')); ?></span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="student-menu">
                    <ul class="nav flex-column sub-menu">
                        
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['form-fields-list', 'form-fields-create', 'form-fields-edit', 'form-fields-delete'])): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('form-fields.index')); ?>"
                                    class="nav-link"><?php echo e(__('admission_form_fields')); ?></i></a>
                            </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('student-create')): ?>
                            <li class="nav-item"><a href="<?php echo e(route('students.create')); ?>"
                                    class="nav-link"><?php echo e(__('student_admission')); ?></a></li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['student-list', 'class-teacher'])): ?>
                            <li class="nav-item"><a href="<?php echo e(route('students.index')); ?>"
                                    class="nav-link"><?php echo e(__('student_details')); ?></a></li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['student-list', 'class-teacher'])): ?>
                            <li class="nav-item"><a href="<?php echo e(route('students.student_subject-index')); ?>"
                                    class="nav-link"><?php echo e(__('student_subject')); ?></a></li>
                        <?php endif; ?>
                        
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['class-report','class-create'])): ?>
                            <li class="nav-item"><a href="<?php echo e(route('class-report.index')); ?>"
                                    class="nav-link"><?php echo e(__('Class Report')); ?></a></li>
                        <?php endif; ?>

                        <!-- <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['student-list', 'class-teacher'])): ?>
                                                                    <li class="nav-item"><a href="<?php echo e(route('students.generate-id-card-index')); ?>" class="nav-link" data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true"><?php echo e(__('generate_id_card')); ?></a></li>
                                            <?php endif; ?> -->
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('student-reset-password')): ?>
                            <li class="nav-item"><a href="<?php echo e(route('students.reset-password.index')); ?>"
                                    class="nav-link"><?php echo e(__('students') . ' ' . __('reset_password')); ?></a></li>
                        <?php endif; ?>
                        <?php if(Auth::user()->hasRole('School Admin')): ?>
                            <li class="nav-item"><a href="<?php echo e(route('students.create-bulk-data')); ?>"
                                    class="nav-link"><?php echo e(__('add_bulk_data')); ?></a></li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('student-edit')): ?>
                            <li class="nav-item"><a href="<?php echo e(route('students.upload-profile')); ?>"
                                    class="nav-link"><?php echo e(__('upload_profile_images')); ?></a></li>
                        <?php endif; ?>

                        
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('guardian-create')): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('guardian.index')); ?>" class="nav-link"> <?php echo e(__('Guardian')); ?> </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </li>
        <?php endif; ?>

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('teacher-list')): ?>
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#teacher-menu" aria-expanded="false"
                    aria-controls="academics-menu">
                    <i class="fa fa-user menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('teacher')); ?></span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="teacher-menu">
                    <ul class="nav flex-column sub-menu">
                        
                        <li class="nav-item">
                            <a href="<?php echo e(route('teachers.index')); ?>" class="nav-link">
                                <span class="menu-title"><?php echo e(__('manage_teacher')); ?></span>
                            </a>
                        </li>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('teacher-create')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('teachers.create-bulk-upload')); ?>" class="nav-link">
                                <span class="menu-title"><?php echo e(__('bulk upload')); ?></span>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </li>
        <?php endif; ?>

        
        <?php if(\Spatie\Permission\PermissionServiceProvider::bladeMethodWrapper('hasAnyRole', ['School Admin'])): ?>
        <li class="nav-item">
            <a class="nav-link" data-toggle="collapse" href="#deposits-menu" aria-expanded="false"
                aria-controls="deposits-menu" data-access="false">
                <i class="fa fa-money menu-icon"></i>
                <span class="menu-title"><?php echo e(__('Deposits')); ?></span>
                <i class="menu-arrow"></i>
            </a>
            <div class="collapse" id="deposits-menu">
                <ul class="nav flex-column sub-menu">
                    <li class="nav-item">
                        <a href="<?php echo e(route('deposit-types.index')); ?>" class="nav-link" data-access="false">
                            <?php echo e(__('Deposit Types')); ?>

                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="<?php echo e(route('student-deposits.index')); ?>" class="nav-link" data-access="false">
                            <?php echo e(__('Student Deposits')); ?>

                        </a>
                    </li>
                </ul>
            </div>
        </li>
        <?php endif; ?>
        
        
        <?php if(\Spatie\Permission\PermissionServiceProvider::bladeMethodWrapper('hasAnyRole', ['School Admin', 'Teacher'])): ?>
            <li class="nav-item">
                <!-- <a class="nav-link" data-toggle="collapse" href="#reward-menu" aria-expanded="false" aria-controls="academics-menu"> -->
                <a class="nav-link" aria-expanded="false" data-toggle="collapse" href="#reward-menu" data-access="true">
                    <i class="fa fa-solid fa-gift menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('reward_system')); ?></span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="reward-menu">
                    <ul class="nav flex-column sub-menu">
                        
                        <li class="nav-item">
                            <a href="<?php echo e(route('reward.index')); ?>" class="nav-link"  data-access="true">
                                <span class="menu-title"><?php echo e(__('Manage Reward System')); ?></span>
                            </a>
                        </li> 
                        
                        <li class="nav-item">
                            <a href="<?php echo e(route('reward-redeem.index')); ?>" class="nav-link"  data-access="true">
                                <span class="menu-title"><?php echo e(__('redeem_gift')); ?></span>
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a href="<?php echo e(route('reward-category.index')); ?>" class="nav-link" data-access="true">
                                <span class="menu-title"><?php echo e(__('setting')); ?></span>
                            </a>
                        </li>                       
                    </ul>
                </div>
            </li> 
        <?php endif; ?>
        
        
        <?php if(\Spatie\Permission\PermissionServiceProvider::bladeMethodWrapper('hasAnyRole', ['School Admin', 'Teacher'])): ?>
        <li class="nav-item">
                    <a href="#booking-menu" class="nav-link"  data-toggle="collapse"  data-access="true">
                        <i class="fa-solid fa-calendar-days menu-icon"></i>
                        <span class="menu-title"><?php echo e(__('Booking System')); ?></span>
                        <i class="menu-arrow"></i>
                        
                    </a>
                    <div class="collapse" id="booking-menu">
                    <ul class="nav flex-column sub-menu">                       
                        
                        <li class="nav-item">
                        <a href="<?php echo e(route('booking.index')); ?>" class="nav-link" data-access="true">
                                <span class="menu-title"><?php echo e(__('Add Booking System')); ?></span>
                            </a>
                            <a href="<?php echo e(route('bookingsetting.index')); ?>" class="nav-link" data-access="true">
                                <span class="menu-title"><?php echo e(__('setting')); ?></span>
                            </a>
                            
                        </li>
                       
                    </ul>

                </div>
                </li>

        <?php endif; ?>

        
        <?php if(Auth::user()->hasRole('Teacher')): ?>
            <li class="nav-item">
                <a href="<?php echo e(route('timetable.teacher.show', Auth::user()->id)); ?>" class="nav-link"
                    data-access="true">
                    <i class="fa fa-calendar menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('timetable')); ?></span>
                </a>
            </li>
        <?php else: ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['timetable-create', 'timetable-list'])): ?>
                <li class="nav-item">
                    <a class="nav-link" data-toggle="collapse" href="#timetable-menu" aria-expanded="false"
                        aria-controls="timetable-menu" data-access="true">
                        <i class="fa fa-calendar menu-icon"></i>
                        <span class="menu-title"><?php echo e(__('timetable')); ?></span>
                        <i class="menu-arrow"></i>
                    </a>

                    <div class="collapse" id="timetable-menu">
                        <ul class="nav flex-column sub-menu">
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('timetable-create')): ?>
                                <li class="nav-item">
                                    <a href="<?php echo e(route('timetable.index')); ?>" class="nav-link"
                                        data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>"
                                        data-access="true"><?php echo e(__('create_timetable')); ?> </a>
                                </li>
                            <?php endif; ?>

                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('timetable-list')): ?>
                                <li class="nav-item">
                                    <a href="<?php echo e(route('timetable.teacher.index')); ?>" class="nav-link"
                                        data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                        <?php echo e(__('teacher_timetable')); ?>

                                    </a>
                                </li>
                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('timetable-list')): ?>
                                <li class="nav-item">
                                    <a href="<?php echo e(route('timetable.timetable_configurations')); ?>" class="nav-link"
                                        data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                        <?php echo e(__('timetable_configurations')); ?>

                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </li>
            <?php endif; ?>
        <?php endif; ?>

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['holiday-create', 'holiday-list'])): ?>
            <li class="nav-item">
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('holiday-list')): ?>
                    <a href="<?php echo e(route('holiday.index')); ?>" class="nav-link"
                        data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                        <i class="fa fa-calendar-check-o menu-icon"></i>
                        <span class="menu-title"><?php echo e(__('holiday_list')); ?></span>
                    </a>
                <?php endif; ?>
            </li>
        <?php endif; ?>
        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['lesson-list', 'lesson-create', 'lesson-edit', 'lesson-delete', 'topic-list', 'topic-create',
            'topic-edit', 'topic-delete'])): ?>
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#subject-lesson-menu" aria-expanded="false"
                    aria-controls="subject-lesson-menu" data-access="true">
                    <i class="fa fa-book menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('subject_lesson')); ?></span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="subject-lesson-menu">
                    <ul class="nav flex-column sub-menu">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['lesson-list', 'lesson-create', 'lesson-edit', 'lesson-delete'])): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(url('lesson')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                    <?php echo e(__('create_lesson')); ?></a>
                            </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['topic-list', 'topic-create', 'topic-edit', 'topic-delete'])): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(url('lesson-topic')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                    <?php echo e(__('create_topic')); ?></a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </li>
        <?php endif; ?>

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['assignment-create', 'assignment-submission'])): ?>
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#student-assignment-menu" aria-expanded="false"
                    aria-controls="student-assignment-menu" data-access="true">
                    <i class="fa fa-tasks menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('student_assignment')); ?></span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="student-assignment-menu">
                    <ul class="nav flex-column sub-menu">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('assignment-create')): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('assignment.index')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                    <?php echo e(__('create_assignment')); ?>

                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('assignment-submission')): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('assignment.submission')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                    <?php echo e(__('assignment_submission')); ?>

                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </li>
        <?php endif; ?>

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('slider-create')): ?>
            <li class="nav-item">
                <a href="<?php echo e(route('sliders.index')); ?>" class="nav-link"
                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                    <i class="fa fa-list menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('sliders')); ?></span>
                </a>
            </li>
        <?php endif; ?>

        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['notification-create', 'notification-list', 'notification-delete'])): ?>
            <li class="nav-item">
                <a href="<?php echo e(route('notifications.index')); ?>" class="nav-link"
                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                    <i class="fa fa-bell menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('notification')); ?></span>
                </a>
            </li>
        <?php endif; ?>

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['class-teacher','attendance-list','attendance-create','attendance-edit','attendance-delete'])): ?>
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#attendance-menu" data-access="true" aria-expanded="false"
                   aria-controls="attendance-menu">
                   <i class="fa fa-check menu-icon"></i>
                   <span class="menu-title"><?php echo e(__('Student Attendance')); ?></span>
                   <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="attendance-menu">
                    <ul class="nav flex-column sub-menu">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['class-teacher', 'attendance-create'])): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('attendance.index')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                    <?php echo e(__('add_attendance')); ?>

                                </a>
                            </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['class-teacher', 'attendance-create'])): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('attendance.subject')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                    <?php echo e(__('Subject')); ?> <?php echo e(__('attendance')); ?>

                                </a>
                            </li>
                        <?php endif; ?>

                        
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['class-teacher', 'attendance-list'])): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('attendance.view')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                    <?php echo e(__('view_attendance')); ?>

                                </a>
                            </li>
                        <?php endif; ?>
                        
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['class-teacher', 'attendance-list'])): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('attendance.scan')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                    <?php echo e(__('scan')); ?> <?php echo e(__('attendance')); ?>

                                </a>
                            </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['class-teacher', 'attendance-list'])): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('attendance.daily-attendance-report')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                    <?php echo e(__('daily')); ?> <?php echo e(__('attendance')); ?> <?php echo e(__('report')); ?>

                                </a>
                            </li>
                        <?php endif; ?>

                        
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['class-teacher', 'attendance-list'])): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('attendance.student-attendance-report')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                    <?php echo e(__('student')); ?> <?php echo e(__('attendance')); ?> <?php echo e(__('report')); ?>

                                </a>
                            </li>
                        <?php endif; ?>
                        
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['class-teacher', 'attendance-list'])): ?>
                             <li class="nav-item">
                                 <a href="<?php echo e(route('attendance.summary-attendance-report')); ?>" class="nav-link"
                                     data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                     <?php echo e(__('Attendance')); ?> <?php echo e(__('Summary')); ?> 
                                 </a>
                             </li>
                         <?php endif; ?>

                    </ul>
                </div>
            </li>
        <?php endif; ?>

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['class-teacher','attendance-list','attendance-create','attendance-edit','attendance-delete'])): ?>
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#teacher_attendance-menu" data-access="true" aria-expanded="false"
                   aria-controls="teacher_attendance-menu">
                   <i class="fa fa-check menu-icon"></i>
                   <span class="menu-title"><?php echo e(__('Teacher Attendance')); ?></span>
                   <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="teacher_attendance-menu">
                    <ul class="nav flex-column sub-menu">

                        
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['class-teacher','attendance-list'])): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('attendance.teacher_scan')); ?>" class="nav-link" data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                    <?php echo e(__('scan')); ?> <?php echo e(__('attendance')); ?>

                                </a>
                            </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['attendance-list'])): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('attendance.daily-teacher-attendance-report')); ?>" class="nav-link" data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                    <?php echo e(__('daily')); ?> <?php echo e(__('attendance')); ?> <?php echo e(__('report')); ?>

                                </a>
                            </li>
                        <?php endif; ?>

                        
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['attendance-list'])): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('attendance.teacher-attendance-report')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                    <?php echo e(__('teacher')); ?> <?php echo e(__('attendance')); ?> <?php echo e(__('report')); ?>

                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </li>
        <?php endif; ?>

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('announcement-list')): ?>
            <li class="nav-item">
                <a href="<?php echo e(route('announcement.index')); ?>" class="nav-link"
                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                    <i class="fa fa-bullhorn menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('announcement')); ?></span>
                </a>
            </li>
        <?php endif; ?>

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['exam-create', 'exam-upload-marks', 'grade-create', 'exam-result'])): ?>
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#exam-menu" aria-expanded="false"
                    aria-controls="exam-menu" data-access="true">
                    <i class="fa fa-book menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('Offline Exam')); ?></span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="exam-menu">
                    <ul class="nav flex-column sub-menu">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('exam-create')): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('exams.index')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                    <?php echo e(__('manage_offline_exam')); ?>

                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('exam-upload-marks')): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('exams.timetable')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                    <?php echo e(__('timetable')); ?>

                                </a>
                            </li>

                            <li class="nav-item">
                                <a href="<?php echo e(route('exams.upload-marks')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                    <?php echo e(__('upload')); ?> <?php echo e(__('Exam Marks')); ?>

                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('exam-result')): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('exams.get-result')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                    <?php echo e(__('offline_exam_result')); ?>

                                </a>
                            </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('grade-create')): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('exam.grade.index')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                    <?php echo e(__('exam_grade')); ?>

                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </li>
        <?php endif; ?>

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['online-exam-create', 'online-exam-list', 'online-exam-edit', 'online-exam-delete'])): ?>
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#online-exam-menu" aria-expanded="false"
                    aria-controls="online-exam-menu" data-access="true">
                    <i class="fa fa-laptop menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('online_exam')); ?></span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="online-exam-menu">
                    <ul class="nav flex-column sub-menu">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('online-exam-list')): ?>
                            <li class="nav-item">

                                <a href="<?php echo e(route('online-exam.index')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                    <?php echo e(__('manage_online_exam')); ?>

                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('online-exam-create')): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('online-exam-question.index')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                    <?php echo e(__('manage_questions')); ?>

                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </li>
        <?php endif; ?>

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['fees-list', 'fees-type-list', 'fees-classes-list', 'fees-paid'])): ?>
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#fees-menu" aria-expanded="false"
                    aria-controls="fees-menu" data-access="true">
                    <i class="fa fa-dollar menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('Fees')); ?></span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="fees-menu">
                    <ul class="nav flex-column sub-menu">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('fees-type-list')): ?>
                            <?php if(false): ?>
                                <li class="nav-item">
                                    <a href="<?php echo e(route('fees-type.index')); ?>" class="nav-link"
                                        data-access="true"> <?php echo e(__('Fees Type')); ?>

                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <li class="nav-item">
                                <a href="<?php echo e(route('student-fee-types.item-code')); ?>" class="nav-link" data-access="true"> <?php echo e(__('manage_item_code')); ?>

                                </a>
                            </li>

                            <li class="nav-item">
                                <a href="<?php echo e(route('student-fee-types.index')); ?>" class="nav-link"
                                    data-access="true"> <?php echo e(__('Student Fees Type')); ?>

                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('fees-list')): ?>
                            <?php if(false): ?>
                                <li class="nav-item">
                                    <a href="<?php echo e(route('fees.index')); ?>" class="nav-link" data-access="true">
                                        <?php echo e(__('Manage Fees')); ?></a>
                                </li>
                            <?php endif; ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('student-fees.index')); ?>" class="nav-link"
                                    data-access="true"> <?php echo e(__('Manage Student Fees')); ?></a>
                            </li>
                            <li class="nav-item">
                                <a href="<?php echo e(route('student-fee.recurring-fees')); ?>" class="nav-link"
                                    data-access="true"> <?php echo e(__('Manage Recurring Fees')); ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('fees-paid')): ?>
                            <?php if(false): ?>
                                <li class="nav-item">
                                    <a href="<?php echo e(route('fees.paid.index')); ?>" class="nav-link"
                                        data-access="true"> <?php echo e(__('Fees Paid')); ?>

                                    </a>
                                </li>
                            <?php endif; ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('student-fees.paid.index')); ?>" class="nav-link"
                                    data-access="true"> <?php echo e(__('Student Fees Paid')); ?>

                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('fees-type-list')): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('student-fee-types.credit-note')); ?>" class="nav-link"
                                    data-access="true"> <?php echo e(__('Manage Credit Note')); ?>

                                </a>
                                <a href="<?php echo e(route('student-fee-types.debit-note')); ?>" class="nav-link"
                                    data-access="true"> <?php echo e(__('Manage Debit Note')); ?>

                                </a>
                                <a href="<?php echo e(route('student-fee-types.refund-note')); ?>" class="nav-link"
                                    data-access="true"> <?php echo e(__('Manage Refund Note')); ?>

                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(false): ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('fees-paid')): ?>
                                <li class="nav-item">
                                    <a href="<?php echo e(route('fees.transactions.log.index')); ?>" class="nav-link"
                                        data-access="true"> <?php echo e(__('Fees Transaction Logs')); ?>

                                    </a>
                                </li>
                            <?php endif; ?>
                        <?php endif; ?>
                    </ul>
                </div>
            </li>
        <?php endif; ?>

        <?php if(\Spatie\Permission\PermissionServiceProvider::bladeMethodWrapper('hasAnyRole', ['School Admin'])): ?>
        
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#selfbilling-menu" aria-expanded="false"
                    aria-controls="selfbilling-menu" data-access="true">
                    <i class="fa-solid fa-money-bill menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('self_billing')); ?></span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="selfbilling-menu">
                    <ul class="nav flex-column sub-menu">
                        <li class="nav-item">
                            <a href="<?php echo e(route('supplier.index')); ?>" class="nav-link"
                                data-access="true"> <?php echo e(__('supplier')); ?>

                            </a>
                        </li>
                            
                        <li class="nav-item">
                            <a href="<?php echo e(route('self-billing.index')); ?>" class="nav-link" data-access="true"> <?php echo e(__('self_billing')); ?>

                            </a>
                        </li>
                    </ul>
                </div>
            </li>

        
            <li class="nav-item">
                
                <a href="<?php echo e(route('statement.index')); ?>" class="nav-link" data-access="true">  
                    <i class="fa fa-file-alt menu-icon"></i> 
                    <span class="menu-title"><?php echo e(__('statement')); ?></span>
                </a>
            </li>
        <?php endif; ?>

        
        <?php if(\Spatie\Permission\PermissionServiceProvider::bladeMethodWrapper('hasRole', ['School Admin'])): ?>
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#subject-package-menu" aria-expanded="false"
                    aria-controls="subject-package-menu" data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>"
                    data-access="true">
                    <i class="fa fa-archive menu-icon" aria-hidden="true"></i>
                    <span class="menu-title"><?php echo e(__('package')); ?></span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="subject-package-menu">
                    <ul class="nav flex-column sub-menu">
                        <li class="nav-item">
                            <a href="<?php echo e(route('subject-package.index')); ?>" class="nav-link"
                                data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>"
                                data-access="true"><?php echo e(__('package')); ?> </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo e(route('subject-package.purchase')); ?>" class="nav-link"
                                data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>"
                                data-access="true"><?php echo e(__('assign_package')); ?> </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo e(route('subject-package.usage')); ?>" class="nav-link"
                                data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>"
                                data-access="true"><?php echo e(__('usage')); ?> </a>
                        </li>
                    </ul>
                </div>
            </li>
        <?php endif; ?>

        
        <?php if(\Spatie\Permission\PermissionServiceProvider::bladeMethodWrapper('hasAnyRole', ['Admin', 'Staff', 'Teacher'])): ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['leave-list', 'leave-create', 'leave-edit', 'leave-delete'])): ?>
                <li class="nav-item">
                    <a class="nav-link" data-toggle="collapse" href="#staff-leave-menu" data-access="true"
                        aria-expanded="false" aria-controls="staff-leave-menu">
                        <i class="fa fa-plane menu-icon"></i>
                        <span class="menu-title"><?php echo e(__('leaves')); ?></span>
                        <i class="menu-arrow"></i>
                    </a>
                    <div class="collapse" id="staff-leave-menu">
                        <ul class="nav flex-column sub-menu">
                            <li class="nav-item">
                                <a href="<?php echo e(route('leave.index')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                    <?php echo e(__('apply_leave')); ?>

                                </a>
                            </li>

                            <li class="nav-item">
                                <a href="<?php echo e(route('leave.leave_report')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                    <?php echo e(__('leave_report')); ?>

                                </a>
                            </li>
                        </ul>
                    </div>
                </li>
            <?php endif; ?>
        <?php endif; ?>

        <?php if(Auth::user()->school_id && Auth::user()->staff): ?>
            <li class="nav-item">
                <a href="<?php echo e(route('payroll.slip.index')); ?>" class="nav-link"
                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                    <i class="fa fa-money menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('payroll')); ?> <?php echo e(__('slips')); ?></span>
                </a>
            </li>
        <?php endif; ?>

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['schools-list', 'schools-create', 'schools-edit', 'schools-delete'])): ?>
            <li class="nav-item">
                <a href="<?php echo e(route('schools.index')); ?>" class="nav-link">
                    <i class="fa fa-university menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('schools')); ?></span>
                </a>
            </li>
        <?php endif; ?>

        <?php if(\Spatie\Permission\PermissionServiceProvider::bladeMethodWrapper('hasAnyRole', ['School Admin'])): ?>
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#report-menu" aria-expanded="false"
                    aria-controls="fees-menu" data-access="true">
                    <i class="fa fa-file menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('Reports')); ?></span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="report-menu">
                    <ul class="nav flex-column sub-menu">

                        <li class="nav-item">
                            <a href="<?php echo e(route('student-fees.fees-collection-report.index')); ?>" class="nav-link"
                                data-access="true"> <?php echo e(__('Fees Collection Report')); ?>

                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="<?php echo e(route('student-fees.fees-pending-report.index')); ?>" class="nav-link"
                                data-access="true"> <?php echo e(__('Pending Fees Report')); ?>

                            </a>
                        </li>

                        <!-- <li class="nav-item">
                            <a href="<?php echo e(route('student-fees.fees-outstanding-report.index')); ?>" class="nav-link"
                                data-access="false"> <?php echo e(__('Outstanding Fees Report')); ?>

                            </a>
                        </li> -->

                        <li class="nav-item">
                            <a href="<?php echo e(route('student-fees.fees-student-report.index')); ?>" class="nav-link"
                                data-access="true"> <?php echo e(__('Student Fees Report')); ?>

                            </a>
                        </li>

                        <!-- <li class="nav-item">
                            <a href="<?php echo e(route('student-fees.fees-type-report.index')); ?>" class="nav-link"
                                data-access="false"> <?php echo e(__('Student Fees Type Report')); ?>

                            </a>
                        </li> -->

                        <!-- <li class="nav-item">
                            <a href="<?php echo e(route('student-fees.fees-class-report.index')); ?>" class="nav-link"
                                data-access="false"> <?php echo e(__('Class Fees Report')); ?>

                            </a>
                        </li> -->

                        <li class="nav-item">
                            <a href="<?php echo e(route('financial-report.index')); ?>" class="nav-link"
                                data-access="true"> <?php echo e(__('Financial Report')); ?>

                            </a>
                        </li>

                            <!-- <li class="nav-item">
                                <a href="<?php echo e(route('student-fees.fees-all-report.index')); ?>" class="nav-link" data-access="false"> <?php echo e(__('All Transaction Report')); ?>

                                </a>
                            </li> -->
                    </ul>
                </div>
            </li>
        <?php endif; ?>

      

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['package-list', 'package-create', 'package-edit', 'package-delete'])): ?>
            <li class="nav-item">
                <a href="<?php echo e(route('package.index')); ?>" class="nav-link">
                <i class="fa fa-receipt menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('package')); ?></span>
                </a>
            </li>
        <?php endif; ?>

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['package-list', 'package-create', 'package-edit', 'package-delete'])): ?>
            <li class="nav-item">
                <a href="<?php echo e(route('announcement-news.index')); ?>" class="nav-link">
                    <i class="fa fa-bullhorn menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('announcement')); ?></span>
                </a>
            </li>
        <?php endif; ?>
        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['addons-list', 'addons-create', 'addons-edit', 'addons-delete'])): ?>
            <li class="nav-item">
                <a href="<?php echo e(route('addons.index')); ?>" class="nav-link">
                    <i class="fa fa-puzzle-piece menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('addons')); ?></span>
                </a>
            </li>
        <?php endif; ?>

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['addons-list', 'addons-create', 'addons-edit', 'addons-delete', 'package-list', 'package-create',
            'package-edit', 'package-delete'])): ?>
            <li class="nav-item">
                <a href="<?php echo e(url('features')); ?>" class="nav-link">
                    <i class="fa fa-list-ul menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('features')); ?></span>
                </a>
            </li>
        <?php endif; ?>

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('subscription-view')): ?>
            <li class="nav-item">
                <a href="<?php echo e(url('subscriptions/report')); ?>" class="nav-link">
                    <i class="fa fa-puzzle-piece menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('subscription')); ?></span>
                </a>
            </li>

            <li class="nav-item">
                <a href="<?php echo e(url('subscriptions/transactions')); ?>" class="nav-link">
                    <i class="fa fa-money menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('subscription_transaction')); ?></span>
                </a>
            </li>
        <?php endif; ?>

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['expense-category-create', 'expense-category-list', 'expense-category-edit', 'expense-category-delete',
            'expense-create', 'expense-list', 'expense-edit', 'expense-delete'])): ?>
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#expense-menu" aria-expanded="false"
                    aria-controls="expense-menu" data-access="true">
                    <i class="fa fa-money menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('expense')); ?></span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="expense-menu">
                    <ul class="nav flex-column sub-menu">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['expense-category-create', 'expense-category-list', 'expense-category-edit',
                            'expense-category-delete'])): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('expense-category.index')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>"
                                    data-access="true"><?php echo e(__('manage_category')); ?> </a>
                            </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['expense-create', 'expense-list', 'expense-edit', 'expense-delete'])): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('expense.index')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                    <?php echo e(__('manage_expense')); ?>

                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </li>
        <?php endif; ?>

        
        <?php if(\Spatie\Permission\PermissionServiceProvider::bladeMethodWrapper('hasRole', ['School Admin'])): ?>
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#credit-menu" aria-expanded="false"
                    aria-controls="credit-menu" data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>"
                    data-access="true">
                    <i class="las la-wallet menu-icon" aria-hidden="true"
                        style="font-size: 25px; margin-left: 2px !important;; margin-right: 3px;"></i>
                    <span class="menu-title"><?php echo e(__('Credit')); ?></span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="credit-menu">
                    <ul class="nav flex-column sub-menu">
                        <li class="nav-item">
                            <a href="<?php echo e(route('credit.view-credit')); ?>" class="nav-link"
                                data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>"
                                data-access="true"><?php echo e(__('View Credit')); ?> </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo e(route('credit.manage-credit')); ?>" class="nav-link"
                                data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>"
                                data-access="true"><?php echo e(__('Manage Credit')); ?> </a>
                        </li>

                    </ul>
                </div>
            </li>
        <?php endif; ?>

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['payroll-create', 'payroll-list', 'payroll-edit', 'payroll-delete', 'payroll-settings-list',
            'payroll-settings-create', 'payroll-settings-edit', 'payroll-settings-delete'])): ?>
            <li class="nav-item">
                <a href="#payroll-menu" class="nav-link" data-toggle="collapse"
                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                    <i class="fa fa-credit-card-alt menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('payroll')); ?></span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="payroll-menu">
                    <ul class="nav flex-column sub-menu">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['payroll-create', 'payroll-list', 'payroll-edit', 'payroll-delete'])): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('payroll.index')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>"
                                    data-access="true"><?php echo e(__('manage_payroll')); ?> </a>
                            </li>

                            <li class="nav-item">
                                <a href="<?php echo e(route('customPayroll.index')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>"
                                    data-access="true"><?php echo e(__('custom_payroll')); ?> </a>
                            </li>

                        <?php endif; ?>

                        <!-- <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['payroll-settings-list', 'payroll-settings-create', 'payroll-settings-edit',
                            'payroll-settings-delete'])): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('payroll-setting.index')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                    <?php echo e(__('payroll_setting')); ?>

                                </a>
                            </li>
                        <?php endif; ?> -->
                    </ul>
                </div>
            </li>
        <?php endif; ?>

        
        <?php if(\Spatie\Permission\PermissionServiceProvider::bladeMethodWrapper('hasRole', ['School Admin'])): ?>
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#commission-menu" 
                    aria-expanded="false" aria-controls="commission-menu" 
                    data-access="true">
                    <i class="fa las la-coins menu-icon"
                        style="font-size: 25px; margin-left: 2px !important;; margin-right: 3px;"></i>
                    <span class="menu-title"><?php echo e(__('Commission')); ?></span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="commission-menu">
                    <ul class="nav flex-column sub-menu">
                        <li class="nav-item">
                            <a href="<?php echo e(route('user-groups.index')); ?>" class="nav-link"
                                data-access="true"> 
                                <?php echo e(__('user_group')); ?>

                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo e(route('managecommission.scan')); ?>" class="nav-link"
                                data-access="true"> 
                                <?php echo e(__('manage').' '.__('commission_per_session')); ?>

                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo e(route('managecommission.per-month.index')); ?>" class="nav-link"
                                data-access="true"> 
                                <?php echo e(__('manage').' '.__('commission_per_month')); ?>

                            </a>
                        </li>
                    </ul>
                </div>
            </li>
        <?php endif; ?>

        
        

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['gallery-create', 'gallery-list', 'gallery-edit', 'gallery-delete'])): ?>
            <li class="nav-item">
                <a href="<?php echo e(route('gallery.index')); ?>" class="nav-link"
                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                    <i class="fa fa-picture-o menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('gallery')); ?></span>
                </a>
            </li>
        <?php endif; ?>

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['certificate-create', 'certificate-list', 'certificate-edit', 'certificate-delete', 'student-list',
            'class-teacher', 'id-card-settings'])): ?>
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#certificate-menu" aria-expanded="false"
                    aria-controls="certificate-menu" data-access="true">
                    <i class="fa fa-trophy menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('certificate_id_card')); ?></span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="certificate-menu">
                    <ul class="nav flex-column sub-menu">

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['certificate-create', 'certificate-list', 'certificate-edit', 'certificate-delete'])): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(url('certificate-template')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                    <?php echo e(__('certificate_template')); ?>

                                </a>
                            </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['certificate-list'])): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(url('certificate')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                    <?php echo e(__('student_certificate')); ?>

                                </a>
                            </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['certificate-list'])): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(url('certificate/staff-certificate')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                    <?php echo e(__('staff_certificate')); ?>

                                </a>
                            </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('id-card-settings')): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo e(route('id-card-settings')); ?>"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>"
                                    data-access="true"><?php echo e(__('id_card_settings')); ?></a>
                            </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['student-list', 'class-teacher'])): ?>
                            <li class="nav-item"><a href="<?php echo e(route('students.generate-id-card-index')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>"
                                    data-access="true"><?php echo e(__('student_id_card')); ?></a></li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('staff-list')): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('staff.id-card')); ?>" class="nav-link"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>"
                                    data-access="true"><?php echo e(__('staff_id_card')); ?></a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </li>
        <?php endif; ?>
        <?php if(Auth::user()->school_id): ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['role-list', 'role-create', 'role-edit', 'role-delete', 'staff-list', 'staff-create', 'staff-edit',
                'staff-delete', 'approve-leave'])): ?>
                <li class="nav-item">
                    <a class="nav-link" data-toggle="collapse" href="#staff-management" aria-expanded="false"
                        aria-controls="staff-management-menu" data-access="true">
                        <i class="fa fa-user-secret menu-icon"></i>
                        <span class="menu-title"><?php echo e(__('Staff Management')); ?></span>
                        <i class="menu-arrow"></i>
                    </a>
                    <div class="collapse" id="staff-management">
                        <ul class="nav flex-column sub-menu">
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['role-list', 'role-create', 'role-edit', 'role-delete'])): ?>
                                <li class="nav-item">
                                    <a href="<?php echo e(route('roles.index')); ?>" class="nav-link"
                                        data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>"
                                        data-access="true"><?php echo e(__('Role & Permission')); ?></a>
                                </li>
                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['staff-list', 'staff-create', 'staff-edit', 'staff-delete'])): ?>
                                <li class="nav-item">
                                    <a href="<?php echo e(route('staff.index')); ?>" class="nav-link"
                                        data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>"
                                        data-access="true"><?php echo e(__('staff')); ?></a>
                                </li>
                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['staff-list', 'staff-create', 'staff-edit', 'staff-delete'])): ?>
                                <li class="nav-item">
                                    <a href="<?php echo e(route('staff.create-bulk-upload')); ?>" class="nav-link"
                                        data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>"
                                        data-access="true"><?php echo e(__('bulk upload')); ?></a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </li>
            <?php endif; ?>
            <?php if(\Spatie\Permission\PermissionServiceProvider::bladeMethodWrapper('hasRole', ['School Admin'])): ?>
                <li class="nav-item">
                    <a href="<?php echo e(route('document.index')); ?>" class="nav-link"
                        data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>">
                        <i
                            class="las la-file-alt menu-icon"style="font-size: 25px; margin-left: 2px !important;; margin-right: 3px;"></i>
                        <span class="menu-title"><?php echo e(__('Document Upload')); ?></span>
                    </a>
                </li>
            <?php endif; ?>

            
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['approve-leave'])): ?>
                <li class="nav-item">
                    <a class="nav-link" data-toggle="collapse" href="#staff-leave-management" aria-expanded="false"
                        aria-controls="staff-leave-management-menu" data-access="true">
                        <i class="fa fa-plane menu-icon"></i>
                        <span class="menu-title"><?php echo e(__('Staff Leave')); ?></span>
                        <i class="menu-arrow"></i>
                    </a>
                    <div class="collapse" id="staff-leave-management">
                        <ul class="nav flex-column sub-menu">

                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('approve-leave')): ?>
                                <li class="nav-item">
                                    <a href="<?php echo e(route('leave.request')); ?>" class="nav-link"
                                        data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>"
                                        data-access="true"><?php echo e(__('staff')); ?> <?php echo e(__('leaves')); ?></a>
                                </li>
                                <li class="nav-item">
                                    <a href="<?php echo e(url('leave/leave_report')); ?>" class="nav-link"
                                        data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>"
                                        data-access="true"><?php echo e(__('leave_report')); ?></a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </li>
            <?php endif; ?>
        <?php else: ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['role-list', 'role-create', 'role-edit', 'role-delete', 'staff-list', 'staff-create', 'staff-edit',
                'staff-delete'])): ?>
                <li class="nav-item">
                    <a class="nav-link" data-toggle="collapse" href="#staff-management" aria-expanded="false"
                        aria-controls="staff-management-menu">
                        <i class="fa fa-user-secret menu-icon"></i>
                        <span class="menu-title"><?php echo e(__('Staff Management')); ?></span>
                        <i class="menu-arrow"></i>
                    </a>
                    <div class="collapse" id="staff-management">
                        <ul class="nav flex-column sub-menu">
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['role-list', 'role-create', 'role-edit', 'role-delete'])): ?>
                                <li class="nav-item">
                                    <a href="<?php echo e(route('roles.index')); ?>"
                                        class="nav-link"><?php echo e(__('Role & Permission')); ?></a>
                                </li>
                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['staff-list', 'staff-create', 'staff-edit', 'staff-delete'])): ?>
                                <li class="nav-item">
                                    <a href="<?php echo e(route('staff.index')); ?>" class="nav-link"><?php echo e(__('staff')); ?></a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </li>
            <?php endif; ?>
        <?php endif; ?>

        
        <?php if(\Spatie\Permission\PermissionServiceProvider::bladeMethodWrapper('hasRole', 'School Admin')): ?>
            <?php if(false): ?>
                <li class="nav-item">
                    <a class="nav-link" data-toggle="collapse" href="#subscription" aria-expanded="false"
                        aria-controls="subscription-menu">
                        <i class="fa fa-puzzle-piece menu-icon"></i>
                        <span class="menu-title"><?php echo e(__('subscription')); ?></span>
                        <i class="menu-arrow"></i>
                    </a>
                    <div class="collapse" id="subscription">
                        <ul class="nav flex-column sub-menu">
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="<?php echo e(route('subscriptions.history')); ?>"><?php echo e(__('subscription')); ?></a>
                            </li>
                            <?php if(false): ?>
                                <li class="nav-item">
                                    <a class="nav-link"
                                        href="<?php echo e(route('subscriptions.index')); ?>"><?php echo e(__('plans')); ?></a>
                                </li>
                            <?php endif; ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo e(route('addons.plan')); ?>"><?php echo e(__('addons')); ?></a>
                            </li>
                        </ul>
                    </div>
                </li>
            <?php endif; ?>
            
            <?php if(false): ?>
                <li class="nav-item">
                    <a href="<?php echo e(url('staff/support')); ?>" class="nav-link">
                        <i class="fa fa-question menu-icon"></i>
                        <span class="menu-title"><?php echo e(__('support')); ?></span>
                    </a>
                </li>
            <?php endif; ?>

            <li class="nav-item">
                <a href="<?php echo e(url('features')); ?>" class="nav-link">
                    <i class="fa fa-list-ul menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('features')); ?></span>
                </a>
            </li>

            <li class="nav-item">
                <a href="https://leapseed-sdn-bhd.gitbook.io/schola.one/" class="nav-link" target="_blank">
                    <i class="fa fa-solid fa-book menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('admin_manual')); ?></span>
                </a>
            </li>
        <?php endif; ?>

        <!-- <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['faqs-create', 'faqs-list', 'faqs-edit', 'faqs-delete'])): ?>
            <li class="nav-item">
                <a href="<?php echo e(route('faqs.index')); ?>" class="nav-link">
                    <i class="fa fa-question menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('faqs')); ?></span>
                </a>
            </li>
        <?php endif; ?> -->
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('web-settings')): ?>
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#web_settings" aria-expanded="false"
                    aria-controls="web_settings-menu">
                    <i class="fa fa-cogs menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('web_settings')); ?></span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="web_settings">
                    <ul class="nav flex-column sub-menu">
                        <li class="nav-item">
                            <a class="nav-link"
                                href="<?php echo e(route('web-settings.index')); ?>"><?php echo e(__('general_settings')); ?></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link"
                                href="<?php echo e(route('web-settings.feature.sections')); ?>"><?php echo e(__('feature_sections')); ?></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link"
                                href="<?php echo e(route('video-settings.index')); ?>"><?php echo e(__('video_setting')); ?></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link"
                                href="<?php echo e(route('picture-settings.index')); ?>"><?php echo e(__('picture_setting')); ?></a>
                        </li>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['faqs-create', 'faqs-list', 'faqs-edit', 'faqs-delete'])): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo e(route('faqs.index')); ?>"><?php echo e(__('faqs')); ?></a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </li>
        <?php endif; ?>

        
        <?php if(isset($systemSettings['school_website_feature']) && $systemSettings['school_website_feature'] == 1): ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('school-web-settings')): ?>
                <li class="nav-item">
                    <a class="nav-link" data-toggle="collapse" href="#web_settings" aria-expanded="false"
                        aria-controls="web_settings-menu" data-access="true">
                        <i class="fa fa-cogs menu-icon"></i>
                        <span class="menu-title"><?php echo e(__('web_settings')); ?></span>
                        <i class="menu-arrow"></i>
                    </a>
                    <div class="collapse" id="web_settings">
                        <ul class="nav flex-column sub-menu">
                            <?php if(isset($systemSettings['school_website_feature']) && $systemSettings['school_website_feature'] == 1): ?>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('class-group-create')): ?>
                                    <li class="nav-item"><a href="<?php echo e(route('class-group.index')); ?>" class="nav-link">
                                            <?php echo e(__('class_group')); ?> </a></li>
                                <?php endif; ?>
                            <?php endif; ?>

                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo e(route('school.web-settings.index')); ?>"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>"
                                    data-access="true"><?php echo e(__('content')); ?></a>
                            </li>

                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['faqs-create', 'faqs-list', 'faqs-edit', 'faqs-delete'])): ?>
                                <li class="nav-item">
                                    <a class="nav-link" href="<?php echo e(route('faqs.index')); ?>"
                                        data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>"
                                        data-access="true"><?php echo e(__('faqs')); ?></a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </li>
            <?php endif; ?>
        <?php endif; ?>
        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['app-settings', 'language-list', 'school-setting-manage', 'system-setting-manage',
            'fcm-setting-manage', 'email-setting-create', 'privacy-policy', 'contact-us', 'about-us', 'guidance-create',
            'guidance-list', 'guidance-edit', 'guidance-delete', 'email-template'])): ?>
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#settings-menu" aria-expanded="false"
                    aria-controls="settings-menu">
                    <i class="fa fa-cog menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('system_settings')); ?></span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="settings-menu">
                    <ul class="nav flex-column sub-menu">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('app-settings')): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo e(route('system-settings.app')); ?>"><?php echo e(__('app_settings')); ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('school-setting-manage')): ?>
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="<?php echo e(route('school-settings.index')); ?>"><?php echo e(__('general_settings')); ?></a>
                            </li>

                            
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo e(route('session-year.index')); ?>"><?php echo e(__('session_year')); ?></a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo e(route('leave-master.index')); ?>"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>"
                                    data-access="true"><?php echo e(__('leaves')); ?> <?php echo e(__('settings')); ?></a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo e(route('leave-master.category-index')); ?>"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>"
                                    data-access="true"><?php echo e(__('leaves')); ?> <?php echo e(__('category')); ?>

                                    <?php echo e(__('settings')); ?></a>
                            </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('student-id-card-settings')): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo e(route('student.id-card-settings')); ?>"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>"
                                    data-access="true"><?php echo e(__('student_id_card')); ?></a>
                            </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('system-setting-manage')): ?>
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="<?php echo e(route('system-settings.index')); ?>"><?php echo e(__('general_settings')); ?></a>
                            </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('subscription-settings')): ?>
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="<?php echo e(route('system-settings.subscription-settings')); ?>"><?php echo e(__('subscription_settings')); ?></a>
                            </li>
                        <?php endif; ?>

                         <?php if(\Spatie\Permission\PermissionServiceProvider::bladeMethodWrapper('hasRole', 'Superadmin')): ?>
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="<?php echo e(route('system-settings.rfid-whitelist')); ?>"><?php echo e(__('rfid_whitelist')); ?></a>
                            </li>
                        <?php endif; ?>

                        
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['guidance-create', 'guidance-list', 'guidance-edit', 'guidance-delete'])): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo e(route('guidances.index')); ?>"><?php echo e(__('guidance')); ?></a>
                            </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('language-list')): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo e(url('language')); ?>">
                                    <?php echo e(__('language_settings')); ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('fcm-setting-manage')): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo e(route('system-settings.fcm')); ?>">
                                    <?php echo e(__('notification_settings')); ?></a>
                            </li>
                        <?php endif; ?>

                        

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('school-setting-manage')): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('school-settings.online-exam.index')); ?>" class="nav-link text-wrap"
                                    data-name="<?php echo e(Auth::user()->getRoleNames()[0]); ?>" data-access="true">
                                    <?php echo e(__('online_exam_terms_condition')); ?>

                                </a>
                            </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('email-setting-create')): ?>
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="<?php echo e(route('system-settings.email.index')); ?>"><?php echo e(__('email_configuration')); ?></a>
                            </li>
                        <?php endif; ?>

                        
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('email-setting-create')): ?>
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="<?php echo e(route('system-settings.email.template')); ?>"><?php echo e(__('email_template')); ?></a>
                            </li>
                        <?php endif; ?>

                        
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('email-template')): ?>
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="<?php echo e(route('school-settings.email.template')); ?>"><?php echo e(__('email_template')); ?></a>
                            </li>
                        <?php endif; ?>

                         <?php if(\Spatie\Permission\PermissionServiceProvider::bladeMethodWrapper('hasRole', 'School Admin')): ?>
                            <li class="nav-item">
                                <a class="nav-link" data-access="true"
                                    href="<?php echo e(route('school-settings.e-invoice.index')); ?>"><?php echo e(__('eInvoice_settings')); ?></a>
                            </li>
                        <?php endif; ?>

                        
                        <?php if(\Spatie\Permission\PermissionServiceProvider::bladeMethodWrapper('hasAnyRole', ['Super Admin', 'School Admin'])): ?>
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="<?php echo e(route('system-settings.payment.index')); ?>"><?php echo e(__('Payment Settings')); ?></a>
                            </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('school-setting-manage')): ?>
                        <li class="nav-item">
                            <a class="nav-link" data-access="true" href="<?php echo e(route('school-settings.third-party')); ?>"><?php echo e(__('Third-Party APIs')); ?></a>
                        </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('system-setting-manage')): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo e(route('system-settings.third-party')); ?>"><?php echo e(__('Third-Party APIs')); ?></a>
                        </li>
                        <?php endif; ?>

                        <?php if(\Spatie\Permission\PermissionServiceProvider::bladeMethodWrapper('hasRole', 'School Admin')): ?>
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="<?php echo e(route('school-settings.terms-condition')); ?>"><?php echo e(__('terms_condition')); ?></a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link"
                                    href="<?php echo e(route('school-settings.privacy-policy')); ?>"><?php echo e(__('privacy_policy')); ?></a>
                            </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('privacy-policy')): ?>
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="<?php echo e(route('system-settings.privacy-policy')); ?>"><?php echo e(__('privacy_policy')); ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('contact-us')): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo e(route('system-settings.contact-us')); ?>">
                                    <?php echo e(__('contact_us')); ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('about-us')): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo e(route('system-settings.about-us')); ?>"> <?php echo e(__('about_us')); ?>

                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('terms-condition')): ?>
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="<?php echo e(route('system-settings.terms-condition')); ?>"><?php echo e(__('terms_condition')); ?></a>
                            </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('school-terms-condition')): ?>
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="<?php echo e(route('system-settings.school-terms-condition')); ?>"><?php echo e(__('school_terms_condition')); ?></a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </li>
        <?php endif; ?>

        <?php if(Auth::user()->hasRole('Super Admin')): ?>
            <li class="nav-item">
                <a class="nav-link" href="<?php echo e(route('system-update.index')); ?>">
                    <i class="fa fa-cloud-download menu-icon"></i>
                    <span class="menu-title"><?php echo e(__('system_update')); ?></span>
                </a>
            </li>
        <?php endif; ?>

    </ul>
</nav>
<?php /**PATH D:\laragon\www\schola\resources\views/layouts/sidebar.blade.php ENDPATH**/ ?>