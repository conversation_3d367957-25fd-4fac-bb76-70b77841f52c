<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AttendanceAutoAbsent extends Model
{
    use HasFactory;

    protected $table = 'attendance_auto_absent';

    protected $fillable = [
        'date',
        'marked_at',
        'school_id',
    ];

    protected $casts = [
        'date' => 'date',
        'marked_at' => 'datetime'
    ];

    public function school()
    {
        return $this->belongsTo(School::class);
    }
}
