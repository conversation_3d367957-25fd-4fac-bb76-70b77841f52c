{"__meta": {"id": "01JXBVSCB1JJSFPRP67GNSD09P", "datetime": "2025-06-10 10:47:53", "utime": **********.441765, "method": "GET", "uri": "/payroll/1?sort=id&order=desc&search=&month=1&year=2025", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.240336, "end": **********.441786, "duration": 2.2014501094818115, "duration_str": "2.2s", "measures": [{"label": "Booting", "start": **********.240336, "relative_start": 0, "end": **********.567967, "relative_end": **********.567967, "duration": 0.*****************, "duration_str": "328ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.56798, "relative_start": 0.*****************, "end": **********.441788, "relative_end": 1.9073486328125e-06, "duration": 1.****************, "duration_str": "1.87s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.592499, "relative_start": 0.*****************, "end": **********.612163, "relative_end": **********.612163, "duration": 0.*****************, "duration_str": "19.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.43932, "relative_start": 2.***************, "end": **********.43984, "relative_end": **********.43984, "duration": 0.0005199909210205078, "duration_str": "520μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "89MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.10", "Environment": "local", "Debug Mode": "Enabled", "URL": "schola.one", "Timezone": "Asia/Kuala_Lumpur", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 210, "nb_statements": 210, "nb_visible_statements": 210, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.24988, "accumulated_duration_str": "250ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 110 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `users` where `id` = 2 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.665194, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "schola", "explain": null, "start_percent": 0, "width_percent": 0.272}, {"sql": "select * from `schools` where `schools`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 13}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "Role", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckRole.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.675241, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "checkSchoolStatus:13", "source": {"index": 22, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FMiddleware%2FCheckSchoolStatus.php&line=13", "ajax": false, "filename": "CheckSchoolStatus.php", "line": "13"}, "connection": "schola", "explain": null, "start_percent": 0.272, "width_percent": 0.268}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (2) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 27}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "Role", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckRole.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.683225, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "schola", "explain": null, "start_percent": 0.54, "width_percent": 0.22}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 304}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 174}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 175}], "start": **********.6913571, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:266", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=266", "ajax": false, "filename": "PermissionRegistrar.php", "line": "266"}, "connection": "schola", "explain": null, "start_percent": 0.76, "width_percent": 0.412}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 613, 1259, 1260, 1261, 1262, 1263, 1265, 1266, 1267, 1268, 1269, 1270, 1271, 1272, 1273)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 304}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 174}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 175}], "start": **********.701172, "duration": 0.05122, "duration_str": "51.22ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:266", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=266", "ajax": false, "filename": "PermissionRegistrar.php", "line": "266"}, "connection": "schola", "explain": null, "start_percent": 1.173, "width_percent": 20.498}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 2 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [2, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.5755122, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "schola", "explain": null, "start_percent": 21.67, "width_percent": 0.32}, {"sql": "select * from `leave_masters` where `school_id` = 1 and exists (select * from `session_years` where `leave_masters`.`session_year_id` = `session_years`.`id` and ((month(`start_date`) <= '01' and year(`start_date`) = '2025') or (month(`start_date`) >= '01' and year(`end_date`) <= '2025')) and `session_years`.`deleted_at` is null) limit 1", "type": "query", "params": [], "bindings": [1, "01", "2025", "01", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 183}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.582329, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:183", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 183}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=183", "ajax": false, "filename": "PayrollController.php", "line": "183"}, "connection": "schola", "explain": null, "start_percent": 21.991, "width_percent": 0.652}, {"sql": "select count(*) as aggregate from `staffs` where exists (select * from `users` where `staffs`.`user_id` = `users`.`id` and `deleted_at` is null and `school_id` = 1)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 201}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.585447, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:201", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 201}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=201", "ajax": false, "filename": "PayrollController.php", "line": "201"}, "connection": "schola", "explain": null, "start_percent": 22.643, "width_percent": 0.56}, {"sql": "select * from `staffs` where exists (select * from `users` where `staffs`.`user_id` = `users`.`id` and `deleted_at` is null and `school_id` = 1) order by `id` desc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.588038, "duration": 0.0033900000000000002, "duration_str": "3.39ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:204", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=204", "ajax": false, "filename": "PayrollController.php", "line": "204"}, "connection": "schola", "explain": null, "start_percent": 23.203, "width_percent": 1.357}, {"sql": "select * from `users` where `users`.`id` in (6, 49, 55, 56, 57, 58, 1325, 1495, 2036, 2063, 2649, 5255, 5355, 6725, 7172, 7258, 7506, 7613, 8577, 8688, 8690)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.5939891, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:204", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=204", "ajax": false, "filename": "PayrollController.php", "line": "204"}, "connection": "schola", "explain": null, "start_percent": 24.56, "width_percent": 0.416}, {"sql": "select * from `expenses` where `expenses`.`staff_id` in (1, 4, 5, 6, 7, 8, 51, 67, 100, 104, 169, 298, 307, 362, 366, 367, 420, 426, 482, 485, 486)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.60022, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:204", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=204", "ajax": false, "filename": "PayrollController.php", "line": "204"}, "connection": "schola", "explain": null, "start_percent": 24.976, "width_percent": 0.692}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = '2025' and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = '2025' and not `type` = 'Full') as `half_leave` from `leaves` where `leaves`.`user_id` in (6, 49, 55, 56, 57, 58, 1325, 1495, 2036, 2063, 2649, 5255, 5355, 6725, 7172, 7258, 7506, 7613, 8577, 8688, 8690) and `status` = 1", "type": "query", "params": [], "bindings": ["01", "2025", "Full", "01", "2025", "Full", 6, 49, 55, 56, 57, 58, 1325, 1495, 2036, 2063, 2649, 5255, 5355, 6725, 7172, 7258, 7506, 7613, 8577, 8688, 8690, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.707323, "duration": 0.00326, "duration_str": "3.26ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:204", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=204", "ajax": false, "filename": "PayrollController.php", "line": "204"}, "connection": "schola", "explain": null, "start_percent": 25.668, "width_percent": 1.305}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= 3000 AND (max_range >= 3000 OR max_range IS NULL)", "type": "query", "params": [], "bindings": [3000, 3000], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.751106, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:229", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=229", "ajax": false, "filename": "PayrollController.php", "line": "229"}, "connection": "schola", "explain": null, "start_percent": 26.973, "width_percent": 0.288}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 486 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [486, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.753237, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 27.261, "width_percent": 0.26}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= 2500 AND (max_range >= 2500 OR max_range IS NULL)", "type": "query", "params": [], "bindings": [2500, 2500], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.758828, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:229", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=229", "ajax": false, "filename": "PayrollController.php", "line": "229"}, "connection": "schola", "explain": null, "start_percent": 27.521, "width_percent": 0.316}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 485 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [485, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.761075, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 27.837, "width_percent": 0.272}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 482 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [482, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.764778, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 28.109, "width_percent": 0.356}, {"sql": "select * from `staffs` where `staffs`.`id` = 426 limit 1", "type": "query", "params": [], "bindings": [426], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.76845, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 28.466, "width_percent": 0.268}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '03' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '03' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 7613", "type": "query", "params": [], "bindings": ["03", 2025, "Full", "03", 2025, "Full", 1, 7613], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.772158, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 28.734, "width_percent": 0.364}, {"sql": "select * from `staffs` where `staffs`.`id` = 426 limit 1", "type": "query", "params": [], "bindings": [426], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.774525, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 29.098, "width_percent": 0.268}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 7613", "type": "query", "params": [], "bindings": ["01", 2025, "Full", "01", 2025, "Full", 1, 7613], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.777112, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 29.366, "width_percent": 0.408}, {"sql": "select * from `staffs` where `staffs`.`id` = 426 limit 1", "type": "query", "params": [], "bindings": [426], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.780014, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 29.774, "width_percent": 0.284}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 7613", "type": "query", "params": [], "bindings": ["04", 2025, "Full", "04", 2025, "Full", 1, 7613], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.782476, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 30.058, "width_percent": 0.308}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 426 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [426, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.784517, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 30.367, "width_percent": 0.328}, {"sql": "select * from `staffs` where `staffs`.`id` = 420 limit 1", "type": "query", "params": [], "bindings": [420], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.7891529, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 30.695, "width_percent": 0.228}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 7506", "type": "query", "params": [], "bindings": ["04", 2025, "Full", "04", 2025, "Full", 1, 7506], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.79159, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 30.923, "width_percent": 0.512}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 420 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [420, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.7939591, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 31.435, "width_percent": 0.252}, {"sql": "select * from `staffs` where `staffs`.`id` = 367 limit 1", "type": "query", "params": [], "bindings": [367], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.796932, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 31.687, "width_percent": 0.24}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '03' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '03' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 7258", "type": "query", "params": [], "bindings": ["03", 2025, "Full", "03", 2025, "Full", 1, 7258], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.79996, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 31.927, "width_percent": 0.356}, {"sql": "select * from `staffs` where `staffs`.`id` = 367 limit 1", "type": "query", "params": [], "bindings": [367], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.802084, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 32.283, "width_percent": 0.244}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 7258", "type": "query", "params": [], "bindings": ["04", 2025, "Full", "04", 2025, "Full", 1, 7258], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.8042471, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 32.528, "width_percent": 0.324}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= 1200 AND (max_range >= 1200 OR max_range IS NULL)", "type": "query", "params": [], "bindings": [1200, 1200], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.806633, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:229", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=229", "ajax": false, "filename": "PayrollController.php", "line": "229"}, "connection": "schola", "explain": null, "start_percent": 32.852, "width_percent": 0.252}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 367 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [367, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.808631, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 33.104, "width_percent": 0.296}, {"sql": "select * from `staffs` where `staffs`.`id` = 366 limit 1", "type": "query", "params": [], "bindings": [366], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.811891, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 33.4, "width_percent": 0.428}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 7172", "type": "query", "params": [], "bindings": ["04", 2025, "Full", "04", 2025, "Full", 1, 7172], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.815016, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 33.828, "width_percent": 0.388}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 366 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [366, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.817074, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 34.216, "width_percent": 0.272}, {"sql": "select * from `staffs` where `staffs`.`id` = 362 limit 1", "type": "query", "params": [], "bindings": [362], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.821018, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 34.489, "width_percent": 0.264}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 6725", "type": "query", "params": [], "bindings": ["01", 2025, "Full", "01", 2025, "Full", 1, 6725], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.8236039, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 34.753, "width_percent": 0.308}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 362 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [362, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.825722, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 35.061, "width_percent": 0.612}, {"sql": "select * from `staffs` where `staffs`.`id` = 307 limit 1", "type": "query", "params": [], "bindings": [307], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.831091, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 35.673, "width_percent": 0.244}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 5355", "type": "query", "params": [], "bindings": ["02", 2025, "Full", "02", 2025, "Full", 1, 5355], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.833845, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 35.917, "width_percent": 0.436}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 307 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [307, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.836783, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 36.353, "width_percent": 0.368}, {"sql": "select * from `staffs` where `staffs`.`id` = 298 limit 1", "type": "query", "params": [], "bindings": [298], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.841841, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 36.722, "width_percent": 0.348}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 5255", "type": "query", "params": [], "bindings": ["02", 2025, "Full", "02", 2025, "Full", 1, 5255], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.844503, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 37.07, "width_percent": 0.28}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 298 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [298, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.84688, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 37.35, "width_percent": 0.64}, {"sql": "select * from `staffs` where `staffs`.`id` = 169 limit 1", "type": "query", "params": [], "bindings": [169], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.852175, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 37.99, "width_percent": 0.252}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 2649", "type": "query", "params": [], "bindings": ["02", 2025, "Full", "02", 2025, "Full", 1, 2649], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.857802, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 38.242, "width_percent": 0.316}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 169 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [169, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.8606522, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 38.559, "width_percent": 0.532}, {"sql": "select * from `staffs` where `staffs`.`id` = 104 limit 1", "type": "query", "params": [], "bindings": [104], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.865534, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 39.091, "width_percent": 0.276}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '10' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '10' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 2063", "type": "query", "params": [], "bindings": ["10", 2024, "Full", "10", 2024, "Full", 1, 2063], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.869338, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 39.367, "width_percent": 0.336}, {"sql": "select * from `staffs` where `staffs`.`id` = 104 limit 1", "type": "query", "params": [], "bindings": [104], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.871521, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 39.703, "width_percent": 0.232}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 2063", "type": "query", "params": [], "bindings": ["02", 2025, "Full", "02", 2025, "Full", 1, 2063], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.8737319, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 39.935, "width_percent": 0.664}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 104 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [104, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.876567, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 40.599, "width_percent": 0.224}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 100 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [100, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.879663, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 40.824, "width_percent": 0.272}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 67 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [67, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.883963, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 41.096, "width_percent": 0.392}, {"sql": "select * from `staffs` where `staffs`.`id` = 51 limit 1", "type": "query", "params": [], "bindings": [51], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.887487, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 41.488, "width_percent": 0.348}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '08' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '08' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 1325", "type": "query", "params": [], "bindings": ["08", 2024, "Full", "08", 2024, "Full", 1, 1325], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.890772, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 41.836, "width_percent": 0.252}, {"sql": "select * from `staffs` where `staffs`.`id` = 51 limit 1", "type": "query", "params": [], "bindings": [51], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.892493, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 42.088, "width_percent": 0.152}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 1325", "type": "query", "params": [], "bindings": ["02", 2025, "Full", "02", 2025, "Full", 1, 1325], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.894711, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 42.24, "width_percent": 0.444}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 51 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [51, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.897037, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 42.684, "width_percent": 0.26}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.900525, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 42.945, "width_percent": 0.252}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["01", 2024, "Full", "01", 2024, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.903777, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 43.197, "width_percent": 0.304}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.906223, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 43.501, "width_percent": 0.268}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2023 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2023 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["12", 2023, "Full", "12", 2023, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.9092119, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 43.769, "width_percent": 0.456}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.912066, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 44.225, "width_percent": 0.24}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["12", 2024, "Full", "12", 2024, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.9144702, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 44.465, "width_percent": 0.324}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.9172032, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 44.789, "width_percent": 0.256}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '11' and year(`date`) = 2023 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '11' and year(`date`) = 2023 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["11", 2023, "Full", "11", 2023, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.919807, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 45.046, "width_percent": 0.264}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.9220152, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 45.31, "width_percent": 0.188}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '06' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '06' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["06", 2024, "Full", "06", 2024, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.925069, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 45.498, "width_percent": 0.272}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.926954, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 45.77, "width_percent": 0.2}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["02", 2024, "Full", "02", 2024, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.929678, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 45.97, "width_percent": 0.468}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.93276, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 46.438, "width_percent": 0.228}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '07' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '07' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["07", 2024, "Full", "07", 2024, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.935113, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 46.666, "width_percent": 0.312}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.937589, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 46.979, "width_percent": 0.284}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '08' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '08' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["08", 2024, "Full", "08", 2024, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.939874, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 47.263, "width_percent": 0.228}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.941783, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 47.491, "width_percent": 0.276}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '11' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '11' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["11", 2024, "Full", "11", 2024, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.944463, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 47.767, "width_percent": 0.348}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.94651, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 48.115, "width_percent": 0.184}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["01", 2025, "Full", "01", 2025, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.949157, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 48.299, "width_percent": 0.496}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.9520118, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 48.795, "width_percent": 0.228}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["02", 2025, "Full", "02", 2025, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.954136, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 49.024, "width_percent": 0.236}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.9560921, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 49.26, "width_percent": 0.176}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '03' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '03' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["03", 2025, "Full", "03", 2025, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.958561, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 49.436, "width_percent": 0.384}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= 2000 AND (max_range >= 2000 OR max_range IS NULL)", "type": "query", "params": [], "bindings": [2000, 2000], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.960438, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:229", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=229", "ajax": false, "filename": "PayrollController.php", "line": "229"}, "connection": "schola", "explain": null, "start_percent": 49.82, "width_percent": 0.168}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 8 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [8, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9621642, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 49.988, "width_percent": 0.404}, {"sql": "select * from `staffs` where `staffs`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.966608, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 50.392, "width_percent": 0.204}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 57", "type": "query", "params": [], "bindings": ["01", 2024, "Full", "01", 2024, "Full", 1, 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.9688, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 50.596, "width_percent": 0.272}, {"sql": "select * from `staffs` where `staffs`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.9708729, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 50.868, "width_percent": 0.228}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2023 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2023 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 57", "type": "query", "params": [], "bindings": ["12", 2023, "Full", "12", 2023, "Full", 1, 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.973401, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 51.097, "width_percent": 0.272}, {"sql": "select * from `staffs` where `staffs`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.9751759, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 51.369, "width_percent": 0.168}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 57", "type": "query", "params": [], "bindings": ["12", 2024, "Full", "12", 2024, "Full", 1, 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.9774451, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 51.537, "width_percent": 0.344}, {"sql": "select * from `staffs` where `staffs`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.9804819, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 51.881, "width_percent": 0.22}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '11' and year(`date`) = 2023 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '11' and year(`date`) = 2023 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 57", "type": "query", "params": [], "bindings": ["11", 2023, "Full", "11", 2023, "Full", 1, 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.982809, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 52.101, "width_percent": 0.292}, {"sql": "select * from `staffs` where `staffs`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.984964, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 52.393, "width_percent": 0.288}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '05' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '05' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 57", "type": "query", "params": [], "bindings": ["05", 2024, "Full", "05", 2024, "Full", 1, 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.987827, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 52.681, "width_percent": 0.272}, {"sql": "select * from `staffs` where `staffs`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.989671, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 52.953, "width_percent": 0.2}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '06' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '06' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 57", "type": "query", "params": [], "bindings": ["06", 2024, "Full", "06", 2024, "Full", 1, 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.9921021, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 53.154, "width_percent": 0.412}, {"sql": "select * from `staffs` where `staffs`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.994874, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 53.566, "width_percent": 0.22}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '07' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '07' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 57", "type": "query", "params": [], "bindings": ["07", 2024, "Full", "07", 2024, "Full", 1, 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.997236, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 53.786, "width_percent": 0.392}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.999768, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 54.178, "width_percent": 0.288}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.002624, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 54.466, "width_percent": 0.292}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.00419, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 54.758, "width_percent": 0.276}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.00673, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 55.034, "width_percent": 0.432}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.008917, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 55.467, "width_percent": 0.272}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.011182, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 55.739, "width_percent": 0.468}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.013459, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 56.207, "width_percent": 0.316}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.016015, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 56.523, "width_percent": 0.388}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= ? AND (max_range >= ? OR max_range IS NULL)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.017497, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 56.911, "width_percent": 0.192}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = ? and `expenses`.`staff_id` is not null and `month` = ? and `year` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.018522, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 57.103, "width_percent": 0.328}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.02185, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 57.432, "width_percent": 0.256}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.023923, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 57.688, "width_percent": 0.488}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.026327, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 58.176, "width_percent": 0.228}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.028916, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 58.404, "width_percent": 0.432}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.030899, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 58.836, "width_percent": 0.136}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.032847, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 58.972, "width_percent": 0.484}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.03545, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 59.457, "width_percent": 0.232}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.03732, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 59.689, "width_percent": 0.368}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0393991, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 60.057, "width_percent": 0.196}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0416598, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 60.253, "width_percent": 0.456}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.043726, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 60.709, "width_percent": 0.168}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.045474, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 60.877, "width_percent": 0.432}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.047764, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 61.309, "width_percent": 0.36}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.050433, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 61.67, "width_percent": 0.416}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0524132, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 62.086, "width_percent": 0.176}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0543082, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 62.262, "width_percent": 0.608}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.056852, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 62.87, "width_percent": 0.2}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.058578, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 63.07, "width_percent": 0.356}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0605512, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 63.426, "width_percent": 0.196}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0628529, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 63.623, "width_percent": 0.432}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.06498, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 64.055, "width_percent": 0.196}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.06763, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 64.251, "width_percent": 0.848}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= ? AND (max_range >= ? OR max_range IS NULL)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.075456, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 65.099, "width_percent": 0.576}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = ? and `expenses`.`staff_id` is not null and `month` = ? and `year` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.077972, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 65.676, "width_percent": 0.336}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0809002, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 66.012, "width_percent": 0.392}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.084197, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 66.404, "width_percent": 0.392}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0860329, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 66.796, "width_percent": 0.24}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.089119, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 67.036, "width_percent": 0.452}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.091418, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 67.488, "width_percent": 0.276}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.093525, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 67.765, "width_percent": 0.304}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.095268, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 68.069, "width_percent": 0.248}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.098078, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 68.317, "width_percent": 0.332}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.099579, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 68.649, "width_percent": 0.18}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.10165, "duration": 0.00261, "duration_str": "2.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 68.829, "width_percent": 1.045}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.105844, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 69.874, "width_percent": 0.224}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1077728, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 70.098, "width_percent": 0.44}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.110137, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 70.538, "width_percent": 0.328}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.113083, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 70.866, "width_percent": 0.452}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.115771, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 71.318, "width_percent": 0.42}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.119174, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 71.738, "width_percent": 0.428}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1213949, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 72.167, "width_percent": 0.312}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.124747, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 72.479, "width_percent": 0.48}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= ? AND (max_range >= ? OR max_range IS NULL)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1266181, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 72.959, "width_percent": 0.272}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = ? and `expenses`.`staff_id` is not null and `month` = ? and `year` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.127969, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 73.231, "width_percent": 0.276}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.131758, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 73.507, "width_percent": 0.336}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1352, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 73.843, "width_percent": 0.408}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1375568, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 74.252, "width_percent": 0.312}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1409202, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 74.564, "width_percent": 0.464}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1434371, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 75.028, "width_percent": 0.464}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1470718, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 75.492, "width_percent": 0.452}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.149405, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 75.944, "width_percent": 0.32}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.152763, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 76.265, "width_percent": 0.568}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.155601, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 76.833, "width_percent": 0.3}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.158942, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 77.133, "width_percent": 0.512}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.161506, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 77.645, "width_percent": 0.292}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1650422, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 77.937, "width_percent": 0.532}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.167803, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 78.47, "width_percent": 0.332}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.171133, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 78.802, "width_percent": 0.712}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.174301, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 79.514, "width_percent": 0.384}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.177811, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 79.898, "width_percent": 0.716}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.181073, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 80.615, "width_percent": 0.62}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.185262, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 81.235, "width_percent": 0.636}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= ? AND (max_range >= ? OR max_range IS NULL)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.188209, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 81.871, "width_percent": 0.348}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = ? and `expenses`.`staff_id` is not null and `month` = ? and `year` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.189936, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 82.219, "width_percent": 0.368}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.194432, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 82.588, "width_percent": 0.356}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.19807, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 82.944, "width_percent": 0.72}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2015119, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 83.664, "width_percent": 0.312}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.204784, "duration": 0.0021000000000000003, "duration_str": "2.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 83.976, "width_percent": 0.84}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.209008, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 84.817, "width_percent": 0.424}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.212853, "duration": 0.00272, "duration_str": "2.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 85.241, "width_percent": 1.089}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.217227, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 86.329, "width_percent": 0.272}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.219625, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 86.602, "width_percent": 0.712}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.223456, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 87.314, "width_percent": 0.28}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2265482, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 87.594, "width_percent": 0.956}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.230829, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 88.551, "width_percent": 0.3}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.233995, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 88.851, "width_percent": 0.828}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2373059, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 89.679, "width_percent": 0.228}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.239233, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 89.907, "width_percent": 0.408}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.241661, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 90.315, "width_percent": 0.248}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.244271, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 90.563, "width_percent": 0.472}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2463892, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 91.036, "width_percent": 0.196}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.24891, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 91.232, "width_percent": 0.844}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2526941, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 92.076, "width_percent": 0.248}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.255415, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 92.324, "width_percent": 0.852}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2586858, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 93.177, "width_percent": 0.228}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.260785, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 93.405, "width_percent": 0.604}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.263828, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 94.009, "width_percent": 0.412}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2663891, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 94.421, "width_percent": 0.536}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2690828, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 94.958, "width_percent": 0.24}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.272017, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 95.198, "width_percent": 0.572}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.274494, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 95.77, "width_percent": 0.268}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.276737, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 96.038, "width_percent": 0.764}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.279588, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 96.802, "width_percent": 0.2}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.281486, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 97.003, "width_percent": 0.62}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.284317, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 97.623, "width_percent": 0.248}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.286332, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 97.871, "width_percent": 0.496}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.288579, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 98.367, "width_percent": 0.28}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2912781, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 98.647, "width_percent": 0.668}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= ? AND (max_range >= ? OR max_range IS NULL)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.295836, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 99.316, "width_percent": 0.4}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = ? and `expenses`.`staff_id` is not null and `month` = ? and `year` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2973769, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 99.716, "width_percent": 0.284}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 13675, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Leave": {"value": 364, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FLeave.php&line=1", "ajax": false, "filename": "Leave.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 227, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Staff": {"value": 105, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FStaff.php&line=1", "ajax": false, "filename": "Staff.php", "line": "?"}}, "App\\Models\\Expense": {"value": 87, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=1", "ajax": false, "filename": "Expense.php", "line": "?"}}, "App\\Models\\User": {"value": 22, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\School": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FSchool.php&line=1", "ajax": false, "filename": "School.php", "line": "?"}}, "App\\Models\\LeaveMaster": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FLeaveMaster.php&line=1", "ajax": false, "filename": "LeaveMaster.php", "line": "?"}}}, "count": 14482, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => payroll-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1161614548 data-indent-pad=\"  \"><span class=sf-dump-note>payroll-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">payroll-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1161614548\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.580765, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "https://schola.test/payroll/1?month=1&order=desc&search=&sort=id&year=2025", "action_name": "payroll.show", "controller_action": "App\\Http\\Controllers\\PayrollController@show", "uri": "GET payroll/{payroll}", "controller": "App\\Http\\Controllers\\PayrollController@show<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=165\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=165\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/PayrollController.php:165-313</a>", "middleware": "web, Role, auth, checkSchoolStatus, status, language", "duration": "2.22s", "peak_memory": "94MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1307475378 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sort</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n  \"<span class=sf-dump-key>order</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>month</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>year</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2025</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1307475378\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-143390370 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-143390370\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-60092444 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">schola.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">k0nsyLuN5aAz6u8lgTG1r68KyFeDp1NRQbXnBoOH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">https://schola.test/payroll</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"742 characters\">XSRF-TOKEN=eyJpdiI6IkdOZk8xeDJvOFJQYXozVFBWcDBvclE9PSIsInZhbHVlIjoidWZoQTc1eUpxS1RNRjNSMm1uWGpZMmd0N0VmSWVkNjBDYTZ5aXNyRjBuSDkrNEVPQ2xxUGNaZDJIMDYwOXNhQk5LaUJCL2pWK2FoV2RaNjdOMVZCOVQwUlNuVmFuOFp1aEp6TVhBY2w5empvd2Rxd0xWZGdUOVUxeUpwZ3UwcW4iLCJtYWMiOiIyNmY5MzczZjg3NDQwZjkyYmEzMjk2MWJiM2Q3YTk3MWJiMGVmZTA1ZDliNTE3MjQ5ZDc1ZTYzZTBmNzZiMGE1IiwidGFnIjoiIn0%3D; schola_saas_school_management_system_session=eyJpdiI6IlFERFdMYVRDWmdoOUlLYXZieXBWOWc9PSIsInZhbHVlIjoiZjlEbGt1WjNaZmFjQ1VHRHFIOTdMNzFxd1JFRXg3d2hTWmc1NkEyS2ZvUGlyRk9ka0dSZkFueGg4Tkp5TkJ2MUhWMmZIaWlrVVlDY29xS1RjOU5PWmtuOGZhUFVBcG14VWk3N1dEMnRmUUwvN3ZNUHhCdW40RkZIYzdsM3NJQUciLCJtYWMiOiIzNDkxNzVhNzg1ZjdiOWM4NzZjMzFhNGMzYmJmZmZmMzg0OTMwODUzZTk5NmM0NDZlMDNjOTllMDYxM2MwODMxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-60092444\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2034785220 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0nsyLuN5aAz6u8lgTG1r68KyFeDp1NRQbXnBoOH</span>\"\n  \"<span class=sf-dump-key>schola_saas_school_management_system_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IQrLeRaEfTugtoRzBcuJ4wOlCeh4PG6VwaBH418U</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2034785220\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1732442200 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 10 Jun 2025 02:47:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1732442200\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-168776362 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0nsyLuN5aAz6u8lgTG1r68KyFeDp1NRQbXnBoOH</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">https://schola.test/payroll</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>landing_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>language</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Language\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Language</span></span> {<a class=sf-dump-ref>#3547</a><samp data-depth=2 class=sf-dump-compact>\n    #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">languages</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n    +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n    #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n    +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n    +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n    +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">English</span>\"\n      \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"7 characters\">en.json</span>\"\n      \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>is_rtl</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">English</span>\"\n      \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"7 characters\">en.json</span>\"\n      \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>is_rtl</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n    +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n    +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">code</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">is_rtl</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n    </samp>]\n  </samp>}\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1749518683</span>\n  </samp>]\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-168776362\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://schola.test/payroll/1?month=1&order=desc&search=&sort=id&year=2025", "action_name": "payroll.show", "controller_action": "App\\Http\\Controllers\\PayrollController@show"}, "badge": null}}