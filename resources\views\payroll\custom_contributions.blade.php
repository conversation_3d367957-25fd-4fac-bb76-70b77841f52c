@extends('layouts.app')

@section('content')
<div class="container">
    <h2>Custom Contributions for {{ $teacher->user->first_name }} {{ $teacher->user->last_name }}</h2>
    <form id="customContributionForm" method="POST" action="{{ route('payroll.customUpdate', $contributions->id) }}">
        @csrf
        @method('PUT')

        <div class="form-group">
            <label for="employee_socso">Employee SOCSO</label>
            <input type="number" step="0.01" class="form-control" id="employee_socso" name="employee_socso" value="{{ $contributions->employee_socso ?? 0 }}">
        </div>

        <div class="form-group">
            <label for="employer_socso">Employer SOCSO</label>
            <input type="number" step="0.01" class="form-control" id="employer_socso" name="employer_socso" value="{{ $contributions->employer_socso ?? 0 }}">
        </div>

        <div class="form-group">
            <label for="employee_eis">Employee EIS</label>
            <input type="number" step="0.01" class="form-control" id="employee_eis" name="employee_eis" value="{{ $contributions->employee_eis ?? 0 }}">
        </div>

        <div class="form-group">
            <label for="employer_eis">Employer EIS</label>
            <input type="number" step="0.01" class="form-control" id="employer_eis" name="employer_eis" value="{{ $contributions->employer_eis ?? 0 }}">
        </div>

        <div class="form-group">
            <label for="employee_epf">Employee EPF</label>
            <input type="number" step="0.01" class="form-control" id="employee_epf" name="employee_epf" value="{{ $contributions->employee_epf ?? 0 }}">
        </div>

        <div class="form-group">
            <label for="employer_epf">Employer EPF</label>
            <input type="number" step="0.01" class="form-control" id="employer_epf" name="employer_epf" value="{{ $contributions->employer_epf ?? 0 }}">
        </div>

        <button type="submit" class="btn btn-primary">Update Contributions</button>
    </form>
</div>
@endsection