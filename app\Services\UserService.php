<?php

namespace App\Services;

use Throwable;
use JsonException;
use App\Models\User;
use App\Models\SystemSetting;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use App\Services\FileSizeLimitService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use App\Repositories\User\UserInterface;
use App\Repositories\Student\StudentInterface;
use App\Repositories\ExtraFormField\ExtraFormFieldsInterface;

class UserService {
    private UserInterface $user;
    private StudentInterface $student;
    private ExtraFormFieldsInterface $extraFormFields;

    public function __construct(UserInterface $user, StudentInterface $student, ExtraFormFieldsInterface $extraFormFields) {
        $this->user = $user;
        $this->student = $student;
        $this->extraFormFields = $extraFormFields;
    }

    /**
     * @param $mobile
     * @return string
     */
    public function makeParentPassword($mobile) {
        return $mobile;
    }

    /**
     * @param $dob
     * @return string
     */
    public function makeStudentPassword($dob) {
        return str_replace('-', '', date('d-m-Y', strtotime($dob)));
    }

    /**
     * @param $first_name
     * @param $last_name
     * @param $email
     * @param $mobile
     * @param $gender
     * @param null $image
     * @return Model|null
     */
    public function createOrUpdateParent($first_name, $last_name, $email, $mobile, $gender, $image = null) {
        $password = $this->makeParentPassword($mobile);
        $schoolId = Auth::getUser()->school_id;

        $parent = array(
            'first_name' => $first_name,
            'last_name'  => $last_name,
            'mobile'     => $mobile,
            'gender'     => $gender,
            'school_id'  => null
        );

        //NOTE : This line will return the old values if the user is already exists
        $user = $this->user->guardian()->withTrashed()->where('email', $email)->first();

        

        if (!empty($image)) {
            if (FileSizeLimitService::checkFileSizeLimit($schoolId)) {
                return ResponseService::errorResponse('storage capacity not enough');}
            $filePath = $image->store('user', 'public');
            $fileSize = $image->getSize();
            $fileSizeKB = round($fileSize / 1024, 2);
            $parent['file_size'] = $fileSizeKB;
            $parent['image'] = $filePath;



        }
        // if (!empty($user)) {
        //     if (isset($parent['image'])) {
        //         if (Storage::disk('public')->exists($user->getRawOriginal('image'))) {
        //             Storage::disk('public')->delete($user->getRawOriginal('image'));
        //         }
        //     }
        // }
        if ($user) {
            // Delete old image if a new one is uploaded and path is set
            if (isset($parent['image'])) {
                $oldImagePath = $user->getRawOriginal('image');
                if ($oldImagePath && Storage::disk('public')->exists($oldImagePath)) {
                    Storage::disk('public')->delete($oldImagePath);
                }
            }
            $user->update($parent);
        } else {
            $parent['password'] = Hash::make($password);
            $parent['email'] = $email;
            $user = $this->user->create($parent);
            $user->assignRole('Guardian');
        }

        return $user;
    }

    /**
     * @param string $first_name
     * @param string $last_name
     * @param string $admission_no
     * @param string|null $mobile
     * @param string $dob
     * @param string $gender
     * @param \Symfony\Component\HttpFoundation\File\UploadedFile|null $image
     * @param int $classSectionID
     * @param string $admissionDate
     * @param null $current_address
     * @param null $permanent_address
     * @param int $sessionYearID
     * @param int $guardianID
     * @param array $extraFields
     * @param int $status
     * @return Model|null
     * @throws JsonException
     * @throws Throwable
     */

    public function createStudentUser(string $first_name, string $last_name, string $admission_no, string|null $mobile, string $dob, string $gender, \Symfony\Component\HttpFoundation\File\UploadedFile|null $image, int $classSectionID, string $admissionDate, $current_address = null, $permanent_address = null, int $sessionYearID, int $guardianID, array $extraFields = [], int $status, string $rfidID = '',$ic_no_2 = '') {
        $password = $this->makeStudentPassword($dob);

        $schoolId = Auth::getUser()->school_id;


        if ($image) {
            if (FileSizeLimitService::checkFileSizeLimit($schoolId)) {
                return ResponseService::errorResponse('storage capacity not enough');
            }
            $image = UploadService::upload($image, 'user');
        }

        if ($image instanceof \Symfony\Component\HttpFoundation\File\UploadedFile) {
            $file_size = round($image->getSize() / 1024, 2);
        } else {
            $file_size = null;
        }

        //Create Student User First
        $user = $this->user->create([
            'first_name'        => $first_name,
            'last_name'         => $last_name,
            'email'             => $admission_no,
            'mobile'            => $mobile,
            'dob'               => date('Y-m-d', strtotime($dob)),
            'gender'            => $gender,
            'password'          => Hash::make($password),
            'school_id'         => Auth::user()->school_id,
            'image'             => $image,
            'file_size'         => $file_size,
            'status'            => $status,
            'rfid_id'           => $rfidID ?? '',
            'current_address'   => $current_address,
            'permanent_address' => $permanent_address,
            'deleted_at'        => $status == 1 ? null : '1970-01-01 01:00:00'
        ]);
        $user->assignRole('Student');

        $roll_number_db = $this->student->builder()->select(DB::raw('max(roll_number)'))->where('class_section_id', $classSectionID)->first();
        $roll_number_db = $roll_number_db['max(roll_number)'];
        $roll_number = $roll_number_db + 1;

        $student = $this->student->create([
            'user_id'          => $user->id,
            'class_section_id' => $classSectionID,
            'ic_no_2'           => $ic_no_2,
            'admission_no'     => $admission_no,
            'roll_number'      => $roll_number,
            'admission_date'   => date('Y-m-d', strtotime($admissionDate)),
            'guardian_id'      => $guardianID,
            'session_year_id'  => $sessionYearID
        ]);
        // dd($image ? round($image->getSize() / 1024, 2) : null);

        // Store Extra Details
        $extraDetails = array();
        foreach ($extraFields as $fields) {
                $data = null;
                if (isset($fields['data'])) {
                    $data = (is_array($fields['data']) ? json_encode($fields['data'], JSON_THROW_ON_ERROR) : $fields['data']);
                }
                $extraDetails[] = array(
                    'student_id'    => $student->user_id,
                    'form_field_id' => $fields['form_field_id'],
                    'data'          => $data,
                );
        }
        if (!empty($extraDetails)) {
            $this->extraFormFields->createBulk($extraDetails);
        }

        $guardian = $this->user->guardian()->where('id', $guardianID)->withTrashed()->firstOrFail();
        $parentPassword = $this->makeParentPassword($guardian->mobile);
        $this->sendRegistrationEmail($guardian, $user, $student->admission_no, $password);
        return $user;
    }

    /**
     * @param $userID
     * @param $first_name
     * @param $last_name
     * @param $mobile
     * @param $dob
     * @param $gender
     * @param $image
     * @param $sessionYearID
     * @param array $extraFields
     * @param null $guardianID
     * @param null $current_address
     * @param null $permanent_address
     * @return Model|null
     * @throws JsonException
     */
    public function updateStudentUser($userID, $first_name, $last_name, $mobile, $dob, $gender, $image, $sessionYearID, array $extraFields = [], $guardianID = null, $current_address = null, $permanent_address = null, $reset_password = null, $classSectionID, $rfid_id = '',$admissionDate,$ic_no_2) {
        $schoolId = Auth::getUser()->school_id;
        if ($image) {
            if (FileSizeLimitService::checkFileSizeLimit($schoolId)) {
                return ResponseService::errorResponse('storage capacity not enough');}
            }
        $studentUserData = array(
            'first_name'        => $first_name,
            'last_name'         => $last_name,
            'mobile'            => $mobile,
            'dob'               => date('Y-m-d', strtotime($dob)),
            'current_address'   => $current_address,
            'permanent_address' => $permanent_address,
            'gender'            => $gender,
            'rfid_id'           => $rfid_id,
            'file_size'         => $image ? round($image->getSize() / 1024, 2) : null,

        );

        if (!empty($current_address)) {
            $studentUserData['current_address'] = $current_address;
        }

        if (!empty($permanent_address)) {
            $studentUserData['permanent_address'] = $permanent_address;
        }

        if (isset($reset_password)) {
            $studentUserData['password'] = Hash::make($this->makeStudentPassword($dob));
        }

        if (!empty($image)) {
            $studentUserData['image'] = UploadService::upload($image, 'user');
        }
        
        //Create Student User First
        $user = User::withTrashed()->find($userID);
        if ($user) {
            $user->update($studentUserData);

            $studentData = array(
                'guardian_id'     => $guardianID,
                'admission_date'  => date('Y-m-d', strtotime($admissionDate)),
                'session_year_id' => $sessionYearID,
                'class_section_id' => $classSectionID,                
                'ic_no_2'             => $ic_no_2,
            );
            $student = $this->student->update($user->student->id, $studentData);
        }
        //$user = $this->user->update($userID, $studentUserData);
        // dd( $image ? round($image->getSize() / 1024, 2) : null);.

        $extraDetails = [];
        foreach ($extraFields as $fields) {
            if ($fields['input_type'] == 'file') {
                if (isset($fields['data']) && $fields['data'] instanceof UploadedFile) {
                    $extraDetails[] = array(
                    'id'            => $fields['id'] ?? null,
                    'student_id'    => $student->user_id,
                    'form_field_id' => $fields['form_field_id'],
                    'data'          => $fields['data']
                    );
                }
            } else {
                $data = null;
                if (isset($fields['data'])) {
                    $data = (is_array($fields['data']) ? json_encode($fields['data'], JSON_THROW_ON_ERROR) : $fields['data']);
                }
                $extraDetails[] = array(
                    'id'            => $fields['id'] ?? null,
                    'student_id'    => $student->user_id,
                    'form_field_id' => $fields['form_field_id'],
                    'data'          => $data,
                );
            }
        }
        $uniqueBy = ['student_id', 'form_field_id'];
        if (isset($fields['id'])) {
            $uniqueBy[] = 'id';
        }
        $this->extraFormFields->upsert($extraDetails, $uniqueBy, ['data']);
        $user->assignRole('Student');
        DB::commit();
        return $user;
    }

    /**
     * @param $email
     * @param $name
     * @param $plainTextPassword
     * @param $childName
     * @param $childAdmissionNumber
     * @param $childPlainTextPassword
     * @return void
     * @throws Throwable
     */
    public function sendRegistrationEmail($guardian, $child, $childAdmissionNumber, $childPlainTextPassword,$type = null) {
        try {
            $school_name = Auth::user()->school->name;
            $school_email = DB::table('users as u')
                            ->join('model_has_roles as mhr','mhr.model_id','=','u.id')
                            ->join('roles as r','r.id','=','mhr.role_id')
                            ->where('u.school_id',Auth::user()->school_id)
                            ->where('r.id','=',2)
                            ->value('u.email');
            // dd($school_email);

            $email_body = $this->replacePlaceholders($guardian, $child, $childAdmissionNumber, $childPlainTextPassword,$type);
            $data = [
                'subject'                => 'Welcome to ' . $school_name,
                'email'                  => $guardian->email,
                'email_body'             => $email_body,
                'school_email'           => $school_email
            ];

            Mail::send('students.email', $data, static function ($message) use ($data) {
                $message->to($data['email'])->subject($data['subject'])->cc($data['school_email']);
                // $message->cc($school_email);
            });
        } catch (\Throwable $th) {

        }

    }

    private function replacePlaceholders($guardian, $child, $childAdmissionNumber, $childPlainTextPassword,$type = null)
    {
        $cache = app(CachingService::class);
        $schoolSettings = $cache->getSchoolSettings();
        $systemSettings = $cache->getSystemSettings();

        // Get school-specific app links directly from database
        $systemSettingsBySchool = SystemSetting::where('school_id', Auth::user()->school_id)->get();
        $schoolAppLinks = $systemSettingsBySchool->pluck('data', 'name')->toArray();

        $templateContent = $schoolSettings['email-template-parent'] ?? '';

        $others = '';
        if($type == 'resend'){
            $childs = DB::table('students as s')
            ->join('users as u','u.id','=','s.user_id')
            ->select(DB::raw('CONCAT(u.first_name," ",u.last_name) as full_name'),'u.dob','s.admission_no','u.email')
            ->where('s.guardian_id',$guardian->id)
            ->where('s.id','!=',$child->id)
            ->whereNull('u.deleted_at')
            ->get();
            if($childs){
                foreach($childs as $c){
                    $childPassword = $this->makeStudentPassword($c->dob);
                    $others .= "
                    <li><strong>Name:</strong> {$c->full_name}</li>
                    <li><strong>Admission No.:</strong> {$c->admission_no}</li>
                    <li><strong>Student ID:</strong> {$c->email}</li>
                    <li><strong>Password:</strong> {$childPassword}</li>
                    <br>
                    ";
                }
            }
        }
        // Define the placeholders and their replacements
        $placeholders = [
            '{parent_name}' => $guardian->full_name,
            '{email}' => $guardian->email,
            '{password}' => $guardian->mobile,
            '{school_name}' => $schoolSettings['school_name'],

            '{child_name}' => $child->full_name,
            '{grno}' => $child->email,
            '{child_password}' => $childPlainTextPassword,
            '{admission_no}' => $childAdmissionNumber,
            '{ic_no_2}' => $child->ic_no_2 ?? '',
            '{others}'       => $others,
            
            '{support_email}' => $schoolSettings['school_email'] ?? '',
            '{support_contact}' => $schoolSettings['school_phone'] ?? '',
            
            '{android_app}' => $schoolAppLinks['app_link'] ?? $systemSettings['app_link'] ?? '',
            '{ios_app}' => $schoolAppLinks['ios_app_link'] ?? $systemSettings['ios_app_link'] ?? '',
            '{huawei_app}' => $schoolAppLinks['huawei_app_link'] ?? $systemSettings['huawei_app_link'] ?? '',
            
            // Add more placeholders as needed
        ];        

        // Replace the placeholders in the template content
        foreach ($placeholders as $placeholder => $replacement) {
            $templateContent = str_replace($placeholder, $replacement, $templateContent);
        }

        return $templateContent;
    }

    public function sendStaffRegistrationEmail($user, $password)
    {
        $cache = app(CachingService::class);
        $schoolSettings = $cache->getSchoolSettings();
        $email_body = $this->replaceStaffPlaceholders($user, $password, $schoolSettings);
        $data = [
            'subject'     => 'Welcome to ' . $schoolSettings['school_name'],
            'email'       => $user->email,
            'email_body'  => $email_body
        ];

        Mail::send('teacher.email', $data, static function ($message) use ($data) {
            $message->to($data['email'])->subject($data['subject']);
        });
    }

    private function replaceStaffPlaceholders($user, $password, $schoolSettings)
    {

        $cache = app(CachingService::class);
        $systemSettings = $cache->getSystemSettings();
        // Get school-specific app links directly from database
        $systemSettingsBySchool = SystemSetting::where('school_id', Auth::user()->school_id)->get();
        $schoolAppLinks = $systemSettingsBySchool->pluck('data', 'name')->toArray();
        

        $templateContent = $schoolSettings['email-template-staff'] ?? '';
        // Define the placeholders and their replacements
        $placeholders = [
            '{full_name}' => $user->full_name,
            '{email}' => $user->email,
            '{password}' => $password,
            '{school_name}' => $schoolSettings['school_name'],
            
            '{support_email}' => $schoolSettings['school_email'] ?? '',
            '{support_contact}' => $schoolSettings['school_phone'] ?? '',

            '{url}' => url('/'),

            '{android_app}' => $schoolAppLinks['teacher_app_link'] ?? $systemSettings['teacher_app_link'] ?? '',
            '{ios_app}' => $schoolAppLinks['teacher_ios_app_link'] ?? $systemSettings['teacher_ios_app_link'] ?? '',
            '{huawei_app}' => $schoolAppLinks['teacher_huawei_app_link'] ?? $systemSettings['teacher_huawei_app_link'] ?? '',

            // Add more placeholders as needed
        ];

        // Replace the placeholders in the template content
        foreach ($placeholders as $placeholder => $replacement) {
            $templateContent = str_replace($placeholder, $replacement, $templateContent);
        }

        return $templateContent;
    }

    /* Backup Code for Student CreateOrUpdate
    public function createOrUpdateStudentUser($first_name, $last_name, $admission_no, $mobile, $dob, $gender, $image, $classSectionID, $admissionDate, array $extraFields = [], $rollNumber = null, $guardianID = null) {
        $password = $this->makeStudentPassword($dob);
        $userExists = $this->user->builder()->where('email', $admission_no)->first();
        if (!empty($rollNumber)) {
            $rollNumber = $this->student->builder()->select(DB::raw('max(roll_number)'))->where('class_section_id', $classSectionID)->first();
            $rollNumber = $rollNumber['max(roll_number)'];
            ++$rollNumber;
        }
        $studentUserData = array(
            'first_name' => $first_name,
            'last_name'  => $last_name,
            'email'      => $admission_no,
            'mobile'     => $mobile,
            'dob'        => date('Y-m-d', strtotime($dob)),
            'gender'     => $gender,
        );

        $studentData = array(
            'class_section_id' => $classSectionID,
            'admission_no'     => $admission_no,
            'roll_number'      => $rollNumber,
            'guardian_id'      => $guardianID
        );


        if (!$userExists) {
            //Create Student User
            $studentUserData = array_merge($studentUserData, [
                'password'  => Hash::make($password),
                'school_id' => Auth::user()->school_id,
                'image'     => $image
            ]);
            $user = $this->user->create($studentUserData);
            $user->assignRole('Student');

            $sessionYear = $this->sessionYear->default();
            $studentData = array_merge($studentData, [
                'user_id'         => $user->id,
                'admission_date'  => date('Y-m-d', strtotime($admissionDate)),
                'session_year_id' => $sessionYear->id
            ]);
            $student = $this->student->create($studentData);

        } else {
            //Update Student User
            if ($image) {
                $studentUserData['image'] = $image;
            }
            $user = $this->user->update($userExists->id, $studentUserData);
            $student = $this->student->update($user->student->id, $studentData);
        }

        // UPSERT EXTRA FIELDS
        $extraDetails = [];
        foreach ($extraFields as $fields) {
            // IF form_field_typ is file, and it's value is empty then skip that array
            if ($fields['input_type'] == 'file' && !isset($fields['data'])) {
                continue;
            }
            $data = null;
            if (isset($fields['data'])) {
                $data = (is_array($fields['data']) ? json_encode($fields['data'], JSON_THROW_ON_ERROR) : $fields['data']);
            }
            $extraDetails[] = array(
                'id'            => $fields['id'] ?? null,
                'student_id'    => $student->id,
                'form_field_id' => $fields['form_field_id'],
                'data'          => $data,
            );
        }

        $this->extraFormFields->upsert($extraDetails, ['student_id', 'form_field_id'], ['data']);
        DB::commit();

        if (!$userExists) {
            // Send Registration Email only if user is new. Already Existing user's parent will not receive email

                $guardian = $this->user->findById($guardianID);
                $password = $this->makeParentPassword($first_name, $mobile);
                $this->sendRegistrationEmail($guardian->email, $guardian->full_name, $password, $user->full_name, $student->admission_no, $password);
        }
        return $user;
    }*/
}
