<?php

namespace App\Http\Controllers;

use App\Exports\StudentDataExport;
use App\Imports\StudentsImport;
use App\Repositories\ClassSection\ClassSectionInterface;
use App\Repositories\FormField\FormFieldsInterface;
use App\Repositories\SchoolSetting\SchoolSettingInterface;
use App\Repositories\SessionYear\SessionYearInterface;
use App\Repositories\Student\StudentInterface;
use App\Repositories\Subscription\SubscriptionInterface;
use App\Repositories\SubjectTeacher\SubjectTeacherInterface;
use App\Repositories\User\UserInterface;
use App\Services\BootstrapTableService;
use App\Services\CachingService;
use App\Services\FeaturesService;
use App\Services\ResponseService;
use App\Services\SubscriptionService;
use App\Services\UserService;
use App\Models\StudentProgress;
use App\Helpers\EInvoiceHelper;

use App\Models\Students;
use App\Models\StudentSubject;
use App\Services\FileSizeLimitService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Validation\ValidationException;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\User;
use PDF;
use Throwable;
use TypeError;
use Exception;

class StudentController extends Controller {
    private StudentInterface $student;
    private UserInterface $user;
    private ClassSectionInterface $classSection;
    private FormFieldsInterface $formFields;
    private SessionYearInterface $sessionYear;
    private CachingService $cache;
    private SubscriptionInterface $subscription;
    private SchoolSettingInterface $schoolSettings;
    private SubscriptionService $subscriptionService;
    private SubjectTeacherInterface $subjectTeacher;

    public function __construct(StudentInterface $student, UserInterface $user, ClassSectionInterface $classSection, FormFieldsInterface $formFields, SessionYearInterface $sessionYear, CachingService $cachingService, SubscriptionInterface $subscription, SchoolSettingInterface $schoolSettings, SubscriptionService $subscriptionService,SubjectTeacherInterface $subjectTeacher) {
        $this->student = $student;
        $this->user = $user;
        $this->classSection = $classSection;
        $this->formFields = $formFields;
        $this->sessionYear = $sessionYear;
        $this->cache = $cachingService;
        $this->subscription = $subscription;
        $this->schoolSettings = $schoolSettings;
        $this->subscriptionService = $subscriptionService;
        $this->subjectTeacher = $subjectTeacher;
    }

    public function index() {
        ResponseService::noPermissionThenRedirect('student-list');
        $class_sections = $this->classSection->all(['*'], ['class', 'class.stream', 'section', 'medium']);
        $extraFields = $this->formFields->defaultModel()->orderBy('rank')->get();
        $sessionYears = $this->sessionYear->all();
        $features = FeaturesService::getFeatures();
        

        return view('students.details', compact('class_sections', 'extraFields', 'sessionYears', 'features'));
    }

    public function create() {
        ResponseService::noPermissionThenRedirect('student-create');
        $class_sections = $this->classSection->all(['*'], ['class', 'class.stream', 'section', 'medium']);
        $sessionYear = $this->cache->getDefaultSessionYear();
        $get_student = $this->student->builder()->latest('id')->withTrashed()->pluck('id')->first();
        // $admission_no = $sessionYear->name . Auth::user()->school_id . ($get_student + 1);
        $studentList = DB::select('SELECT COUNT(id) AS total FROM students WHERE school_id = '.Auth::user()->school_id);
        $totalStudent = $studentList[0]->total + 1;
        $admission_no = $sessionYear->name.Auth::user()->school_id.str_pad(($totalStudent), 4, '0', STR_PAD_LEFT);
        while(true){
            $studentList = DB::select('SELECT id FROM users WHERE email = ?',[$admission_no]);
            if(COUNT($studentList)){
                $totalStudent = $totalStudent + 1;
                $admission_no = $sessionYear->name.Auth::user()->school_id.str_pad(($totalStudent + 1), 4, '0', STR_PAD_LEFT);
            }
            else{
                break;
            }
        }
        
        $extraFields = $this->formFields->defaultModel()->orderBy('rank')->get();
        $sessionYears = $this->sessionYear->all();
        $features = FeaturesService::getFeatures();
        return view('students.create', compact('class_sections', 'admission_no', 'extraFields', 'sessionYears', 'features'));
    }

    public function store(Request $request) {
        ResponseService::noPermissionThenRedirect(['student-create']);
        $request->validate([
            'first_name'          => 'required',
            'last_name'           => 'required',
            'mobile'              => 'nullable|regex:/^([0-9\s\-\+\(\)]*)$/',
            'image'               => 'nullable|mimes:jpeg,png,jpg,svg|image|max:2048',
            'dob'                 => 'required',
            'class_section_id'    => 'required|numeric',
            /*NOTE : Unique constraint is used because it's not school specific*/
            'admission_no'        => 'required|unique:users,email',
            'admission_date'      => 'required',
            'session_year_id'     => 'required|numeric',
            'guardian_email'      => 'required|email',
            'guardian_first_name' => 'required|string',
            'guardian_last_name'  => 'required|string',
            'guardian_mobile'     => 'required|numeric',
            'guardian_gender'     => 'required|in:male,female',
            'guardian_image'      => 'nullable|mimes:jpg,jpeg,png|max:4096',
            'status'              => 'nullable|in:0,1',
            'name'                => 'nullable',            
            'tax_identification_number' => 'nullable',
            'address'             => 'nullable',
            'city'                => 'nullable',
            'postal_code'         => 'nullable',
            'country'             => 'nullable',
            'state'               => 'nullable',
            // 'file_Size'           => 'nullable',
            'contact_no'          => 'nullable',
            'rfid_id'             => 'nullable',
            'ic_no_2'             => 'nullable',
        ]);
        $rfidCheck = DB::select('SELECT COUNT(*) as count FROM users WHERE rfid_id = ?', [$request->rfid_id]);
        
        if ($rfidCheck[0]->count > 0) {
            return ResponseService::errorResponse('Invalid, RFID ID has been taken.');
        }

        if ((isset($request->ic_no))){
            $regex = '/^[0-9]{6}[0-9]{2}[0-9]{4}$/';
            if (!preg_match($regex, $request->ic_no)) {
                ResponseService::errorResponse("Invalid IC Number format. Use format: XXXXXXYYZZZZ", null, null);
            }
        } elseif (isset($request->tax_identification_number)){
            $isTINError = false;
            if(strlen($request->tax_identification_number) >= 11 && strlen($request->tax_identification_number) <= 13){
            }
            else{
                $isTINError = true;
            }
            if($isTINError) {            
                ResponseService::errorResponse("Invalid TIN format.\nIndividual TIN: IGXXXXXXXXX\n11 to 13 characters", null, null);
            }
        }

        try {
            DB::beginTransaction();

            // Check free trial package
            $today_date = Carbon::now()->format('Y-m-d');
            $subscription = $this->subscription->builder()->doesntHave('subscription_bill')->whereDate('start_date', '<=', $today_date)->where('end_date', '>=', $today_date)->whereHas('package', function ($q) {
                $q->where('is_trial', 1);
            })->first();
            

            // If free trail package
            if ($subscription) {
                $systemSettings = $this->cache->getSystemSettings();
                $student = $this->user->builder()->role('Student')->withTrashed()->count();
                if ($student >= $systemSettings['student_limit']) {
                    $message = "The free trial allows only " . $systemSettings['student_limit'] . " students.";
                    ResponseService::errorResponse($message);
                }
            } else {
                // Regular package? Check Postpaid or Prepaid
                $subscription = $this->subscriptionService->active_subscription(Auth::user()->school_id);
                // If prepaid plan check student limit
                if ($subscription && $subscription->package_type == 0) {
                    $status = $this->subscriptionService->check_user_limit($subscription, "Students");
                    
                    if (!$status) {
                        ResponseService::errorResponse('You reach out limits');
                    }
                }
            }
            

            // Get the user details from the guardian details & identify whether that user is guardian or not. if not the guardian and has some other role then show appropriate message in response
            $guardianUser = $this->user->builder()->whereHas('roles', function ($q) {
                $q->where('name', '!=', 'Guardian');
            })->where('email', $request->guardian_email)->withTrashed()->first();
            if ($guardianUser) {
                ResponseService::errorResponse("Email ID is already taken for Other Role");
            }
            $userService = app(UserService::class);
            $sessionYear = $this->sessionYear->findById($request->session_year_id);
       
            $guardian = $userService->createOrUpdateParent($request->guardian_first_name, $request->guardian_last_name, $request->guardian_email, $request->guardian_mobile, $request->guardian_gender, $request->guardian_image);
            $userService->createStudentUser($request->first_name, $request->last_name, $request->admission_no, $request->mobile, $request->dob, $request->gender, $request->image, $request->class_section_id, $request->admission_date, $request->current_address, $request->permanent_address, $sessionYear->id, $guardian->id, $request->extra_fields ?? [], $request->status ?? 0, $request->rfid_id ?? '',$request->ic_no_2 ?? '');
            $e_invoice_data = [
                'name'                => $request->name,
                'ic_no'               => $request->ic_no,
                'tax_identification_number' => preg_replace('/\D/', '', $request->tax_identification_number),
                'email'               => $request->email,
                'address'             => $request->address,
                'city'                => $request->city,
                'postal_code'         => $request->postal_code,
                'country'             => $request->country,
                'state'               => $request->state,
                'contact_no'          => $request->contact_no,
                'guardian_id'         => $guardian->id,
                'school_id'           => Auth::user()->school_id,
                'contact_no'          => $request->contact_no ?? '',
            ];
            DB::table('e_invoice_guardian')->insert($e_invoice_data);

            //Change parent to inactive if student is inactive
            if($request->status != 1){
                $checkActiveStudent = DB::table('students as s')->join('users as u','u.id','=','s.user_id')->where('s.guardian_id',$guardian->id)->whereNull('u.deleted_at')->count();
                if($checkActiveStudent == 0){
                    DB::table('users')->where('id',$guardian->id)->update(['deleted_at' => now()]);
                }
            }

           

            DB::commit();
            ResponseService::successResponse('Data Stored Successfully');
        } catch (Throwable $e) {
            // IF Exception is TypeError and message contains Mail keywords then email is not sent successfully
            if ($e instanceof TypeError && Str::contains($e->getMessage(), [
                    'Failed',
                    'Mail',
                    'Mailer',
                    'MailManager'
                ])) {
                DB::commit();
                ResponseService::warningResponse("Student Registered successfully. But Email not sent.");
            } else {
                DB::rollBack();
                ResponseService::logErrorResponse($e, "Student Controller -> Store method");
                ResponseService::errorResponse();
            }

        }
    }

    public function update($id, Request $request) {
        ResponseService::noAnyPermissionThenSendJson(['student-create', 'student-edit']);

        $rfidCheck = DB::select('SELECT COUNT(*) as count FROM users WHERE rfid_id = ? AND id != ? ', [$request->rfid_id, $id]);
        if ($rfidCheck[0]->count > 0) {
            return ResponseService::errorResponse('Invalid, RFID ID has been taken.');
        }

        $rules = [
            'first_name'      => 'required',
            'last_name'       => 'required',
            'mobile'          => 'nullable|regex:/^([0-9\s\-\+\(\)]*)$/',
            'image'           => 'nullable|mimes:jpeg,png,jpg,svg|image|max:2048',
            'dob'             => 'required',
            'admission_date'  => 'required',
            'session_year_id' => 'required|numeric',
            'guardian_email'  => 'required|email',
            'ic_no_2'         => 'nullable',
            'skip_package_credit' => 'nullable|boolean'  // Add this validation rule
        ];
        if (is_numeric($request->guardian_id)) {
            $rules['guardian_email'] = 'required|email|unique:users,email,' . $request->guardian_id;
        }
        $request->validate($rules);
        if($request->extra_fields != null){
            foreach($request->extra_fields as $extraFields){
                if($extraFields['input_type'] == "file" && empty($extraFields['data'])){
                    if ($extraFields['input_type'] == "file" && $extraFields['file_exists'] == "false"){
                        $formName = DB::select("SELECT name, is_required FROM form_fields WHERE id = ?", [$extraFields['form_field_id']]);
                        if($formName && $formName[0]->is_required == 1) {
                            return ResponseService::errorResponse($formName[0]->name.' field is required');
                        }
                    }
                }
            }
        }
        try {
            DB::beginTransaction();
            $userService = app(UserService::class);
            $sessionYear = $this->sessionYear->findById($request->session_year_id);
            $guardian = $userService->createOrUpdateParent($request->guardian_first_name, $request->guardian_last_name, $request->guardian_email, $request->guardian_mobile, $request->guardian_gender, $request->guardian_image);
            
            // Update Student User
            $userService->updateStudentUser($id, $request->first_name, $request->last_name, $request->mobile, $request->dob, $request->gender, $request->image, $sessionYear->id, $request->extra_fields ?? [], $guardian->id, $request->current_address, $request->permanent_address, $request->reset_password, $request->class_section_id, $request->rfid_id,$request->admission_date, $request->ic_no_2);

            // Update credit_status in students table
            DB::table('students')
                ->where('user_id', $id)
                ->update([
                    'credit_status' => $request->has('skip_package_credit') ? 1 : 0
                ]);

            DB::commit();
            ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Student Controller -> Update method");
            ResponseService::errorResponse();
        }
    }
    
    public function show(Request $request) {
        ResponseService::noPermissionThenRedirect('student-list');
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'ASC');
        $search = request('search');

        $sql = $this->student->builder()->with('user.extra_student_details.form_field', 'guardian', 'class_section.class.stream', 'class_section.section', 'class_section.medium')
            ->where(function ($query) use ($search) {
                $query->when($search, function ($query) use ($search) {
                    $query->where(function ($query) use ($search) {
                        $query->where('user_id', 'LIKE', "%$search%")
                            ->orWhere('class_section_id', 'LIKE', "%$search%")
                            ->orWhere('admission_no', 'LIKE', "%$search%")
                            ->orWhere('roll_number', 'LIKE', "%$search%")
                            ->orWhere('ic_no_2', 'LIKE', "%$search%")
                            // ->orWhere('admission_date', 'LIKE', date('Y-m-d', strtotime("%$search%")))
                            ->orWhereHas('user', function ($q) use ($search) {
                                $q->where('first_name', 'LIKE', "%$search%")
                                    ->orwhere('last_name', 'LIKE', "%$search%")
                                    ->orwhere('email', 'LIKE', "%$search%")
                                    ->orwhere('dob', 'LIKE', "%$search%")
                                    ->orwhere('mobile', 'LIKE', "%$search%")
                                    ->orwhere('rfid_id','LIKE',"%$search%")
                                    ->orWhereRaw("concat(first_name,' ',last_name) LIKE '%" . $search . "%'");
                            })->orWhereHas('guardian', function ($q) use ($search) {
                                $q->where('first_name', 'LIKE', "%$search%")
                                    ->orwhere('last_name', 'LIKE', "%$search%")
                                    ->orwhere('email', 'LIKE', "%$search%")
                                    ->orwhere('dob', 'LIKE', "%$search%")
                                    ->orwhere('mobile', 'LIKE', "%$search%")
                                    ->orWhereRaw("concat(first_name,' ',last_name) LIKE '%" . $search . "%'");
                            });
                    });
                });
                //class filter data
            })->when(request('class_id') != null, function ($query) {
                $classId = request('class_id');
                $query->where(function ($query) use ($classId) {
                    $query->where('class_section_id', $classId);
                });
            })->when(request('session_year_id') != null, function ($query) {
                $sessionYearID = request('session_year_id');
                $query->where(function ($query) use ($sessionYearID) {
                    $query->where('session_year_id', $sessionYearID);
                });
            });

        if ($request->show_deactive == 1) {
            $sql = $sql->whereHas('user', function ($query) {
                $query->where('status', 0)->withTrashed();
            });
        } else if($request->show_deactive == 2){
            $sql = $sql->whereHas('user', function ($query) {
                $query->where('status', 2)->withTrashed();
            });
        } else {
            $sql = $sql->whereHas('user', function ($query) {
                $query->where('status', 1);
            });
        }

        if ($request->exam_id && $request->exam_id != 'data-not-found') {
            $sql = $sql->has('exam_result')->whereHas('exam_result', function($q) use($request) {
                $q->where('exam_id',$request->exam_id);
            });
        }
        $total = $sql->count();
        if (!empty($request->class_id)) {
            $sql = $sql->orderBy('roll_number', 'ASC');
        } else {
            $sql = $sql->orderBy($sort, $order);
        }
        $sql->skip($offset)->take($limit);
        $res = $sql->get();
        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $no = 1;
        foreach ($res as $row) {
            $operate = '';
            if (!$request->show_deactive) {
                if (Auth::user()->can('student-edit')) {
                    $operate .= BootstrapTableService::editButton(route('students.update', $row->user->id, ['data-id' => $row->id]));
                    $operate .= BootstrapTableService::button('fa fa-exclamation-triangle', route('student.change-status', $row->user_id), ['btn-gradient-info', 'deactivate-student'], ['title' => __('inactive')]);
                }
            } else {
                if (Auth::user()->can('student-edit')) {
                    $operate .= BootstrapTableService::editButton(route('students.update', $row->user->id, ['data-id' => $row->id]));
                }
                $operate .= BootstrapTableService::button('fa fa-check', route('student.change-status', $row->user_id), ['btn-gradient-success', 'activate-student'], ['title' => __('active')]);
            }

            if (Auth::user()->can('student-delete')) {
                $operate .= BootstrapTableService::trashButton(route('student.trash', $row->user_id));
            }
            $student_gender = $row->user->gender;
            $guardian_gender = $row->guardian->gender;
            $row->user->gender = trans(strtolower($row->user->gender));
            $row->guardian->gender = trans(strtolower($row->guardian->gender));
            $tempRow = $row->toArray();
            $tempRow['no'] = $no++;
            $tempRow['eng_student_gender'] = $student_gender;
            $tempRow['eng_guardian_gender'] = $guardian_gender;


            if(!$request->show_deactive){
                $e_invoice_exist = DB::table('e_invoice_guardian')->join('students','students.guardian_id','=','e_invoice_guardian.guardian_id')->where('e_invoice_guardian.school_id',Auth::user()->school_id)->where('students.id', $row->id)->select('e_invoice_guardian.guardian_id','e_invoice_guardian.status')->first();
                
                if ($e_invoice_exist) {
                    $incompleteDataCount = DB::table('e_invoice_guardian')
                        ->where('guardian_id', $e_invoice_exist->guardian_id)
                        ->where('school_id',Auth::user()->school_id)
                        ->where(function ($query) {
                            $query->whereNull('name')
                                ->orWhere('name', '')
                                ->orWhereNull('ic_no')
                                ->orWhere('ic_no', '')
                                ->orWhereNull('tax_identification_number')
                                ->orWhere('tax_identification_number', '')
                                ->orWhereNull('address')
                                ->orWhere('address', '')
                                ->orWhereNull('city')
                                ->orWhere('city', '')
                                ->orWhereNull('postal_code')
                                ->orWhere('postal_code', '')
                                ->orWhereNull('country')
                                ->orWhere('country', '')
                                ->orWhereNull('state')
                                ->orWhere('state', '');
                        })
                        ->count();
                }

                if (!isset($incompleteDataCount)) {
                    $incompleteDataCount = 0;
                }

                if($incompleteDataCount === 0 && $e_invoice_exist && $e_invoice_exist->status == 1) {
                    $e_invoice_status = true;
                } else {
                    $e_invoice_status = false;
                }
                $e_invoice_exist = DB::table('e_invoice_guardian')
                    ->join('students','students.guardian_id','=','e_invoice_guardian.guardian_id')
                    ->where('e_invoice_guardian.school_id',Auth::user()->school_id)
                    ->where('students.id', $row->id)
                    ->select('e_invoice_guardian.guardian_id','e_invoice_guardian.status','e_invoice_guardian.ic_no', 'e_invoice_guardian.tax_identification_number')
                    ->first();

                if((isset($e_invoice_exist) && $e_invoice_exist->status != 1) || !isset($e_invoice_exist)){
                    $operate .= BootstrapTableService::button('fa fa-solid fa-file-lines',
                    url("{{ route('students.validate-tin') }}".$row->user_id),
                    ['btn-gradient-warning','validate-einvoice'],
                    ['title' => __('Validate E-invoice'),
                    'data-ic-no' => $e_invoice_exist->ic_no ?? '',
                    'data-tin' => $e_invoice_exist->tax_identification_number ?? '']
                    );
                }
                $tempRow['e_invoice_status'] = $e_invoice_status;
            }

            // $tempRow['user.dob'] = format_date($row->user->dob);
            // $tempRow['admission_date'] = format_date($row->admission_date);
            
            // $tempRow['extra_fields'] = $row->user->extra_student_details()->has('form_field')->with('form_field')->get();
            $tempRow['extra_fields'] = $row->user->extra_student_details;
            foreach ($row->user->extra_student_details as $key => $field) {
                $data = '';
                if ($field->form_field->type == 'checkbox') {
                    $data = json_decode($field->data);
                } else if($field->form_field->type == 'file') {
                    $data = '<a href="'.Storage::url($field->data).'" target="_blank">DOC</a>';
                } else if($field->form_field->type == 'dropdown') {
                    $data = $field->form_field->default_values;
                    $data = $data[$field->data] ?? '';
                } else {
                    $data = $field->data;
                }
                $tempRow[$field->form_field->name] = $data;
            }
            
            $tempRow['operate'] = $operate;

            if(!Auth::user()->hasRole('School Admin') && !(Auth::user()->can('student-create') && Auth::user()->can('student-delete') && Auth::user()->can('student-edit') && Auth::user()->can('student-list'))) {
                $tempRow['guardian.email'] = '**********';
                $tempRow['guardian.mobile'] = '**********';
                $tempRow['user']['mobile'] = '**********';
            }
            $rows[] = $tempRow;
        }

        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    }

    public function destroy($user_id) {
        ResponseService::noPermissionThenSendJson('student-delete');
        try {            
            // Attempt to find the video setting by ID
            $videoSetting = DB::table('video_settings')->where('id', $id)->first();
    
            
            if (!$videoSetting) {
                // Return an error response if the video setting is not found
                return ResponseService::errorResponse('Video setting not found');
            }
    
            // Perform the deletion
            DB::table('video_settings')->where('id', $id)->delete();
    
            $this->user->deleteById($user_id);
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Student Controller -> Delete method");
            // Log the error
            ResponseService::logErrorResponse($e, "VideoSettingsController -> destroyVideoSettings method");
    
            ResponseService::errorResponse();
        }
    }

    public function changeStatus($userId) {
        try {
            // ResponseService::noFeatureThenSendJson('Student Management');
            ResponseService::noPermissionThenRedirect('student-edit');
            DB::beginTransaction();
            $user = $this->user->findTrashedById($userId);
            $isActivating = $user->status == 0 || $user->status == 2;
            if ($user->status == 0 || $user->status == 2) {
                $subscription = $this->subscriptionService->active_subscription(Auth::user()->school_id);
                // If prepaid plan check student limit
                if ($subscription && $subscription->package_type == 0) {
                    $status = $this->subscriptionService->check_user_limit($subscription, "Students");
                    
                    if (!$status) {
                        ResponseService::errorResponse('You reach out limits');
                    }
                }
            }

            $classSectionExists = DB::table('students')->where('user_id',$userId)->whereNotNull('class_section_id')->first();
            if(!$classSectionExists){
                ResponseService::errorResponse('Class Section cannot be empty');
            }
                        
            DB::table('users')->where('id', $userId)->update(['status' => ($user->status == 0 || $user->status == 2 ? 1 : 0), 'deleted_at' => $user->status == 1 ? now() : null]);
            //Active Guardian when users getting activated
            $student = $this->student->builder()->where('user_id',$user->id)->first();
            if($student){
                if($user->status == 0 || $user->status == 2){
                    $guardian = User::onlyTrashed()->find($student->guardian_id);
                    if(isset($guardian) && $guardian->deleted_at != null){
                        DB::table('users')->where('id',$guardian->id)->update(['deleted_at' => null]);
                    }
                } else {
                    $checkActiveStudent = DB::table('students as s')->join('users as u','u.id','=','s.user_id')->where('s.guardian_id',$student->guardian_id)->whereNull('u.deleted_at')->count();
                    if($checkActiveStudent == 0){
                        $guardian = User::withTrashed()->find($student->guardian_id);
                        if(isset($guardian) && $guardian->deleted_at == null){
                            DB::table('users')->where('id',$guardian->id)->update(['deleted_at' => now()]);
                        }
                    }
                }
            }
            DB::commit();

            if($isActivating) {
                $student = DB::table('students')->where('user_id',$userId)->first();
                $guardian = DB::table('users')
                ->select('id', 'email', DB::raw("CONCAT(first_name, ' ', last_name) as full_name"),'mobile')
                ->where('id', $student->guardian_id)
                ->first();
                $child = DB::table('users')
                    ->select('id','email','dob',DB::raw("CONCAT(first_name, ' ', last_name) as full_name"))
                    ->where('id', $userId)
                    ->first();
                $childPassword =str_replace('-', '', date('d-m-Y', strtotime($user->dob)));     
                $userService = app(UserService::class);          
                $userService->sendRegistrationEmail($guardian, $user, $student->admission_no, $childPassword);

            }
            ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, 'Student Controller ---> Change Status');
            ResponseService::errorResponse();
        }
    }

    public function changeStatusBulk(Request $request) {
        // ResponseService::noFeatureThenSendJson('Student Management');
        ResponseService::noPermissionThenRedirect('student-create');
        try {
            $names = array();
            DB::beginTransaction();
            foreach (json_decode($request->ids, false, 512, JSON_THROW_ON_ERROR) as $key => $userId) {
                $studentUser = $this->user->findTrashedById($userId);
                if ($studentUser->status == 0) {
                    $subscription = $this->subscriptionService->active_subscription(Auth::user()->school_id);
                    // If prepaid plan check student limit
                    if ($subscription && $subscription->package_type == 0) {
                        $status = $this->subscriptionService->check_user_limit($subscription,"Students");
                        
                        if (!$status) {
                            ResponseService::errorResponse('You reach out limits');
                        }
                    }
                }
                $classSectionExists = DB::table('students')->where('user_id',$userId)->whereNotNull('class_section_id')->first();
                
                if(!$classSectionExists){
                    $names[] = $studentUser->first_name . " ".$studentUser->last_name;
                    continue;
                } 

                $this->user->builder()->where('id', $userId)->withTrashed()->update(['status' => $studentUser->status == 0 ? 1 : 0, 'deleted_at' => $studentUser->status == 1 ? now() : null]);
                $student = $this->student->builder()->where('user_id',$studentUser->id)->first();
                if($student){
                    if($studentUser->status == 0 || $studentUser->status == 2){
                        $guardian = User::onlyTrashed()->find($student->guardian_id);
                        if(isset($guardian) && $guardian->deleted_at != null){
                            DB::table('users')->where('id',$guardian->id)->update(['deleted_at' => null]);
                        }
                    } else {
                        $checkActiveStudent = DB::table('students as s')->join('users as u','u.id','=','s.user_id')->where('s.guardian_id',$student->guardian_id)->whereNull('u.deleted_at')->count();
                        if($checkActiveStudent == 0){
                            $guardian = User::withTrashed()->find($student->guardian_id);
                            if(isset($guardian) && $guardian->deleted_at == null){
                                DB::table('users')->where('id',$guardian->id)->update(['deleted_at' => now()]);
                            }
                        }
                    }
                }
            }
            DB::commit();
            if($names){
                $stdNames = implode(',',$names);
                return response()->json([
                    'status' => 'warning',
                    'message' => $stdNames,
                ]);            
            }
            ResponseService::successResponse("Status Updated Successfully");
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function trash($id) {
        // ResponseService::noFeatureThenSendJson('Student Management');
        ResponseService::noPermissionThenSendJson('student-delete');
        try {
            DB::beginTransaction();
            $this->user->builder()->where('id',$id)->withTrashed()->forceDelete();
            DB::commit();
            ResponseService::successResponse("Data Deleted Permanently");
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Student Controller ->Trash Method", 'cannot_delete_because_data_is_associated_with_other_data');
            ResponseService::errorResponse();
        }
    }

    public function createBulkData() {
        ResponseService::noPermissionThenRedirect('student-create');
        $class_section = $this->classSection->all(['*'], ['class', 'class.stream', 'section', 'medium']);
        $sessionYears = $this->sessionYear->all();
        return view('students.add_bulk_data', compact('class_section', 'sessionYears'));
    }

    public function storeBulkData(Request $request) {
        ResponseService::noPermissionThenRedirect('student-create');
        $validator = Validator::make($request->all(), [
            'session_year_id'  => 'required|numeric',
            'class_section_id' => 'required',
            'file'             => 'required|mimes:csv,txt'
        ]);
        if ($validator->fails()) {
            ResponseService::errorResponse($validator->errors()->first());
        }
        try {
            Excel::import(new StudentsImport($request->class_section_id, $request->session_year_id), $request->file);
            ResponseService::successResponse('Data Stored Successfully');
        } catch (ValidationException $e) {
            ResponseService::errorResponse($e->getMessage());
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "Student Controller -> Store Bulk method");
            ResponseService::errorResponse();
        }
    }

    public function resetPasswordIndex() {
        $class_section = $this->classSection->builder()->with('class', 'class.stream', 'section')->get();
        return view('students.reset-password', compact('class_section'));
    }

    public function resetPasswordShow() {
        ResponseService::noPermissionThenRedirect('reset-password-list');
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'DESC');

        $sql = $this->user->builder()->where('reset_request', 1);
        if (!empty($_GET['search'])) {
            $search = $_GET['search'];
            $sql->where(function ($query) use ($search) {
                $query->where('id', 'LIKE', "%$search%")->orwhere('email', 'LIKE', "%$search%")
                    ->orwhere('first_name', 'LIKE', "%$search%")
                    ->orwhere('last_name', 'LIKE', "%$search%")
                    ->orWhereRaw("concat(users.first_name,' ',users.last_name) LIKE '%" . $search . "%'");
            });
        }

        $total = $sql->count();

        $sql->orderBy($sort, $order)->skip($offset)->take($limit);
        $res = $sql->get();

        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $no = 1;
        foreach ($res as $row) {
            $operate = BootstrapTableService::button('fa fa-edit', route('student.reset-password.update', $row->id), ['reset_password', 'btn-gradient-primary', 'btn-action', 'btn-rounded btn-icon'], ['title' => trans("reset_password"), 'data-id' => $row->id, 'data-dob' => $row->dob]);
            $tempRow = $row->toArray();
            $tempRow['no'] = $no++;
            $tempRow['operate'] = $operate;
            $rows[] = $tempRow;
        }

        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    }

    public function resetPasswordUpdate(Request $request) {
        ResponseService::noPermissionThenRedirect('student-change-password');
        try {
            DB::beginTransaction();
            $dob = date('dmY', strtotime($request->dob));
            $password = Hash::make($dob);
            $this->user->update($request->id, ['password' => $password, 'reset_request' => 0]);
            DB::commit();

            ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Student Controller -> Reset Password method");
            ResponseService::errorResponse();
        }
    }

    public function rollNumberIndex() {
        ResponseService::noPermissionThenRedirect('student-create');
        $class_section = $this->classSection->all(['*'], ['class', 'class.stream', 'section', 'medium']);

        return view('students.assign_roll_no', compact('class_section'));
    }

    public function rollNumberUpdate(Request $request) {
        ResponseService::noPermissionThenRedirect('student-create');
        $validator = Validator::make(
            $request->all(),
            ['roll_number_data.*.roll_number' => 'required',],
            ['roll_number_data.*.roll_number.required' => trans('please_fill_all_roll_numbers_data')]
        );
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            DB::beginTransaction();
            foreach ($request->roll_number_data as $data) {
                $updateRollNumberData = array(
                    'roll_number' => $data['roll_number']
                );

                // validation required when the edit of roll number is enabled

                // $class_roll_number_data = $this->student->builder()->where(['class_section_id' => $student->class_section_id,'roll_number' => $data['roll_number']])->whereNot('id',$data['student_id'])->count();
                // if(isset($class_roll_number_data) && !empty($class_roll_number_data)){
                //     $response = array(
                //         'error' => true,
                //         'message' => trans('roll_number_already_exists_of_number').' - '.$i
                //     );
                //     return response()->json($response);
                // }
                // TODO : Use upsert here
                $this->student->update($data['student_id'], $updateRollNumberData);
            }
            DB::commit();
            ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Student Controller -> updateStudentRollNumber");
            ResponseService::errorResponse();
        }
    }

    public function rollNumberShow(Request $request) {
        ResponseService::noPermissionThenRedirect('student-create');
        try {
            ResponseService::noPermissionThenRedirect('student-list');
            $currentSessionYear = $this->cache->getDefaultSessionYear();
            $class_section_id = $request->class_section_id;
            $sql = $this->user->builder()->with('student');
            $sql = $sql->whereHas('student', function ($q) use ($class_section_id, $currentSessionYear) {
                $q->where(['class_section_id' => $class_section_id, 'session_year_id' => $currentSessionYear->id]);
            });
            if (!empty($_GET['search'])) {
                $search = $_GET['search'];
                $sql->where(function ($query) use ($search) {
                    $query->where('first_name', 'LIKE', "%$search%")
                        ->orwhere('last_name', 'LIKE', "%$search%")
                        ->orwhere('email', 'LIKE', "%$search%")
                        ->orwhere('dob', 'LIKE', "%$search%")
                        ->orWhereHas('student', function ($q) use ($search) {
                            $q->where('id', 'LIKE', "%$search%")
                                ->orWhere('user_id', 'LIKE', "%$search%")
                                ->orWhere('class_section_id', 'LIKE', "%$search%")
                                ->orWhere('admission_no', 'LIKE', "%$search%")
                                ->orWhere('admission_date', 'LIKE', date('Y-m-d', strtotime("%$search%")))
                                ->orWhereHas('user', function ($q) use ($search) {
                                    $q->where('first_name', 'LIKE', "%$search%")
                                        ->orwhere('last_name', 'LIKE', "%$search%")
                                        ->orwhere('email', 'LIKE', "%$search%")
                                        ->orwhere('dob', 'LIKE', "%$search%");
                                });
                        });
                });
            }
            if ($request->sort_by == 'first_name') {
                $sql = $sql->orderBy('first_name', $request->order_by);
            }
            if ($request->sort_by == 'last_name') {
                $sql = $sql->orderBy('last_name', $request->order_by);
            }
            $total = $sql->count();
            $res = $sql->get();

            $bulkData = array();
            $bulkData['total'] = $total;
            $rows = array();
            $no = 1;
            $roll = 1;
            $index = 0;

            // TODO : improve this
            foreach ($res as $row) {
                $tempRow = $row->toArray();
                $tempRow['no'] = $no++;
                $tempRow['student_id'] = $row->student->id;
                $tempRow['old_roll_number'] = $row->student->roll_number;

                // for edit roll number comment below line
                $tempRow['new_roll_number'] = "<input type='hidden' name='roll_number_data[" . $index . "][student_id]' class='form-control' readonly value=" . $row->student->id . "> <input type='hidden' name='roll_number_data[" . $index . "][roll_number]' class='form-control' value=" . $roll . ">" . $roll;

                // and uncomment below line
                // $tempRow['new_roll_number'] = "<input type='hidden' name='roll_number_data[" . $index . "][student_id]' class='form-control' readonly value=" . $row->student->id . "> <input type='text' name='roll_number_data[" . $index . "][roll_number]' class='form-control' value=" . $roll . ">";

                $tempRow['user_id'] = $row->id;
                $tempRow['admission_no'] = $row->student->admission_no;
                $tempRow['admission_date'] = $row->student->admission_date;
                $rows[] = $tempRow;
                $index++;
                $roll++;
            }

            $bulkData['rows'] = $rows;
            return response()->json($bulkData);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "Student Controller -> listStudentRollNumber");
            ResponseService::errorResponse();
        }
    }

    public function downloadSampleFile() {
        try {
            return Excel::download(new StudentDataExport(), 'import.xlsx');
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, 'Student Controller ---> Download Sample File');
            ResponseService::errorResponse();
        }
    }

    public function update_profile()
    {
        ResponseService::noPermissionThenRedirect('student-edit');
        
        $class_sections = $this->classSection->all(['*'], ['class', 'class.stream', 'section', 'medium']);
        return view('students.add_bulk_profile',compact('class_sections'));
        
    }

    public function list($id = null, Request $request)
    {
        ResponseService::noPermissionThenRedirect('student-edit');
        $search = request('search');

        $res = array();
        $total = 0;
        if (!empty($request->class_id)) {
            $sql = $this->student->builder()->with('user', 'guardian', 'class_section.class', 'class_section.section', 'class_section.medium')
                ->where(function ($query) use ($search) {
                    $query->when($search, function ($query) use ($search) {
                        $query->where(function ($query) use ($search) {
                            $query->where('user_id', 'LIKE', "%$search%")
                                ->orWhere('roll_number', 'LIKE', "%$search%")
                                ->orWhereHas('user', function ($q) use ($search) {
                                    $q->where('first_name', 'LIKE', "%$search%")
                                        ->orwhere('last_name', 'LIKE', "%$search%")
                                        ->orwhere('email', 'LIKE', "%$search%")
                                        ->orwhere('dob', 'LIKE', "%$search%");
                                });
                        });
                    });
                })->when(request('class_id') != null, function ($query) {
                    $classId = request('class_id');
                    $query->where(function ($query) use ($classId) {
                        $query->where('class_section_id', $classId);
                    });
                });

            $sql = $sql->whereHas('user', function ($query) {
                $query->where('status', 1);
            });
            $total = $sql->count();
            $sql = $sql->orderBy('roll_number', 'ASC');
            $res = $sql->get();
        }
        
        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $no = 1;
        foreach ($res as $row) {
            
            $tempRow = $row->toArray();
            $tempRow['no'] = $no++;
            $rows[] = $tempRow;
        }

        $bulkData['rows'] = $rows;
        return response()->json($bulkData);

    }

    public function store_update_profile(Request $request)
    {
        ResponseService::noAnyPermissionThenRedirect(['student-edit']);
        $schoolId = Auth::getUser()->school_id;

        try {
           
            $data = array();
            if ($request->student_image) {
                if (FileSizeLimitService::checkFileSizeLimit($schoolId)) {
                    return ResponseService::errorResponse('storage capacity not enough');}
                foreach ($request->student_image as $key => $profile) {
                    $fileSize = $profile->getSize();
                    $fileSizeKB = round($fileSize / 1024, 2);
                    $data[] = [
                        'id' => $key,
                        'image' => $profile->store('user','public'),
                        'file_size' =>$fileSizeKB
                    ];
                }
            }
            if ($request->guardian_image) {
                if (FileSizeLimitService::checkFileSizeLimit($schoolId)) {
                    return ResponseService::errorResponse('storage capacity not enough');}
                foreach ($request->guardian_image as $key => $profile) {
                    $fileSize = $profile->getSize();
                    $fileSizeKB = round($fileSize / 1024, 2);
                    $data[] = [
                        'id' => $key,
                        'image' => $profile->store('user','public'),
                        'file_size' =>$fileSizeKB
                    ];
                }
            }
            // dd($data);
            $this->user->upsert($data,['id'],['image','file_size']);
            ResponseService::successResponse('Profile Updated Successfully');
            
        } catch (\Throwable $th) {
            ResponseService::logErrorResponse($th);
            ResponseService::errorResponse();
        }
    }

    public function generate_id_card_index() {
        ResponseService::noFeatureThenRedirect('ID Card - Certificate Generation');
        ResponseService::noAnyPermissionThenRedirect(['student-list', 'class-teacher']);

        $class_sections = $this->classSection->all(['*'], ['class', 'class.stream', 'section', 'medium']);
        $sessionYears = $this->sessionYear->all();

        return view('students.generate_id_card', compact('class_sections', 'sessionYears'));
    }

    public function generate_id_card(Request $request) {
        ResponseService::noFeatureThenRedirect('ID Card - Certificate Generation');
        ResponseService::noAnyPermissionThenRedirect(['student-list', 'class-teacher']);
        $request->validate([
            'user_id' => 'required'
        ], [
            'user_id.required' => trans('Please select at least one record')
        ]);
        try {
            $user_ids = explode(",",$request->user_id);
            $settings = $this->cache->getSchoolSettings();
            if (!isset($settings['student_id_card_fields'])) {
                return redirect()->route('id-card-settings')->with('error',trans('settings_not_found'));
            }

            $settings['student_id_card_fields'] = explode(",",$settings['student_id_card_fields']);

            $data = explode("storage/", $settings['signature'] ?? '');
            $settings['signature'] = end($data);

            $data = explode("storage/", $settings['background_image'] ?? '');
            $settings['background_image'] = end($data);

            $data = explode("storage/", $settings['horizontal_logo'] ?? '');
            $settings['horizontal_logo'] = end($data);

            $sessionYear = $this->cache->getDefaultSessionYear();
            $valid_until = date('F j, Y',strtotime($sessionYear->end_date));
            $height = $settings['page_height'] * 2.8346456693;
            $width = $settings['page_width'] * 2.8346456693;
            // $customPaper = array(0,0,360,200);
            $customPaper = array(0,0,$width,$height);
            $students = $this->user->builder()->select('id','first_name','last_name','image','school_id','gender','dob')->with('student:id,user_id,class_section_id,school_id,guardian_id,roll_number','student.class_section.class','student.class_section.section','student.class_section.medium','student.class_section.class.stream','student.guardian:id,mobile,first_name,last_name')->whereHas('student',function($q) use($user_ids) {
                $q->whereIn('id',$user_ids);
            })->get();
            $settings['page_height'] = ($settings['page_height'] * 3.7795275591).'px';

            $pdf = PDF::loadView('students.students_id_card',compact('students','sessionYear','valid_until','settings'));
            $pdf->setPaper($customPaper);

            
            return $pdf->stream();
            return view('students.id_card_pdf');
        } catch (\Throwable $th) {
            ResponseService::logErrorResponse($th);
            ResponseService::errorResponse();
        }
    }

    public function admissionForm()
    {
        $schoolSettings = $this->cache->getSchoolSettings();

        $data = explode("storage/", $schoolSettings['horizontal_logo'] ?? '');
            $schoolSettings['horizontal_logo'] = end($data);

        if ($schoolSettings['horizontal_logo'] == null) {
            $systemSettings = $this->cache->getSystemSettings();
            $data = explode("storage/", $systemSettings['horizontal_logo'] ?? '');
            $schoolSettings['horizontal_logo'] = end($data);
        }

        $pdf = PDF::loadView('students.admission_form',compact('schoolSettings'));
        return $pdf->stream();
    }

    public function id_card_index()
    {
        ResponseService::noFeatureThenRedirect('ID Card - Certificate Generation');
        ResponseService::noPermissionThenRedirect('student-id-card-settings');
        $settings = $this->cache->getSchoolSettings();
        $settings['student_id_card_fields'] = explode(",",$settings['student_id_card_fields'] ?? '');

        return view('students.id_card_settings',compact('settings'));
    }

    public function id_card_store(Request $request)
    {
        ResponseService::noFeatureThenSendJson('ID Card - Certificate Generation');
        ResponseService::noAnyPermissionThenSendJson(['student-id-card-settings']);
        $settings = [
            'header_color'              => 'required',
            'footer_color'              => 'required',
            'header_footer_text_color'  => 'required',
            'layout_type'               => 'required',
            'background_image'          => 'nullable|image|max:2048',
            'profile_image_style'       => 'required',
            'signature'                 => 'nullable',
            'page_width'                => 'required',
            'page_height'               => 'required',
            'student_id_card_fields'    => 'nullable',
        ];
        $validator = Validator::make($request->all(), $settings);
        
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }

        $request->validate([
            'student_id_card_fields' => 'required'
        ],[
            'student_id_card_fields.required' => 'Please select at least one field.'
        ]);

        try {
            DB::beginTransaction();
            $data = array();
            foreach ($settings as $key => $rule) {
                if ($key == 'background_image' || $key == 'signature') {
                    if ($request->hasFile($key)) {
                        // TODO : Remove the old files from server
                        $data[] = [
                            "name" => $key,
                            "data" => $request->file($key),
                            "type" => "file"
                        ];
                    }
                } else if($key == 'student_id_card_fields') {
                    $key_value = implode(",",$request->student_id_card_fields);
                    $data[] = [
                        "name" => $key,
                        "data" => $key_value,
                        "type" => "string"
                    ];

                } else {
                    $data[] = [
                        "name" => $key,
                        "data" => $request->$key,
                        "type" => "string"
                    ];
                }
            }
            $this->schoolSettings->upsert($data, ["name"], ["data"]);
            $this->cache->removeSchoolCache(config('constants.CACHE.SCHOOL.SETTINGS'));

            DB::commit();
            ResponseService::successResponse('Data Updated Successfully');
        } catch (\Throwable $th) {
            ResponseService::logErrorResponse($th);
            ResponseService::errorResponse();
        }
    }

    public function remove_image_from_id_card($type)
    {
        ResponseService::noFeatureThenSendJson('ID Card - Certificate Generation');
        ResponseService::noAnyPermissionThenRedirect(['student-id-card-settings']);
        try {
            DB::beginTransaction();
            if ($type == 'background') {
                $this->schoolSettings->builder()->where('name','background_image')->delete();
            } else {
                $this->schoolSettings->builder()->where('name','signature')->delete();
            }
            DB::commit();
            $this->cache->removeSchoolCache(config('constants.CACHE.SCHOOL.SETTINGS'));
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (\Throwable $th) {
            ResponseService::logErrorResponse($th);
            ResponseService::errorResponse();
        }
    }

    public function studentSubjectIndex() {
        ResponseService::noPermissionThenRedirect('student-list');
        $class_sections = $this->classSection->all(['*'], ['class', 'class.stream', 'section', 'medium']);
        $subjects = DB::table('subjects')->whereNull('deleted_at')->where('school_id', Auth::user()->school_id)->select('id', 'name')->get();
        $teachers = DB::table('users')->join('subject_teachers', 'users.id', '=', 'subject_teachers.teacher_id')->where('users.school_id', Auth::user()->school_id)->select('users.id', 'users.first_name', 'users.last_name')->distinct()->get();
        return view('students.student_subject', compact('class_sections', 'subjects', 'teachers'));
    }

    public function showStudentSubject(Request $request) {
        ResponseService::noPermissionThenRedirect('student-list');
    
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'ASC');
        $search = request('search');
        $class_section = $request->class_section_id;
        $teacher_id = $request->teacher_id;
        $schoolId = Auth::user()->school_id;
        $sessionYear = $this->cache->getDefaultSessionYear();
        $query = DB::table('students')
                ->join('class_sections', 'class_sections.id', '=', 'students.class_section_id')
                ->join('users', 'students.user_id', '=', 'users.id')
                ->leftJoin('class_subjects', 'class_subjects.class_id', '=', 'class_sections.class_id')
                ->leftJoin('subjects', 'class_subjects.subject_id', '=', 'subjects.id')
                ->leftJoin('subject_teachers', 'class_subjects.id', '=', 'subject_teachers.class_subject_id')                
                ->leftJoin('student_subjects', function ($join) {
                    $join->on('student_subjects.student_id', '=', 'users.id')
                        ->on('student_subjects.class_subject_id', '=', 'class_subjects.id');
                })
                ->select(
                    'class_subjects.id AS class_subject_id',
                    'class_sections.id AS class_section_id',
                    'subjects.id AS subject_id',
                    'subjects.name AS subject_name',
                    'class_subjects.type AS class_subject_type',
                    'subjects.type',
                    'subject_teachers.teacher_id',                    
                    'students.id',
                    'users.first_name',
                    'users.last_name',
                    'student_subjects.id AS student_subject_id'
                )
                ->where('students.session_year_id',$sessionYear->id)
                ->where('class_sections.school_id', $schoolId)
                ->whereNull('users.deleted_at')
                ->where(function ($query) {
                    $query->whereNull('class_subjects.deleted_at')
                          ->orWhereNull('class_subjects.id');
                })
                ->where(function ($query) {
                    $query->where('class_subjects.type', 'Compulsory')
                          ->orWhere('class_subjects.type', 'Elective')
                          ->orWhereNull('class_subjects.id');
                });  
                
        if ($teacher_id) {
            $query->where('subject_teachers.teacher_id', $teacher_id);
        }

        if($class_section){
            $query->where(function ($query) use ($class_section) {
                $query->where('class_sections.id','=',$class_section);
            });
        }

        if ($search) {
            $query->where(function ($query) use ($search) {
                $query->whereRaw("concat(users.first_name, ' ', users.last_name) LIKE '%" . $search . "%'")
                      ->orWhere('users.first_name', 'LIKE', "%{$search}%")
                      ->orWhere('users.last_name', 'LIKE', "%{$search}%");
            });
        }
        $res = $query->get();
        $rows = [];
        $no = 1;
        foreach ($res as $row) {
            $userId = $row->id; 

            if (!isset($rows[$userId])) {
                $tempRow = [
                    'id' => $row->id,
                    'no' => $no++,
                    'user_name' => $row->first_name . " " . $row->last_name,
                    'core_subjects' => [],
                    'elective_subjects' => [],
                    'operate' => BootstrapTableService::editButton(route('students.student_subject.edit', $row->id), false),
                ];

                if ($row->class_subject_type === "Compulsory") {
                    $tempRow['core_subjects'][] = [
                        'subject_name' => $row->subject_name,
                        'subject_type' => $row->type,
                        'class_subject_id' => $row->class_subject_id,
                        'subject_id' => $row->subject_id,
                    ];
                } elseif ($row->class_subject_type === "Elective" && $row->student_subject_id) {
                    $tempRow['elective_subjects'][] = [
                        'subject_name' => $row->subject_name,
                        'subject_type' => $row->type,
                        'class_subject_id' => $row->class_subject_id,
                        'subject_id' => $row->subject_id,
                    ];
                }

                $rows[$userId] = $tempRow;
            } else {
                if ($row->class_subject_type === "Compulsory") {
                    $rows[$userId]['core_subjects'][] = [
                        'subject_name' => $row->subject_name,
                        'subject_type' => $row->type,
                        'class_subject_id' => $row->class_subject_id,
                        'subject_id' => $row->subject_id,
                    ];
                } elseif ($row->class_subject_type === "Elective" && $row->student_subject_id) {
                    $rows[$userId]['elective_subjects'][] = [
                        'subject_name' => $row->subject_name,
                        'subject_type' => $row->type,
                        'class_subject_id' => $row->class_subject_id,
                        'subject_id' => $row->subject_id,
                    ];
                }
            }
        }

        if ($request->subject_id) {
            $rows = array_filter($rows, function($student) use ($request) {
                // Check core subjects
                foreach ($student['core_subjects'] as $subject) {
                    if ($subject['subject_id'] == $request->subject_id) {
                        return true;
                    }
                }
                // Check elective subjects
                foreach ($student['elective_subjects'] as $subject) {
                    if ($subject['subject_id'] == $request->subject_id) {
                        return true;
                    }
                }
                return false;
            });
        }

        $dataArray = array_values($rows);
        $bulkData['total'] = count($dataArray);

        usort($rows, function ($a, $b) use ($sort, $order) {
            if ($order === 'ASC') {
                return $a[$sort] <=> $b[$sort];
            } else {
                return $b[$sort] <=> $a[$sort];
            }
        });
        $paginatedRows = array_slice($rows, $offset, $limit);
        $startNo = $offset + 1; 
        foreach ($paginatedRows as &$row) {
            $row['no'] = $startNo++;
        }
        $bulkData['rows'] = array_values($paginatedRows);

        return response()->json($bulkData);
                                 
    }

    public function editStudentSubject($id) {
        ResponseService::noPermissionThenRedirect('student-list');

        // Fetch the data to edit
        $student = DB::table('students')
                   ->join('users','students.user_id','=','users.id')
                   ->where('students.id',$id)
                   ->select('users.id as user_id','users.first_name','users.last_name')
                   ->first();

        $electiveSubjects = DB::table('class_subjects')
        ->select('class_subjects.*', 'subjects.id as subject_id', 'subjects.name', 'subjects.type', 'elective_subject_groups.total_selectable_subjects')
        ->join('classes', 'class_subjects.class_id', '=', 'classes.id')
        ->join('subjects', 'class_subjects.subject_id', '=', 'subjects.id')
        ->join('class_sections', 'classes.id', '=', 'class_sections.class_id')
        ->join('students', 'class_sections.id', '=', 'students.class_section_id')
        ->join('elective_subject_groups', 'class_subjects.elective_subject_group_id', '=', 'elective_subject_groups.id')
        ->where('students.id', $id)
        ->where('class_subjects.type', 'Elective')
        ->whereNull('class_subjects.deleted_at')
        ->get();

        $selectedStudentSubjects = DB::table('student_subjects')
                ->join('class_subjects', 'student_subjects.class_subject_id', '=', 'class_subjects.id')
                ->join('users', 'student_subjects.student_id', '=', 'users.id')
                ->where('student_subjects.student_id', $student->user_id)
                ->pluck('class_subjects.id')
                ->toArray();

        return response(view('students.edit_student_subject', compact('student', 'electiveSubjects', 'selectedStudentSubjects', 'id')));
    }
    
    public function updateStudentSubject(Request $request){
        ResponseService::noPermissionThenRedirect('student-list');
        try{
            DB::beginTransaction();
            $studentId = $request->student_id;
            $studentDetail = DB::table("students")
                            ->join("users","users.id", "=", "students.user_id")
                            ->where("users.id",$studentId)
                            ->select("class_section_id","session_year_id")
                            ->first();

            $electiveSubjects = $request->elective_subjects ?? [];
            $totalSelectable = $request->total_selectable_subjects;
            
            if (count($electiveSubjects) > $totalSelectable) {
                return ResponseService::errorResponse('You can only select up to ' . $totalSelectable . ' subjects.');
            }

            $existingSubjects = DB::table('student_subjects')
                                ->where('student_id', $studentId)
                                ->pluck('class_subject_id')
                                ->toArray();

            $subjectsToRemove = array_diff($existingSubjects, $electiveSubjects);

            if (!empty($subjectsToRemove)) {
                DB::table('student_subjects')
                    ->where('student_id', $studentId)
                    ->whereIn('class_subject_id', $subjectsToRemove)
                    ->delete();
            }

            foreach($electiveSubjects as $subject){
                if (!in_array($subject, $existingSubjects)) {
                    $data = [
                       'student_id' => $studentId,
                       'class_subject_id' => $subject,
                       'class_section_id' => $studentDetail->class_section_id,
                       'session_year_id' => $studentDetail->session_year_id,
                       'school_id' => Auth::user()->school_id,
                       'created_at' => now(),
                       'updated_at' => now(),
                    ];
                    DB::table('student_subjects')->insert($data);
                }
            }
            DB::commit();
            ResponseService::successResponse('Data Updated Successfully');
        } catch (\Throwable $th) {
            ResponseService::logErrorResponse($th);
            ResponseService::errorResponse();
        }

    }

    public function studentProgress(Request $request){
        ResponseService::noFeatureThenRedirect('Student Progress');
        $classSections = $this->classSection->builder()->with('class', 'class.stream', 'section', 'medium')->get();
        $subjectTeachers = $this->subjectTeacher->builder()->with('subject:id,name,type')->get();
        $sessionYears = $this->sessionYear->all();
        $schoolId = Auth::user()->school_id;

        // Get the class_section_id from the request or set a default value if needed
        $classSectionId = $request->input('class_section_id', null);

        $students = DB::table('students as s')
            ->join('users as u', 's.user_id', '=', 'u.id')
            ->where('s.school_id', $schoolId)
            ->whereNull('u.deleted_at')
            ->select('s.id', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"))
            ->get();

        $user = Auth::user();
        return response(view('students.student-progress', compact('classSections', 'subjectTeachers', 'sessionYears','students')));
    }

    public function studentProgressStore(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Student Progress');
        $sessionYear = $this->cache->getDefaultSessionYear();
        $teacherId = Auth::user()->id;
        // Validate the request data
        $request->validate([
            'student_id'        => 'required',
            'class_subject_id'  => 'required',
            'teacher_id'        => 'nullable',
            'title'             => 'nullable',
            'description'       => 'nullable',
            'files'             => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'created_date'      => 'required|date',
        ]);
        try {
            DB::beginTransaction();
            $schoolId = Auth::getUser()->school_id;
            $settings = $this->cache->getSchoolSettings();
            $requireApproval = isset($settings['student_progress_approval']) && $settings['student_progress_approval'] == 1;

            // Handle file upload if applicable
            $imageName = '';
            
            $isAdmin = Auth::user()->hasRole('School Admin');
            $status = $isAdmin ? 'auto-approve' : ($requireApproval ? 'pending' : 'auto-approve');

            if ($request->hasFile('picture')) {
                $image = $request->file('picture');
                if (FileSizeLimitService::checkFileSizeLimit($schoolId)) {
                return ResponseService::errorResponse('storage capacity not enough');}
            $imageName = time() . '_' . $image->getClientOriginalName();
                $imageName = $image->store('images', 'public');
                $fileSize = $image->getSize();
                $fileSizeKB = round($fileSize / 1024, 2);
            

            // Insert data into the database using DB facade
            DB::table('student_progress')->insert([
                'school_id' => Auth::user()->school_id,
                'class_section_id' => $request->class_section_id,
                'student_id' => $request->student_id,
                'class_subject_id' => $request->class_subject_id,
                'teacher_id' => $teacherId,
                'title' => $request->title,
                'description' => $request->description,
                'files' => $imageName,
                'session_year_id' => $sessionYear->id,
                'created_on' => $request->created_date,
                'file_size'  => $fileSizeKB,
                'status' => $status,
                'remark' => $request->remark ?? ''
            ]);
        }else{
            DB::table('student_progress')->insert([
            'school_id' => Auth::user()->school_id,
            'class_section_id' => $request->class_section_id,
            'student_id' => $request->student_id,
            'class_subject_id' => $request->class_subject_id,
            'teacher_id' => $teacherId,
            'title' => $request->title,
            'description' => $request->description,
            'files' => $imageName,
            'session_year_id' => $sessionYear->id,
            'created_on' => $request->created_date,
            // 'file_size'  => $fileSizeKB,
            'status' => $status,
            'remark' => $request->remark ?? ''
            ]);
        }
        $studentDetails = DB::table('students')
                ->join('users', 'users.id', '=', 'students.user_id')
                ->where('students.id', $request->student_id)
                ->first();

        $data = [
            'user_id' => $studentDetails->user_id,
            'school_id' => Auth::user()->school_id, 
            'date' => now(), 
            'status' => 0, 
            'type' => 8, 
        ];
        DB::table('admission_notification')->insert($data);

        if ($isAdmin || $status === 'auto-approve') {
            $recipients = [];
            $recipients[] = $studentDetails->user_id;
            
            $guardian_id = DB::table('students')
                ->where('id', $request->student_id)
                ->whereNotNull('guardian_id')
                ->value('guardian_id');
                
            if ($guardian_id) {
                $recipients[] = $guardian_id;
            }

            $subjectDetails = DB::table('class_subjects AS cs')
                ->join('subjects AS s', 's.id', '=', 'cs.subject_id')
                ->where('cs.id', $request->class_subject_id)
                ->select('s.name AS subject_name', 's.type AS subject_type')
                ->first();

            $title = 'New Progress Added';
            $body = 'The progress for student ' . $studentDetails->first_name . ' ' . $studentDetails->last_name . 
            ' in ' . $subjectDetails->subject_name . ' - ' . $subjectDetails->subject_type . 
            ' has been added';
            if ($status === 'auto-approve') {
                $body .= ' by ' . Auth::user()->first_name . ' ' . Auth::user()->last_name;
            }
            $type = 'Student Progress';
            
            send_notification($recipients, $title, $body, $type);
        }
        
        DB::commit();       
        ResponseService::successResponse('Data Updated Successfully');
        } catch (\Throwable $th) {
            DB::rollBack();
            ResponseService::logErrorResponse($th);
            ResponseService::errorResponse();
        }
    }

    public function showStudentProgress(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Student Progress');
        
        // Get request parameters
        $offset = $request->input('offset', 0);
        $limit = $request->input('limit', 99999);
        $sort = $request->input('sort', 'id');
        $order = $request->input('order', 'DESC');
        $subject_id = $request->input('subject_id');
        $class_section_id = $request->input('class_id');
        $schoolId = Auth::user()->school_id;
        $session_year_id = $request->input('session_year_id');
        $search = $request->input('search');
        // Get school settings to check if approval is required
        $settings = $this->cache->getSchoolSettings();
        $requireApproval = isset($settings['student_progress_approval']) && $settings['student_progress_approval'] == 1;

        // Fetch the school ID from the authenticated user
        $schoolId = Auth::user()->school_id;

        // Build the query
        $query = StudentProgress::query();

        // Join with users table to get student names
        $query = DB::table('student_progress AS sp')
            ->join('students AS s', 's.id', '=', 'sp.student_id')
            ->join('users AS u', 'u.id', '=', 's.user_id')
            ->join('class_subjects AS cs', 'cs.id', '=', 'sp.class_subject_id')
            ->join('subjects AS sub', 'sub.id', '=', 'cs.subject_id')
            ->join('users AS t', 't.id', '=', 'sp.teacher_id') 
            ->select(
                'sp.*',
                'u.first_name',
                'u.last_name',
                'sp.class_subject_id',
                's.id AS student_id',
                'sub.name as subject_name',
                'sub.type',
                'sp.files',
                't.first_name AS teacher_first_name',
                't.last_name AS teacher_last_name',
                DB::raw("CONCAT(u.first_name, ' ', u.last_name) as student_name"),
                DB::raw("CONCAT(t.first_name, ' ', t.last_name) as teacher_name")
            )
            ->whereNull('u.deleted_at')
            ->where('s.school_id', $schoolId);

        if($class_section_id){
            $query->where('s.class_section_id',$class_section_id);
        }

        if($subject_id){
            $query->where('cs.id',$subject_id);
        }

        if ($search) {
            $query->where(function($query) use ($search) {
                $query->where('s.id', 'LIKE', "%{$search}%")
                      ->orWhere(DB::raw('CONCAT(u.first_name, " ", u.last_name)'), 'LIKE', "%{$search}%")
                      ->orWhere('u.first_name', 'LIKE', "%{$search}%")
                      ->orWhere('u.last_name', 'LIKE', "%{$search}%");
            });
        }
        // Get the total count of records (after applying filters)
        $total = $query->count();
        
        // Then your existing ordering
        $query->orderBy($sort, $order)
        ->skip($offset)
        ->take($limit);

        // Get the records
        $res = $query->get();

        // Prepare the response data
        $bulkData = [];
        $bulkData['total'] = $total;
        $rows = [];
        $no = 1;
        
        foreach ($res as $row) {
            $operate = '';
            $tempRow = [];        

            if (Auth::user()->hasRole('School Admin')) {
                $operate .= BootstrapTableService::editButton(route('students.student-progress.update', $row->id));
                $operate .= BootstrapTableService::deleteButton(route('students.student-progress-delete', $row->id));
            } else {
                // For teachers: can only edit/delete their own entries that are pending or auto-approve
                if (($row->status == "auto-approve" || $row->status == "pending") && $row->teacher_id == Auth::id()) {
                    $operate .= BootstrapTableService::editButton(route('students.student-progress.update', $row->id));
                    $operate .= BootstrapTableService::deleteButton(route('students.student-progress-delete', $row->id));
                }
            }

            $status = '';
            if(Auth::user()->hasRole('School Admin')){
                if($row->status == "pending") {
                    $status .= BootstrapTableService::button('fa-solid fa-check',
                        route('student.progress.verify', ['id' => $row->id,'status'=>'approve']), 
                        ['btn-gradient-success','btn-approve'],
                        ['title'=>'Approve']
                    );
                    $status .= BootstrapTableService::button('fa-solid fa-xmark',
                        route('student.progress.verify', ['id' => $row->id,'status'=>'reject']), 
                        ['btn-gradient-danger','btn-reject'],
                        ['title'=>'Reject']
                    );
                } else if($row->status == "approve" || $row->status == "auto-approve") {  
                    $status = "Approved";
                } else if($row->status == "reject") {   
                    $status = "Rejected";
                }
            } else {
                if($row->status == "pending") {
                    $status = "Pending Approval";
                } else if($row->status == "approve" || $row->status == "auto-approve") { 
                    $status = "Approved";
                } else if($row->status == "reject") {   
                    $status = "Rejected";
                }
            }
    
            // Set all row data
            $tempRow['no'] = $no++;
            $tempRow['student_id'] = $row->student_id;
            $tempRow['student_name'] = $row->first_name . " " . $row->last_name;
            $tempRow['class_section'] = $row->class_section_id;
            $tempRow['subject_name'] = $row->subject_name;
            $tempRow['class_subject_id'] = $row->class_subject_id;
            $tempRow['title'] = $row->title;
            $tempRow['description'] = $row->description;
            $tempRow['teacher_name'] = $row->teacher_first_name . " " . $row->teacher_last_name;
            $tempRow['created_on'] = $row->created_on;
            $tempRow['files'] = $row->files;
            $tempRow['operate'] = $operate;
            $tempRow['status'] = $status;
            $tempRow['remark'] = $row->remark;
            
            $rows[] = $tempRow;
        }
          
        $bulkData['rows'] = $rows;

        return response()->json($bulkData);
    }

    public function studentListDestroy($id)
    {
        ResponseService::noFeatureThenRedirect('Student Progress');
        try {
            DB::beginTransaction(); 
            $studentProgress = DB::table('student_progress')->where('id', $id)->first();
            if ($studentProgress && $studentProgress->files) {
                $filePath = public_path('storage/' . $studentProgress->files);
                if (file_exists($filePath)) {
                    unlink($filePath); // Delete the file from the storage
                }
            }

            DB::table('student_progress')->where('id',$id)->delete();
            DB::commit();
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Student Controller -> Destroy Method");
            ResponseService::errorResponse();
        }
    }

    public function updateStudentProgress(Request $request, $id)
    {
        ResponseService::noFeatureThenRedirect('Student Progress');

        $request->validate([
            'title'             => 'nullable',
            'description'       => 'nullable',
            'files'             => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'status'            => 'nullable|in:approve,reject,pending',  
            'remark'            => 'nullable|required_if:status,reject',
        ]);

        try {
            DB::beginTransaction();

            // Retrieve existing record from the database
            $studentProgress = DB::table('student_progress')->where('id', $id)->first();

            if (!$studentProgress) {
                throw new Exception('Student progress record not found.');
            }
            // $fileSize = $request->file('update_file_or_picture')->getSize();
            // $fileSizeKB = round($fileSize / 1024, 2);

            // Prepare the data to update
            $data = [
                'title'             => $request->input('title'),
                'description'       => $request->input('description'),
                // 'file_size'         => $fileSizeKB,
            ];

            if ($request->has('status')) {
                // Convert status to match the format we want to store
                $statusMap = [
                    'approve' => 'approve',
                    'reject' => 'reject',
                    'pending' => 'pending'
                ];
                
                $data['status'] = $statusMap[$request->status];
                $data['remark'] = $request->status === 'reject' ? $request->remark : null;
    
                // Get student details for notification
                $student = DB::table('students')
                            ->join('users', 'users.id', '=', 'students.user_id')
                            ->where('students.id', $studentProgress->student_id)
                            ->select('students.*', 'users.first_name', 'users.last_name', 'users.id as user_id')
                            ->first();
    
            }
            $schoolId = Auth::getUser()->school_id;

            // Check if a new file is uploaded
            if ($request->hasFile('update_file_or_picture')) {
                $fileSize = $request->file('update_file_or_picture')->getSize();
                $fileSizeKB = round($fileSize / 1024, 2);
                    if (FileSizeLimitService::checkFileSizeLimit($schoolId)) {
                        return ResponseService::errorResponse('storage capacity not enough');}
                if ($studentProgress->files) {
                    $oldFilePath = storage_path('app/public/' . $studentProgress->files);
                    if (file_exists($oldFilePath)) {
                        unlink($oldFilePath);
                    }
                }

                // Store the new file
                $file = $request->file('update_file_or_picture');
                $path = $file->store('images', 'public'); 
                $data['files'] = $path; 
                $data['file_size'] =$fileSizeKB;
            }

            // Update the record in the database
            DB::table('student_progress')->where('id', $id)->update($data);

            DB::commit();
            ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "StudentProgress Controller -> Update Method");
            ResponseService::errorResponse();
        }
    }

    public function verifyProgress($id, $status, Request $request)
    {
        ResponseService::noFeatureThenRedirect('Student Progress');

        $request->validate([
            'remark' => 'required_if:status,reject'
        ]);

        // Get the current status before updating
        $currentStatus = DB::table('student_progress')
            ->where('id', $id)
            ->value('status');

        // Only proceed if status is actually changing
        if ($currentStatus !== $status) {
            $remark = $request->remark;
            
            // Update the status
            DB::table('student_progress')
                ->where('id', $id)
                ->update([
                    'status' => $status, 
                    'remark' => $remark ?? null
                ]);

            // Get necessary data for notification
            $progress = DB::table('student_progress AS sp')
                ->join('class_subjects AS cs', 'cs.id', '=', 'sp.class_subject_id')
                ->join('subjects AS s', 's.id', '=', 'cs.subject_id')
                ->where('sp.id', intval($id))
                ->select('sp.*', 's.name AS subject_name', 's.type AS subject_type')
                ->first();

            $student = DB::table('students')
                ->join('users', 'users.id', '=', 'students.user_id')
                ->where('students.id', $progress->student_id)
                ->select('students.*', 'users.first_name', 'users.last_name', 'users.id as user_id')
                ->first();

            $teacher = DB::table('users')
                ->where('id', $progress->teacher_id)
                ->select('id', DB::raw('concat(first_name, " " ,last_name) as name'))
                ->first();

            $user = array();
            $user[] = $teacher->id;
            
            if ($status === 'approve') {
                $title = 'New Progress Added';
                $body = 'The progress for student ' . $student->first_name . ' ' . $student->last_name . 
                       ' in ' . $progress->subject_name . ' - ' . $progress->subject_type . 
                       ' has been added';
                
                // Add student and guardian to notifications only for approve status
                $user[] = $student->user_id;
                
                // Get and add parent/guardian if exists
                $guardian_id = DB::table('students')
                    ->where('id', $student->id)
                    ->whereNotNull('guardian_id')
                    ->value('guardian_id');
                    
                if ($guardian_id) {
                    $user[] = $guardian_id;
                }
            } else {
                $title = 'Progress Rejected';
                $body = 'The progress for student ' . $student->first_name . ' ' . $student->last_name . 
                       ' in ' . $progress->subject_name . ' - ' . $progress->subject_type . 
                       ' has been rejected by admin' . 
                       ($remark ? '. Reason: ' . $remark : '');
            }

            $type = 'Student Progress';
            send_notification($user, $title, $body, $type);
        }

        return ResponseService::successResponse('Status updated successfully');
    }

    public function getStudentsProgressClassSection(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Student Progress');
        $students = DB::table('students')
            ->join('users', 'students.user_id', '=', 'users.id')
            ->select('students.id', DB::raw("CONCAT(users.first_name, ' ', users.last_name) AS full_name"))
            ->where('students.class_section_id', $request->class_section_id)
            ->whereNull("users.deleted_at")
            ->get();

        return response()->json($students);
    }
    
    public function checkDuplicate(Request $request)
    {
        $firstName = $request->input('first_name');
        $lastName = $request->input('last_name');
        $checkActiveOnly = $request->input('check_active_only', false);

        $query = User::where('first_name', $firstName)
            ->where('last_name', $lastName)
            ->whereHas('roles', function ($q) {
                $q->where('name', 'Student'); // Ensure it's a student role
            });

        if ($checkActiveOnly) {
            $query->where('status', 1); // Only check for active students
        } else {
            $query->withTrashed(); // Include trashed (inactive) students if not checking active only
        }

        $duplicateStudent = $query->exists(); // Use exists() for efficiency

        return response()->json(['duplicate' => $duplicateStudent]);
    }

    public function validateTaxPayerTin(Request $request)
    {
        try {
            DB::beginTransaction();
    
            $schoolId = auth()->user()->school_id;
            $tin = $request->input('tin', '');
            $idType = $request->input('id_type', '');
            $idValue = $request->input('id_value', '');
    
            if (empty($tin) || empty($idType) || empty($idValue)) {
                return response()->json([
                    'success' => false,
                    'message' => 'All fields are required',
                    'errors' => [
                        'tin' => empty($tin) ? 'TIN is required' : null,
                        'id_type' => empty($idType) ? 'ID type is required' : null,
                        'id_value' => empty($idValue) ? 'ID value is required' : null,
                    ]
                ], 400);
            }
    
            $validationResult = EInvoiceHelper::validateTaxPayerTin($schoolId, $tin, $idType, $idValue);
    
            $status = $validationResult['success'] ? 1 : 0;
            DB::table('e_invoice_guardian')
                ->where('tax_identification_number', $tin)
                ->update(['status' => $status]);
    
            DB::commit();
    
            return response()->json([
                'success' => $validationResult['success'],
                'message' => $validationResult['message'],
                'status' => $status
            ]);
    
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Validation failed: ' . $e->getMessage(),
                'status' => 0
            ], 500);
        }
    }
}    
