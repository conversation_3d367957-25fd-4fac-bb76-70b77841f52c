{"__meta": {"id": "01JXBVYGBG82XKYXYC20DQYR89", "datetime": "2025-06-10 10:50:41", "utime": **********.393803, "method": "GET", "uri": "/payroll/1?sort=id&order=desc&search=&month=1&year=2025", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": ********38.796965, "end": **********.39383, "duration": 2.596865177154541, "duration_str": "2.6s", "measures": [{"label": "Booting", "start": ********38.796965, "relative_start": 0, "end": **********.17037, "relative_end": **********.17037, "duration": 0.*****************, "duration_str": "373ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.170384, "relative_start": 0.*****************, "end": **********.393834, "relative_end": 4.0531158447265625e-06, "duration": 2.***************, "duration_str": "2.22s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.194865, "relative_start": 0.****************, "end": **********.209121, "relative_end": **********.209121, "duration": 0.014256000518798828, "duration_str": "14.26ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.389977, "relative_start": 2.****************, "end": **********.39068, "relative_end": **********.39068, "duration": 0.0007030963897705078, "duration_str": "703μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "89MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.10", "Environment": "local", "Debug Mode": "Enabled", "URL": "schola.one", "Timezone": "Asia/Kuala_Lumpur", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 210, "nb_statements": 210, "nb_visible_statements": 210, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.27795999999999993, "accumulated_duration_str": "278ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 110 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `users` where `id` = 2 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.23262, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "schola", "explain": null, "start_percent": 0, "width_percent": 0.306}, {"sql": "select * from `schools` where `schools`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 13}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "Role", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckRole.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.240277, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "checkSchoolStatus:13", "source": {"index": 22, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FMiddleware%2FCheckSchoolStatus.php&line=13", "ajax": false, "filename": "CheckSchoolStatus.php", "line": "13"}, "connection": "schola", "explain": null, "start_percent": 0.306, "width_percent": 0.198}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (2) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 27}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "Role", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckRole.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2469978, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "schola", "explain": null, "start_percent": 0.504, "width_percent": 0.41}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 304}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 174}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 175}], "start": **********.254675, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:266", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=266", "ajax": false, "filename": "PermissionRegistrar.php", "line": "266"}, "connection": "schola", "explain": null, "start_percent": 0.914, "width_percent": 0.518}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 613, 1259, 1260, 1261, 1262, 1263, 1265, 1266, 1267, 1268, 1269, 1270, 1271, 1272, 1273)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 304}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 174}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 175}], "start": **********.264672, "duration": 0.06353, "duration_str": "63.53ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:266", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=266", "ajax": false, "filename": "PermissionRegistrar.php", "line": "266"}, "connection": "schola", "explain": null, "start_percent": 1.432, "width_percent": 22.856}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 2 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [2, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.668096, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "schola", "explain": null, "start_percent": 24.288, "width_percent": 0.482}, {"sql": "select * from `leave_masters` where `school_id` = 1 and exists (select * from `session_years` where `leave_masters`.`session_year_id` = `session_years`.`id` and ((month(`start_date`) <= '01' and year(`start_date`) = '2025') or (month(`start_date`) >= '01' and year(`end_date`) <= '2025')) and `session_years`.`deleted_at` is null) limit 1", "type": "query", "params": [], "bindings": [1, "01", "2025", "01", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 183}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.679367, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:183", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 183}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=183", "ajax": false, "filename": "PayrollController.php", "line": "183"}, "connection": "schola", "explain": null, "start_percent": 24.77, "width_percent": 0.547}, {"sql": "select count(*) as aggregate from `staffs` where exists (select * from `users` where `staffs`.`user_id` = `users`.`id` and `deleted_at` is null and `school_id` = 1)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 201}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.682811, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:201", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 201}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=201", "ajax": false, "filename": "PayrollController.php", "line": "201"}, "connection": "schola", "explain": null, "start_percent": 25.317, "width_percent": 0.712}, {"sql": "select * from `staffs` where exists (select * from `users` where `staffs`.`user_id` = `users`.`id` and `deleted_at` is null and `school_id` = 1) order by `id` desc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.686695, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:204", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=204", "ajax": false, "filename": "PayrollController.php", "line": "204"}, "connection": "schola", "explain": null, "start_percent": 26.029, "width_percent": 1.374}, {"sql": "select * from `users` where `users`.`id` in (6, 49, 55, 56, 57, 58, 1325, 1495, 2036, 2063, 2649, 5255, 5355, 6725, 7172, 7258, 7506, 7613, 8577, 8688, 8690)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.692244, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:204", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=204", "ajax": false, "filename": "PayrollController.php", "line": "204"}, "connection": "schola", "explain": null, "start_percent": 27.403, "width_percent": 0.363}, {"sql": "select * from `expenses` where `expenses`.`staff_id` in (1, 4, 5, 6, 7, 8, 51, 67, 100, 104, 169, 298, 307, 362, 366, 367, 420, 426, 482, 485, 486)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.6993499, "duration": 0.00264, "duration_str": "2.64ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:204", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=204", "ajax": false, "filename": "PayrollController.php", "line": "204"}, "connection": "schola", "explain": null, "start_percent": 27.767, "width_percent": 0.95}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = '2025' and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = '2025' and not `type` = 'Full') as `half_leave` from `leaves` where `leaves`.`user_id` in (6, 49, 55, 56, 57, 58, 1325, 1495, 2036, 2063, 2649, 5255, 5355, 6725, 7172, 7258, 7506, 7613, 8577, 8688, 8690) and `status` = 1", "type": "query", "params": [], "bindings": ["01", "2025", "Full", "01", "2025", "Full", 6, 49, 55, 56, 57, 58, 1325, 1495, 2036, 2063, 2649, 5255, 5355, 6725, 7172, 7258, 7506, 7613, 8577, 8688, 8690, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.708371, "duration": 0.00322, "duration_str": "3.22ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:204", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=204", "ajax": false, "filename": "PayrollController.php", "line": "204"}, "connection": "schola", "explain": null, "start_percent": 28.716, "width_percent": 1.158}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= 3000 AND (max_range >= 3000 OR max_range IS NULL)", "type": "query", "params": [], "bindings": [3000, 3000], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.736314, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:229", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=229", "ajax": false, "filename": "PayrollController.php", "line": "229"}, "connection": "schola", "explain": null, "start_percent": 29.875, "width_percent": 0.363}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 486 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [486, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.73966, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 30.238, "width_percent": 0.374}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= 2500 AND (max_range >= 2500 OR max_range IS NULL)", "type": "query", "params": [], "bindings": [2500, 2500], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.7475212, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:229", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=229", "ajax": false, "filename": "PayrollController.php", "line": "229"}, "connection": "schola", "explain": null, "start_percent": 30.612, "width_percent": 0.425}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 485 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [485, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.751284, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 31.037, "width_percent": 0.342}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 482 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [482, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.756552, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 31.379, "width_percent": 0.306}, {"sql": "select * from `staffs` where `staffs`.`id` = 426 limit 1", "type": "query", "params": [], "bindings": [426], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.762521, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 31.684, "width_percent": 0.299}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '03' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '03' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 7613", "type": "query", "params": [], "bindings": ["03", 2025, "Full", "03", 2025, "Full", 1, 7613], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.767077, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 31.983, "width_percent": 0.468}, {"sql": "select * from `staffs` where `staffs`.`id` = 426 limit 1", "type": "query", "params": [], "bindings": [426], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.7707589, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 32.451, "width_percent": 0.273}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 7613", "type": "query", "params": [], "bindings": ["01", 2025, "Full", "01", 2025, "Full", 1, 7613], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.774755, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 32.724, "width_percent": 0.471}, {"sql": "select * from `staffs` where `staffs`.`id` = 426 limit 1", "type": "query", "params": [], "bindings": [426], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.778434, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 33.195, "width_percent": 0.273}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 7613", "type": "query", "params": [], "bindings": ["04", 2025, "Full", "04", 2025, "Full", 1, 7613], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.782472, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 33.469, "width_percent": 0.439}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 426 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [426, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.785766, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 33.908, "width_percent": 0.705}, {"sql": "select * from `staffs` where `staffs`.`id` = 420 limit 1", "type": "query", "params": [], "bindings": [420], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.794374, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 34.613, "width_percent": 0.302}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 7506", "type": "query", "params": [], "bindings": ["04", 2025, "Full", "04", 2025, "Full", 1, 7506], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.798256, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 34.915, "width_percent": 0.385}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 420 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [420, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.801351, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 35.3, "width_percent": 0.385}, {"sql": "select * from `staffs` where `staffs`.`id` = 367 limit 1", "type": "query", "params": [], "bindings": [367], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.8067992, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 35.685, "width_percent": 0.288}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '03' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '03' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 7258", "type": "query", "params": [], "bindings": ["03", 2025, "Full", "03", 2025, "Full", 1, 7258], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.8108358, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 35.973, "width_percent": 0.529}, {"sql": "select * from `staffs` where `staffs`.`id` = 367 limit 1", "type": "query", "params": [], "bindings": [367], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.814641, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 36.502, "width_percent": 0.407}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 7258", "type": "query", "params": [], "bindings": ["04", 2025, "Full", "04", 2025, "Full", 1, 7258], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.819338, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 36.908, "width_percent": 0.371}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= 1200 AND (max_range >= 1200 OR max_range IS NULL)", "type": "query", "params": [], "bindings": [1200, 1200], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.822197, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:229", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=229", "ajax": false, "filename": "PayrollController.php", "line": "229"}, "connection": "schola", "explain": null, "start_percent": 37.279, "width_percent": 0.371}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 367 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [367, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.826055, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 37.649, "width_percent": 0.457}, {"sql": "select * from `staffs` where `staffs`.`id` = 366 limit 1", "type": "query", "params": [], "bindings": [366], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.8319712, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 38.106, "width_percent": 0.288}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 7172", "type": "query", "params": [], "bindings": ["04", 2025, "Full", "04", 2025, "Full", 1, 7172], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.836221, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 38.394, "width_percent": 0.468}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 366 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [366, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.839645, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 38.862, "width_percent": 0.317}, {"sql": "select * from `staffs` where `staffs`.`id` = 362 limit 1", "type": "query", "params": [], "bindings": [362], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.844985, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 39.178, "width_percent": 0.291}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 6725", "type": "query", "params": [], "bindings": ["01", 2025, "Full", "01", 2025, "Full", 1, 6725], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.84935, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 39.47, "width_percent": 0.504}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 362 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [362, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.85291, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 39.973, "width_percent": 0.36}, {"sql": "select * from `staffs` where `staffs`.`id` = 307 limit 1", "type": "query", "params": [], "bindings": [307], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.85858, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 40.333, "width_percent": 0.288}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 5355", "type": "query", "params": [], "bindings": ["02", 2025, "Full", "02", 2025, "Full", 1, 5355], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.862514, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 40.621, "width_percent": 0.457}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 307 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [307, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.8657792, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 41.078, "width_percent": 0.669}, {"sql": "select * from `staffs` where `staffs`.`id` = 298 limit 1", "type": "query", "params": [], "bindings": [298], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.872449, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 41.747, "width_percent": 0.327}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 5255", "type": "query", "params": [], "bindings": ["02", 2025, "Full", "02", 2025, "Full", 1, 5255], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.876862, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 42.074, "width_percent": 0.486}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 298 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [298, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.88028, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 42.56, "width_percent": 0.425}, {"sql": "select * from `staffs` where `staffs`.`id` = 169 limit 1", "type": "query", "params": [], "bindings": [169], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.8859909, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 42.985, "width_percent": 0.273}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 2649", "type": "query", "params": [], "bindings": ["02", 2025, "Full", "02", 2025, "Full", 1, 2649], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.889814, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 43.258, "width_percent": 0.536}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 169 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [169, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.8933659, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 43.794, "width_percent": 0.324}, {"sql": "select * from `staffs` where `staffs`.`id` = 104 limit 1", "type": "query", "params": [], "bindings": [104], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.899361, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 44.118, "width_percent": 0.277}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '10' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '10' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 2063", "type": "query", "params": [], "bindings": ["10", 2024, "Full", "10", 2024, "Full", 1, 2063], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.903353, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 44.395, "width_percent": 0.504}, {"sql": "select * from `staffs` where `staffs`.`id` = 104 limit 1", "type": "query", "params": [], "bindings": [104], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.907074, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 44.899, "width_percent": 0.824}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 2063", "type": "query", "params": [], "bindings": ["02", 2025, "Full", "02", 2025, "Full", 1, 2063], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.913016, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 45.722, "width_percent": 0.414}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 104 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [104, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.916244, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 46.136, "width_percent": 0.313}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 100 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [100, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.922009, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 46.449, "width_percent": 0.317}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 67 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [67, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9280279, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 46.766, "width_percent": 0.342}, {"sql": "select * from `staffs` where `staffs`.`id` = 51 limit 1", "type": "query", "params": [], "bindings": [51], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.933504, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 47.107, "width_percent": 0.295}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '08' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '08' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 1325", "type": "query", "params": [], "bindings": ["08", 2024, "Full", "08", 2024, "Full", 1, 1325], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.937806, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 47.403, "width_percent": 0.471}, {"sql": "select * from `staffs` where `staffs`.`id` = 51 limit 1", "type": "query", "params": [], "bindings": [51], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.9414458, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 47.874, "width_percent": 0.277}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 1325", "type": "query", "params": [], "bindings": ["02", 2025, "Full", "02", 2025, "Full", 1, 1325], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.945471, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 48.151, "width_percent": 0.486}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 51 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [51, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9488351, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 48.636, "width_percent": 0.356}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.95438, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 48.993, "width_percent": 0.291}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["01", 2024, "Full", "01", 2024, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.958762, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 49.284, "width_percent": 0.371}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.9623458, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 49.655, "width_percent": 0.284}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2023 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2023 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["12", 2023, "Full", "12", 2023, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.966194, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 49.939, "width_percent": 0.468}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.969781, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 50.407, "width_percent": 0.309}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["12", 2024, "Full", "12", 2024, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.973797, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 50.716, "width_percent": 0.439}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.977224, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 51.155, "width_percent": 0.295}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '11' and year(`date`) = 2023 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '11' and year(`date`) = 2023 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["11", 2023, "Full", "11", 2023, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.9816308, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 51.45, "width_percent": 0.403}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.9850268, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 51.853, "width_percent": 0.345}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '06' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '06' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["06", 2024, "Full", "06", 2024, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.9894552, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 52.198, "width_percent": 0.414}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.992985, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 52.612, "width_percent": 0.281}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["02", 2024, "Full", "02", 2024, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.997102, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 52.893, "width_percent": 0.443}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.000795, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 53.335, "width_percent": 0.277}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '07' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '07' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["07", 2024, "Full", "07", 2024, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.004755, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 53.612, "width_percent": 0.421}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.008292, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 54.033, "width_percent": 0.281}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '08' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '08' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["08", 2024, "Full", "08", 2024, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.012546, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 54.314, "width_percent": 0.425}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.016095, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 54.738, "width_percent": 0.338}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '11' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '11' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["11", 2024, "Full", "11", 2024, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.020256, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 55.076, "width_percent": 0.381}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.023598, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 55.458, "width_percent": 0.252}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["01", 2025, "Full", "01", 2025, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.0271971, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 55.709, "width_percent": 0.371}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.030606, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 56.08, "width_percent": 0.248}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["02", 2025, "Full", "02", 2025, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.033939, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 56.328, "width_percent": 0.399}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.0364988, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 56.728, "width_percent": 0.284}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '03' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '03' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["03", 2025, "Full", "03", 2025, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.040446, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 57.012, "width_percent": 0.428}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= 2000 AND (max_range >= 2000 OR max_range IS NULL)", "type": "query", "params": [], "bindings": [2000, 2000], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.043329, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:229", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=229", "ajax": false, "filename": "PayrollController.php", "line": "229"}, "connection": "schola", "explain": null, "start_percent": 57.44, "width_percent": 0.306}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 8 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [8, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.047031, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 57.746, "width_percent": 0.399}, {"sql": "select * from `staffs` where `staffs`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.053397, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 58.145, "width_percent": 0.306}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 57", "type": "query", "params": [], "bindings": ["01", 2024, "Full", "01", 2024, "Full", 1, 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.0577528, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 58.451, "width_percent": 0.46}, {"sql": "select * from `staffs` where `staffs`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.061238, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 58.911, "width_percent": 0.302}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2023 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2023 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 57", "type": "query", "params": [], "bindings": ["12", 2023, "Full", "12", 2023, "Full", 1, 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.0648859, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 59.214, "width_percent": 0.471}, {"sql": "select * from `staffs` where `staffs`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.068331, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 59.685, "width_percent": 0.378}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 57", "type": "query", "params": [], "bindings": ["12", 2024, "Full", "12", 2024, "Full", 1, 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.072625, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 60.063, "width_percent": 0.399}, {"sql": "select * from `staffs` where `staffs`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.076038, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 60.462, "width_percent": 0.27}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '11' and year(`date`) = 2023 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '11' and year(`date`) = 2023 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 57", "type": "query", "params": [], "bindings": ["11", 2023, "Full", "11", 2023, "Full", 1, 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.080016, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 60.732, "width_percent": 0.317}, {"sql": "select * from `staffs` where `staffs`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.0828009, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 61.048, "width_percent": 0.219}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '05' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '05' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 57", "type": "query", "params": [], "bindings": ["05", 2024, "Full", "05", 2024, "Full", 1, 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.0865889, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 61.268, "width_percent": 0.385}, {"sql": "select * from `staffs` where `staffs`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.089764, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 61.653, "width_percent": 0.23}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '06' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '06' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 57", "type": "query", "params": [], "bindings": ["06", 2024, "Full", "06", 2024, "Full", 1, 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.093659, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 61.883, "width_percent": 0.403}, {"sql": "select * from `staffs` where `staffs`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.097322, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 62.286, "width_percent": 0.27}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '07' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '07' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 57", "type": "query", "params": [], "bindings": ["07", 2024, "Full", "07", 2024, "Full", 1, 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.101363, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 62.556, "width_percent": 0.435}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.104609, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 62.991, "width_percent": 0.281}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.107843, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 63.272, "width_percent": 0.309}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1099129, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 63.581, "width_percent": 0.259}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1128151, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 63.84, "width_percent": 0.417}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.115261, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 64.257, "width_percent": 0.291}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.118367, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 64.549, "width_percent": 0.374}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1206949, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 64.923, "width_percent": 0.263}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.123997, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 65.186, "width_percent": 0.371}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= ? AND (max_range >= ? OR max_range IS NULL)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.125417, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 65.556, "width_percent": 0.173}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = ? and `expenses`.`staff_id` is not null and `month` = ? and `year` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.126518, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 65.729, "width_percent": 0.266}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.129391, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 65.995, "width_percent": 0.252}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1324172, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 66.247, "width_percent": 0.54}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.13566, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 66.787, "width_percent": 0.205}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.138401, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 66.992, "width_percent": 0.511}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.140954, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 67.503, "width_percent": 0.241}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.143131, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 67.744, "width_percent": 0.504}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.145532, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 68.247, "width_percent": 0.187}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.147573, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 68.434, "width_percent": 0.572}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.150255, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 69.006, "width_percent": 0.205}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.152494, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 69.211, "width_percent": 0.363}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.154393, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 69.575, "width_percent": 0.148}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.156877, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 69.722, "width_percent": 0.432}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.159085, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 70.154, "width_percent": 0.148}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1608129, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 70.301, "width_percent": 0.335}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1632242, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 70.636, "width_percent": 0.223}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.165375, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 70.859, "width_percent": 0.425}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.167693, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 71.284, "width_percent": 0.194}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.16993, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 71.478, "width_percent": 0.378}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.172369, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 71.856, "width_percent": 0.252}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1744752, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 72.107, "width_percent": 0.482}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1775482, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 72.59, "width_percent": 0.234}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1804059, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 72.823, "width_percent": 0.389}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= ? AND (max_range >= ? OR max_range IS NULL)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.18502, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 73.212, "width_percent": 0.266}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = ? and `expenses`.`staff_id` is not null and `month` = ? and `year` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.186246, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 73.478, "width_percent": 0.219}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1886292, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 73.698, "width_percent": 0.162}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.191083, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 73.86, "width_percent": 0.342}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.193741, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 74.201, "width_percent": 0.252}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.195998, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 74.453, "width_percent": 0.234}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1973338, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 74.687, "width_percent": 0.151}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1991782, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 74.838, "width_percent": 0.478}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.201567, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 75.317, "width_percent": 0.241}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2042918, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 75.558, "width_percent": 0.338}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2063742, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 75.896, "width_percent": 0.295}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.209118, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 76.191, "width_percent": 0.331}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.211039, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 76.522, "width_percent": 0.194}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2131672, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 76.716, "width_percent": 0.475}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.215384, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 77.191, "width_percent": 0.227}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.217445, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 77.418, "width_percent": 0.313}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.219377, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 77.731, "width_percent": 0.169}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.222094, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 77.9, "width_percent": 0.324}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2236822, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 78.223, "width_percent": 0.173}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.225525, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 78.396, "width_percent": 0.248}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= ? AND (max_range >= ? OR max_range IS NULL)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.226605, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 78.644, "width_percent": 0.144}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = ? and `expenses`.`staff_id` is not null and `month` = ? and `year` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.227752, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 78.788, "width_percent": 0.317}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.230704, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 79.105, "width_percent": 0.176}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.232469, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 79.281, "width_percent": 0.259}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2339778, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 79.54, "width_percent": 0.277}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.236564, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 79.817, "width_percent": 0.374}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.238502, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 80.191, "width_percent": 0.234}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.240674, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 80.425, "width_percent": 0.457}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2430189, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 80.882, "width_percent": 0.209}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2451, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 81.091, "width_percent": 0.266}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.246571, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 81.357, "width_percent": 0.165}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.249007, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 81.523, "width_percent": 0.367}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.250788, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 81.889, "width_percent": 0.13}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.252445, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 82.019, "width_percent": 0.209}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.253673, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 82.228, "width_percent": 0.119}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2559361, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 82.346, "width_percent": 0.421}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.257877, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 82.767, "width_percent": 0.191}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.260001, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 82.958, "width_percent": 0.385}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.262489, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 83.343, "width_percent": 0.291}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2649338, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 83.634, "width_percent": 0.338}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= ? AND (max_range >= ? OR max_range IS NULL)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.266814, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 83.973, "width_percent": 0.219}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = ? and `expenses`.`staff_id` is not null and `month` = ? and `year` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2680528, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 84.192, "width_percent": 0.27}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.271719, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 84.462, "width_percent": 0.248}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.274241, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 84.71, "width_percent": 0.558}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.27754, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 85.268, "width_percent": 0.263}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.28063, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 85.53, "width_percent": 0.608}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.28423, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 86.138, "width_percent": 0.288}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.287041, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 86.426, "width_percent": 0.583}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2899818, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 87.009, "width_percent": 0.335}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.292665, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 87.344, "width_percent": 0.468}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.295161, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 87.811, "width_percent": 0.237}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.298042, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 88.049, "width_percent": 0.64}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.301161, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 88.689, "width_percent": 0.263}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3038988, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 88.952, "width_percent": 0.662}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3072212, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 89.614, "width_percent": 0.223}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3098822, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 89.837, "width_percent": 0.669}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.313127, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 90.506, "width_percent": 0.227}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.315597, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 90.732, "width_percent": 0.45}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3186631, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 91.182, "width_percent": 0.299}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3209891, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 91.481, "width_percent": 0.565}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.324259, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 92.046, "width_percent": 0.453}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3278449, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 92.499, "width_percent": 0.586}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.33111, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 93.085, "width_percent": 0.327}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.334086, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 93.413, "width_percent": 0.46}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.336466, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 93.873, "width_percent": 0.255}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.339906, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 94.129, "width_percent": 0.64}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.342993, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 94.769, "width_percent": 0.212}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3461702, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 94.981, "width_percent": 0.738}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.349768, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 95.719, "width_percent": 0.27}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.352994, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 95.989, "width_percent": 0.694}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3561149, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 96.683, "width_percent": 0.194}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3585699, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 96.877, "width_percent": 0.853}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.362166, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 97.73, "width_percent": 0.216}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.364625, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 97.946, "width_percent": 0.558}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.367897, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 98.503, "width_percent": 0.252}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3704119, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 98.755, "width_percent": 0.597}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= ? AND (max_range >= ? OR max_range IS NULL)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.376644, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 99.352, "width_percent": 0.324}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = ? and `expenses`.`staff_id` is not null and `month` = ? and `year` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.378414, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 99.676, "width_percent": 0.324}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 13675, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Leave": {"value": 364, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FLeave.php&line=1", "ajax": false, "filename": "Leave.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 227, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Staff": {"value": 105, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FStaff.php&line=1", "ajax": false, "filename": "Staff.php", "line": "?"}}, "App\\Models\\Expense": {"value": 87, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=1", "ajax": false, "filename": "Expense.php", "line": "?"}}, "App\\Models\\User": {"value": 22, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\School": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FSchool.php&line=1", "ajax": false, "filename": "School.php", "line": "?"}}, "App\\Models\\LeaveMaster": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FLeaveMaster.php&line=1", "ajax": false, "filename": "LeaveMaster.php", "line": "?"}}}, "count": 14482, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => payroll-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-993602278 data-indent-pad=\"  \"><span class=sf-dump-note>payroll-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">payroll-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-993602278\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.676805, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "https://schola.test/payroll/1?month=1&order=desc&search=&sort=id&year=2025", "action_name": "payroll.show", "controller_action": "App\\Http\\Controllers\\PayrollController@show", "uri": "GET payroll/{payroll}", "controller": "App\\Http\\Controllers\\PayrollController@show<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=165\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=165\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/PayrollController.php:165-313</a>", "middleware": "web, Role, auth, checkSchoolStatus, status, language", "duration": "2.62s", "peak_memory": "94MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-3623862 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sort</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n  \"<span class=sf-dump-key>order</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>month</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>year</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2025</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-3623862\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1622756013 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1622756013\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-346250291 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">schola.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">k0nsyLuN5aAz6u8lgTG1r68KyFeDp1NRQbXnBoOH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">https://schola.test/payroll</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"742 characters\">XSRF-TOKEN=eyJpdiI6IitWbEVUY1NPaGp1WlF4a2EvRjVMU1E9PSIsInZhbHVlIjoiV3NWMlBvUGdhUDZSdkd0S3BNaGZ5N2lxcks1eUxsZGNMYzROZ1lKcGNQUmtlTk40eUUrNFVFTUY2T3ZPNWpIb0FWQTh1Y2x0RXEvRURDM01mOUdCOTFkeUZTU3BUUGdmMjVFU0p3OVRTd2haSktRNURDUVhyNFF1R3EvRytOeDAiLCJtYWMiOiIwNGMwMzNmNDkzZGQ2MmEzYmE5N2MwNWJjNGQzMTEwMzJlMjdlMGQ5ZWVlY2JkODk0NTc2NzhlZDhkMzE1MzRkIiwidGFnIjoiIn0%3D; schola_saas_school_management_system_session=eyJpdiI6IjNpdUQwdm95cEhmMFpUUVRyR0JaM1E9PSIsInZhbHVlIjoiM1U2TlVxdmozbXgwSzIrQkRySFBFM0ZXemhtMDd1R053K3dqNmlWbjR5bnhXSm9rOG9xTUs2dXdaZWx1Q1BjWUtaNC9qUFFnZzYwbW1MUTdFeTFKbWxMOWF3NVNhL3lPRTFZalQ0Mk1VUzBqRXN1emFZVUVvdWFiY0FEbUUydTIiLCJtYWMiOiIzZmZhZTVjNTRiMmM2NTg4ZjBjZTg2YjM3NWUyMzliYTUzMTk4OTk1ZTVmMDQwODNhYzdkZGEzOGE5ZmFhYzA2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-346250291\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1183023439 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0nsyLuN5aAz6u8lgTG1r68KyFeDp1NRQbXnBoOH</span>\"\n  \"<span class=sf-dump-key>schola_saas_school_management_system_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IQrLeRaEfTugtoRzBcuJ4wOlCeh4PG6VwaBH418U</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1183023439\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-385185711 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 10 Jun 2025 02:50:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-385185711\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1260202079 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0nsyLuN5aAz6u8lgTG1r68KyFeDp1NRQbXnBoOH</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">https://schola.test/payroll</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>landing_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>language</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Language\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Language</span></span> {<a class=sf-dump-ref>#3547</a><samp data-depth=2 class=sf-dump-compact>\n    #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">languages</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n    +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n    #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n    +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n    +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n    +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">English</span>\"\n      \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"7 characters\">en.json</span>\"\n      \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>is_rtl</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">English</span>\"\n      \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"7 characters\">en.json</span>\"\n      \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>is_rtl</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n    +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n    +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">code</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">is_rtl</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n    </samp>]\n  </samp>}\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1749518683</span>\n  </samp>]\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1260202079\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://schola.test/payroll/1?month=1&order=desc&search=&sort=id&year=2025", "action_name": "payroll.show", "controller_action": "App\\Http\\Controllers\\PayrollController@show"}, "badge": null}}