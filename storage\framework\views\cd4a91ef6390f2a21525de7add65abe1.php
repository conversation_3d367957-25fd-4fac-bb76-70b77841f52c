

<?php $__env->startSection('title'); ?>
    <?php echo e(__('payroll')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<style>
    #table_list th {
        padding: 0 18px; 
        text-align:center;
    }
</style>
    <div class="content-wrapper">
        <div class="page-header">
            <h3 class="page-title">
                <?php echo e(__('manage') . ' ' . __('payroll')); ?>

            </h3>
        </div>
        <form action="<?php echo e(route('payroll.store')); ?>" method="post" class="create-form" novalidate="novalidate" data-success-function="formSuccessFunction">
            <?php echo csrf_field(); ?>
            <div class="row">

                <div class="col-md-12 grid-margin stretch-card">
                    <div class="card">
                        <div class="card-body">
                            <h4 class="card-title">
                                <?php echo e(__('create') . ' ' . __('payroll')); ?>

                            </h4>

                            <div class="row">
                                <div class="form-group col-sm-12 col-md-3">
                                    <label><?php echo e(__('select')); ?> <?php echo e(__('month')); ?> <span
                                            class="text-danger">*</span></label>
                                    <?php echo Form::select('month', $months, null, ['class' => 'form-control', 'id' => 'month']); ?>

                                </div>

                                <div class="form-group col-sm-12 col-md-3">
                                    <label><?php echo e(__('select')); ?> <?php echo e(__('year')); ?> <span
                                            class="text-danger">*</span></label>
                                    <?php echo Form::selectRange(
                                        'year',
                                        $sessionYear,
                                        date('Y', strtotime(Carbon\Carbon::now())),
                                        date('Y', strtotime(Carbon\Carbon::now())),
                                        ['class' => 'form-control', 'id' => 'year'],
                                    ); ?>

                                </div>

                                <div class="form-group col-sm-12 col-md-2 mt-4">
                                    <input class="btn btn-theme" id="search" type="button" value=<?php echo e(__('search')); ?>>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-12 grid-margin stretch-card">
                    <div class="card">
                        <div class="card-body">
                            <h4 class="card-title"><?php echo e(__('list') . ' ' . __('payroll')); ?></h4>
                            <div class="row" id="toolbar">
                                <div class="form-group col-sm-12 col-md-3">
                                    <label class="filter-menu"><?php echo e(__('date')); ?> <span class="text-danger">*</span></label>
                                    <?php echo Form::text('date', null, ['required', 'class' => 'form-control datepicker-popup', 'id' => 'date']); ?>

                                </div>
                            </div>
                            <div class="staff-table">

                                <table aria-describedby="mydesc" class='table' id='table_list'
                                       data-toggle="table" data-url="<?php echo e(route('payroll.show', [1])); ?>"
                                       data-click-to-select="true" data-side-pagination="server" data-pagination="false"
                                       data-page-list="[5, 10, 20, 50, 100, 200, All]" data-search="true" data-show-columns="false"
                                       data-show-refresh="true" data-fixed-columns="false" data-fixed-number="2"
                                       data-fixed-right-number="1" data-trim-on-search="false" data-mobile-responsive="true"
                                       data-sort-name="id" data-sort-order="desc" data-maintain-selected="true"
                                       data-export-data-type="basic"  data-query-params="payrollQueryParams"
                                       data-toolbar="#toolbar"
                                       data-export-options='{ "fileName": "payroll-list-<?= date('d-m-y') ?>"
                                    ,"ignoreColumn":["operate"]}' data-show-export="true" data-escape="true" data-response-handler="responseHandler" data-check-on-init="true">
                                    <thead>
                                        <tr>
                                            <th colspan="9">Staff Details</th>
                                            <th colspan="4">Earnings</th>
                                            <th colspan="2">Deductions</th>
                                            <th colspan="4">Employee</th>
                                            <th colspan="3">Employer</th>
                                            <th scope="col" rowspan="2" data-field="net_salary" data-formatter="netSalaryInputFormatter" data-sortable="false"><?php echo e(__('net_salary')); ?></th> 
                                            <th scope="col" rowspan="2" data-field="operate" data-escape="false"><?php echo e(__('action')); ?></th>
                                        </tr>
                                        <tr>
                                            <th data-field="state" data-checkbox="true"></th>
                                            <th scope="col" data-field="id" data-sortable="true" data-visible="false"><?php echo e(__('id')); ?></th>
                                            <th style="width:400px" scope="col" data-field="no"><?php echo e(__('no.')); ?></th>
                                            <th scope="col" data-field="user.full_name"><?php echo e(__('name')); ?></th>
                                            <th scope="col" data-formatter="salaryStatusFormatter" data-field="status"><?php echo e(__('status')); ?></th>
                                            <th scope="col" data-field="salary" data-formatter="salaryInputFormatter" data-sortable="false"><?php echo e(__('basic_salary')); ?></th>
                                            <th scope="col" class="text-wrap" data-field="paid_leaves" data-sortable="false"><?php echo e(__('Monthly Allowed Paid Leaves')); ?></th>
                                            <th scope="col" class="text-wrap" data-field="total_leaves" data-sortable="false"><?php echo e(__('taken_leaves')); ?></th>
                                            <th scope="col" class="text-wrap" data-field="salary_deduction" data-sortable="false" data-escape="false" data-visible="false"><?php echo e(__('salary_deduction')); ?></th>
                                            <th scope="col" data-field="allowance" data-formatter="allowanceInputFormatter" data-sortable="false"><?php echo e(__('allowance')); ?></th>
                                            <th scope="col" data-field="claims" data-formatter="claimsInputFormatter" data-sortable="false"><?php echo e(__('claims')); ?></th>
                                            <th scope="col" data-field="earn_others" data-formatter="earnOthersInputFormatter" data-sortable="false"><?php echo e(__('others')); ?></th>
                                            <th scope="col" class="payroll_remark" data-field="earn_remark" data-formatter="earnRemarkFormatter" data-sortable="false"><?php echo e(__('payroll_remark')); ?></th>
                                            <th scope="col" data-field="deduction_others" data-formatter="deductionOthersInputFormatter" data-sortable="false"><?php echo e(__('others')); ?></th>
                                            <th scope="col" class="payroll_remark" data-field="deduction_remark" data-formatter="deductionRemarkFormatter" data-sortable="false"><?php echo e(__('payroll_remark')); ?></th>                                      
                                            <!-- <th scope="col" data-field="leave_without_pay" data-formatter="LWPInputFormatter" data-sortable="false" data-visible="false"><?php echo e(__('leave_without_pay')); ?></th> -->
                                            <th scope="col" data-field="employee_epf" data-formatter="employeeEPFInputFormatter" data-sortable="false"><?php echo e(__('employee_epf_11%')); ?></th>
                                            <th scope="col" data-field="employee_socso" data-formatter="employeeSocsoInputFormatter" data-sortable="false"><?php echo e(__('employee_socso')); ?></th>
                                            <th scope="col" data-field="employee_eis" data-formatter="employeeEISInputFormatter" data-sortable="false"><?php echo e(__('employee_eis')); ?></th>
                                            <th scope="col" data-field="employee_pcb" data-formatter="employeePCBInputFormatter" data-sortable="false"><?php echo e(__('employee_pcb')); ?></th>
                                            <th scope="col" data-field="employer_epf" data-formatter="employerEPFInputFormatter" data-sortable="false"><?php echo e(__('employer_epf_13%')); ?></th>
                                            <th scope="col" data-field="employer_socso" data-formatter="employerSocsoInputFormatter" data-sortable="false"><?php echo e(__('employer_socso')); ?></th>
                                            <th scope="col" data-field="employer_eis" data-formatter="employerEISInputFormatter" data-sortable="false"><?php echo e(__('employer_eis')); ?></th>
                                        </tr>
                                    </thead>
                                </table>
                                <textarea id="user_id" name="user_id" style="display: none"></textarea>
                                <input type="submit" class="btn btn-theme mt-3 float-right" value="<?php echo e(__('submit')); ?>">

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('script'); ?>
    <script>

        setTimeout(function(){
            addScrollableTable();
        }, 1000);           

        $('#search').click(function (e) {
            e.preventDefault();
            $('#table_list').bootstrapTable('refresh');
            $('.staff-table').show();
            let month = $('#month').val();
            let year = $('#year').val();
            $('#table_list').bootstrapTable('uncheckAll');

            var lastDate = getLastDateOfMonth(month, year);
            $('#date').val(lastDate);
        });
        window.onload = $('.staff-table').hide();
    </script>
    <script>
        var $tableList = $('#table_list')
        var selections = []
        var user_list = [];

        function responseHandler(res) {
            $.each(res.rows, function (i, row) {
                row.state = $.inArray(row.id, selections) !== -1
            })
            return res
        }

        $(function () {
            $tableList.on('check.bs.table check-all.bs.table uncheck.bs.table uncheck-all.bs.table',
                function (e, rowsAfter, rowsBefore) {
                    user_list = [];
                    var rows = rowsAfter
                    if (e.type === 'uncheck-all') {
                        rows = rowsBefore
                    }
                    var ids = $.map(!$.isArray(rows) ? [rows] : rows, function (row) {
                        return row.id
                    })

                    var func = $.inArray(e.type, ['check', 'check-all']) > -1 ? 'union' : 'difference'
                    selections = window._[func](selections, ids)
                    selections.forEach(element => {
                        user_list.push(element);
                    });
                    $('textarea#user_id').val(user_list);
                })
        })


        function formSuccessFunction(response) {
            setTimeout(() => {
                user_list = [];
                $('#table_list').bootstrapTable('uncheckAll');
                $('.staff-table').hide();
            }, 2000);
        }
    </script>
    <script>
        $(document).ready(function(){
                $('#table_list').on('input','input[type="number"].form-control',function(){
                    let row = $(this).closest('tr');
                    let inputName = $(this).attr('name');
                    let rowId = inputName.match(/\[(.*?)\]/)[1];

                    let basic_salary = parseFloat(row.find("input[name='basic_salary[" + rowId + "]']").val()) || 0;
                    let allowance = parseFloat(row.find("input[name='allowance[" + rowId + "]']").val()) || 0;
                    let claims = parseFloat(row.find("input[name='claims[" + rowId + "]']").val()) || 0;
                    let earnOthers = parseFloat(row.find("input[name='earn_others[" + rowId + "]']").val()) || 0;
                    let deductionOthers = parseFloat(row.find("input[name='deduction_others[" + rowId + "]']").val()) || 0;
                    // let employeeEPF = parseFloat(row.find("input[name='employee_epf[" + rowId + "]']").val()) || 0;
                    let employeeSOCSO = parseFloat(row.find("input[name='employee_socso[" + rowId + "]']").val()) || 0;
                    let employeeEIS = parseFloat(row.find("input[name='employee_eis[" + rowId + "]']").val()) || 0;
                    let employeePCB = parseFloat(row.find("input[name='employee_pcb[" + rowId + "]']").val()) || 0;

                    // Calculate EPF as 11% of the basic salary
                    let employeeEPF = basic_salary * 0.11;
                    row.find("input[name='employee_epf[" + rowId + "]']").val(employeeEPF.toFixed(2));
                    
                    // Calculate Employer EPF as 13% of the basic salary (adjust the percentage if necessary)
                    let employerEPF = basic_salary * 0.13;
                    row.find("input[name='employer_epf[" + rowId + "]']").val(employerEPF.toFixed(2));

                    if (inputName.startsWith('basic_salary')) {
                        $.ajax({
                                url: '/update-epf-contribution', // Route to update salary
                                method: 'POST',
                                headers: {
                                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                                },
                                data: {
                                    id: rowId,
                                    basic_salary: basic_salary,
                                },
                                success: function(data) {
                                // Update the row with the received data
                                row.find("input[name='employee_epf[" + rowId + "]']").val(data.employee_epf);
                                row.find("input[name='employer_epf[" + rowId + "]']").val(data.employer_epf);
                                row.find("input[name='employee_socso[" + rowId + "]']").val(data.employee_socso || 0);
                                row.find("input[name='employer_socso[" + rowId + "]']").val(data.employer_socso || 0);
                                row.find("input[name='employee_eis[" + rowId + "]']").val(data.employee_eis || 0);
                                row.find("input[name='employer_eis[" + rowId + "]']").val(data.employer_eis || 0);
                                let employeeSOCSO = parseFloat(row.find("input[name='employee_socso[" + rowId + "]']").val()) || 0;
                                let employeeEIS = parseFloat(row.find("input[name='employee_eis[" + rowId + "]']").val()) || 0;

                                let netSalary = calculateNetSalary(basic_salary,allowance,claims,earnOthers,deductionOthers,employeeEPF,employeeSOCSO,employeeEIS,employeePCB);
                                row.find('input[name="net_salary[' + rowId + ']"]').val(netSalary.toFixed(2));
                            },
                            error: function(xhr) {
                                console.error('Error updating EPF contribution:', xhr.responseText);
                            }

                        });
                    } else{
                        let netSalary = calculateNetSalary(basic_salary,allowance,claims,earnOthers,deductionOthers,employeeEPF,employeeSOCSO,employeeEIS,employeePCB);
                        row.find('input[name="net_salary[' + rowId + ']"]').val(netSalary.toFixed(2));
                    }
                });

                function calculateNetSalary(basic_salary,allowance,claims,earnOthers,deductionOthers,employeeEPF,employeeSOCSO,employeeEIS,employeePCB){
                    let netSalary = (basic_salary + allowance + claims + earnOthers) - (deductionOthers + employeeEPF + employeeSOCSO + employeeEIS + employeePCB);
                    return netSalary;
                }

                $('input[type="number"].form-control').trigger('input');

                $('#table_list').on('click', '.preview', function() {
                    let row = $(this).closest('tr');
                    let rowId = row.data('id');
                    let rowIndex = row.data('index');
                    let rowData = $('#table_list').bootstrapTable('getData')[rowIndex];
                    let staffId = rowData.id;
                    let user_id = $(this).data('id');   
                    let month = $('#month').val();
                    let year = $('#year').val();       
                    let basic_salary = parseFloat(row.find("input[name='basic_salary[" + staffId + "]']").val()) || 0;             
                    let allowance = parseFloat(row.find("input[name='allowance[" + staffId + "]']").val()) || 0;
                    let claims = parseFloat(row.find("input[name='claims[" + staffId + "]']").val()) || 0;
                    let earnOthers = parseFloat(row.find("input[name='earn_others[" + staffId + "]']").val()) || 0;
                    let deductionOthers = parseFloat(row.find("input[name='deduction_others[" + staffId + "]']").val()) || 0;
                    let earn_remark = row.find('textarea[name="earn_remark[' + staffId + ']"]').val() || " ";
                    let deduction_remark = row.find('textarea[name="deduction_remark[' + staffId + ']"]').val() || " ";
                    let employee_pcb = row.find('input[name="employee_pcb[' + staffId + ']"]').val() || 0;
                    let employee_epf= parseFloat(row.find("input[name='employee_epf[" + staffId + "]']").val()) || 0;
                    let employee_socso= parseFloat(row.find("input[name='employee_socso[" + staffId + "]']").val()) || 0;
                    let employee_eis = parseFloat(row.find("input[name='employee_eis[" + staffId + "]']").val()) || 0;
                    let employer_epf = parseFloat(row.find("input[name='employer_epf[" + staffId + "]']").val()) || 0;
                    let employer_socso= parseFloat(row.find("input[name='employer_socso[" + staffId + "]']").val()) || 0;
                    let employer_eis= parseFloat(row.find("input[name='employer_eis[" + staffId + "]']").val()) || 0;
                    let net_salary = row.find('input[name="net_salary[' + staffId + ']"]').val() || 0;
                    if (rowData) {
                        let formData = {
                            user_id: user_id,
                            basic_salary: basic_salary,
                            allowance: allowance,
                            claims: claims,
                            earn_others: earnOthers,
                            deduction_others: deductionOthers,
                            employee_epf: employee_epf,
                            employee_socso: employee_socso,
                            employee_eis: employee_eis,
                            employee_pcb: employee_pcb,
                            employer_epf: employer_epf,
                            employer_socso:employer_socso,
                            employer_eis: employer_eis,
                            month: month,
                            year: year,
                            earn_remark: earn_remark,
                            deduction_remark: deduction_remark,
                            net_salary: net_salary
                        };
                        $.ajax({
                            url:'/payroll/preview',
                            method:'POST',
                            headers:{
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                            },
                            data: {
                                previewData: formData
                            },
                            xhrFields: {
                                responseType: 'blob'
                            },
                            success: function(blob){
                                let url = window.URL.createObjectURL(blob);
                                window.open(url, '_blank');
                            }

                        });
                    }
                });
            });

            
            $('#search').click(function (e) {
                e.preventDefault();
                $('#table_list').bootstrapTable('refresh');
                $('.staff-table').show();
                let month = $('#month').val();
                let year = $('#year').val();
                $('#table_list').bootstrapTable('uncheckAll');

                var lastDate = getLastDateOfMonth(month, year);
                $('#date').val(lastDate);
            });
            window.onload = $('.staff-table').hide();

            

                
        </script>

        <script>
            var $tableList = $('#table_list');
            var selections = [];
            var user_list = [];

            function responseHandler(res) {
                $.each(res.rows, function (i, row) {
                    row.state = $.inArray(row.id, selections) !== -1;
                })
                return res;
            }

            $(function () {
                $tableList.on('check.bs.table check-all.bs.table uncheck.bs.table uncheck-all.bs.table',
                    function (e, rowsAfter, rowsBefore) {
                        user_list = [];
                        var rows = rowsAfter;
                        if (e.type === 'uncheck-all') {
                            rows = rowsBefore;
                        }
                        var ids = $.map(!$.isArray(rows) ? [rows] : rows, function (row) {
                            return row.id;
                        });

                        var func = $.inArray(e.type, ['check', 'check-all']) > -1 ? 'union' : 'difference'
                        selections = window._[func](selections, ids);
                        selections.forEach(element => {
                            user_list.push(element);
                        });
                        $('textarea#user_id').val(user_list);
                    });
            });

            function formSuccessFunction(response) {
                setTimeout(() => {
                    user_list = [];
                    $('#table_list').bootstrapTable('uncheckAll');
                    $('.staff-table').hide();
                }, 2000);
            }

        </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laragon\www\schola\resources\views/payroll/index.blade.php ENDPATH**/ ?>