

<?php $__env->startSection('title'); ?>
<?php echo e(__('Credit')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="content-wrapper">
    <div class="page-header">
        <h3 class="page-title">
            <?php echo e(__('Manage Credit')); ?>

        </h3>
    </div>

    <div class="row">
        <div class="col-md-12 grid-margin stretch-card search-container">
            <div class="card">
                <div class="card-body">
                    <form id="create-form" class="create-form common-validation-rules" action="<?php echo e(route('credit.store')); ?>" method="POST" novalidate="novalidate" data-success-function="successFunction">

                        <div class="border border-secondary rounded-lg mb-2 p-2 mb-3">
                            <div class="col-12 mt-1">
                                <h4 class="card-title">
                                    <?php echo e(__('add_credit')); ?>

                                </h4>
                                <hr>
                            </div>
                            <div class="row col-12">
                                <div class="form-group col-sm-12 col-md-12 col-lg-6">
                                    <label for="class-id"><?php echo e(__('Classes')); ?> </label>
                                    <select name="class_id" id="class-id" class="class-id form-control select2-dropdown select2-hidden-accessible" tabindex="-1" aria-hidden="true" required>
                                        <?php $__currentLoopData = $classes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($item->id); ?>"><?php echo e($item->full_name); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>

                                </div>

                                <div class="form-group col-sm-12 col-md-12 col-lg-6">
                                    <label for="student-id"><?php echo e(__('Students')); ?> </label>
                                    <select name="student_id[]" id="student-id" class="student-id form-control select2-dropdown select2-hidden-accessible" tabindex="-1" aria-hidden="true" multiple required>
                                    </select>
                                    <div class="form-check w-fit-content">
                                        <label class="form-check-label user-select-none">
                                            <input type="checkbox" class="form-check-input" id="select-all" value="1"><?php echo e(__("Select All")); ?>

                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="row col-12">

                                <div class="form-group col-md-12 col-lg-3">
                                    <label><?php echo e(__('Type')); ?> </label>

                                </div>

                                <div class="form-group col-md-12 col-lg-4">
                                    <div class="d-flex align-items-center">
                                        <div class="form-check form-check-inline">
                                            <label class="form-check-label">
                                                <?php echo Form::radio('type', 'credit', null, ['class' => 'form-check-input edit', 'id' => 'type']); ?>

                                                <?php echo e(__('Credit +')); ?>

                                            </label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <label class="form-check-label">
                                                <?php echo Form::radio('type', 'debit', null, ['class' => 'form-check-input edit', 'id' => 'type']); ?>

                                                <?php echo e(__('Debit -')); ?>

                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row col-12">

                                <div class="form-group col-md-12 col-lg-3">
                                    <label><?php echo e(__('Credit')); ?> </label>
                                    <?php echo Form::text('credit_amount', null, ['name'=>'credit_amount', 'class' => 'form-control amount','placeholder' => __('enter').' '.__('Credits').' '.__('Amount'),'id' => 'credit_amount', 'required' => true, 'min' => 0, "data-convert" => "number"]); ?>

                                </div>

                                <div class="form-group col-md-12 col-lg-4">
                                    <label><?php echo e(__('details')); ?> </label>
                                    <input type="text"
                                        name="detail"
                                        placeholder="<?php echo e(__('details')); ?>"
                                        class="form-control">
                                </div>

                            </div>

                        </div>

                        <hr>

                        <div class="form-group col-md-12">
                          <button id="button" type="button" class="btn btn-theme" onclick="confirmCreditSubmission()"><?php echo e(__('add_credit')); ?></button>
                          <input type="hidden" name="generate_invoice" id="generate_invoice" value="1">
                        </div>
                        
                        <script>
                            function confirmCreditSubmission() {
                                const type = $('input[name="type"]:checked').val();
                                if (type === 'credit') {
                                    Swal.fire({
                                        title: 'Generate Invoice?',
                                        text: 'Do you want to generate an invoice for this credit?',
                                        icon: 'question',
                                        showCancelButton: true,
                                        confirmButtonText: 'Yes',
                                        cancelButtonText: 'No'
                                    }).then((result) => {
                                        if (result.isConfirmed) {
                                            $('#generate_invoice').val('1');
                                        } else {
                                            $('#generate_invoice').val('0');
                                        }
                                        $('#create-form').submit();
                                    });
                                } else {
                                    $('#generate_invoice').val('0');
                                    $('#create-form').submit();
                                }
                            }
                        </script>
                       
                    </form>
                </div>
            </div>
        </div>
        <div class="col-md-12 grid-margin stretch-card search-container">
            <div class="card">
                <div class="card-body">
                    <h4 class="card-title">
                        <?php echo e(__('Manage Credit')); ?>

                    </h4>
                    <div class="row mt-4" id="toolbar">
                        <div class="col">
                            <label for="filter_class_id"><?php echo e(__("Student Class")); ?></label>
                            <select name="filter_class_id" id="filter_class_id" class="form-control " onchange="loadFilterStudentByClass(this)">
                                <option value="allClass">View All Class</option>
                                <?php $__currentLoopData = $classes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($item->id); ?>"><?php echo e($item->full_name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        <div class="col">
                            <label for="filter_student_id"><?php echo e(__('Student')); ?></label>
                            <select name="filter_student_id" id="filter_student_id" class="form-control">
                            </select>
                        </div>

                        <div class="col">
                            <label for="filter_start_date"> <?php echo e(__('Start Date')); ?> </label>
                            <?php echo Form::text('start_date', null, ['required', 'placeholder' => __('start_date'), 'class' => 'datepicker-popup form-control','id'=>'filter_start_date','data-date-end-date'=>"0d",'autocomplete'=>'off']); ?>

                        </div>
                        <div class="col">
                            <label for="filter_end_date"> <?php echo e(__('End Date')); ?> </label>
                            <?php echo Form::text('end_date', null, ['required', 'placeholder' => __('end_date'), 'class' => 'datepicker-popup form-control','id'=>'filter_end_date','data-date-end-date'=>"0d",'autocomplete'=>'off']); ?>

                        </div>
                    </div>


                    <div class="row">
                        <div class="col-12">
                            <table aria-describedby="mydesc" class='table reward_table' id='table_list'
                                data-toggle="table" data-url="<?php echo e(route('credit.manage-credit-show', $id ?? '')); ?>"
                                data-click-to-select="true" data-side-pagination="server"
                                data-pagination="true" data-page-list="[5, 10, 20, 50, 100, 200, All]"
                                data-toolbar="#toolbar" data-show-columns="true"
                                data-show-refresh="true" data-trim-on-search="false"
                                data-mobile-responsive="true" data-sort-name="id" data-sort-order="desc"
                                data-maintain-selected="true" data-export-data-type="basic"  data-show-export="true"
                                data-query-params="creditQueryParams" data-escape="true" data-escape-title="false">
                                <thead>
                                    <tr>
                                        <th scope="col" data-field="id" data-sortable="true" data-visible="false"><?php echo e(__('id')); ?></th>
                                        <th scope="col" data-field="no"><?php echo e(__('no.')); ?></th>
                                        <th scope="col" data-field="full_name"><?php echo e(__('Student')); ?></th>

                                        <th scope="col" data-field="updated_at" data-formatter="dateTimeFormatter" data-align="center"><?php echo e(__('last_update')); ?></th>
                                        <th scope="col" data-field="detail" data-align="center"><?php echo e(__('Details')); ?></th>
                                        <th scope="col" data-field="credit_amount" data-align="center"><?php echo e(__('Amount')); ?></th>
                                        <th scope="col" data-field="balance" data-align="center"><?php echo e(__('Post Balance')); ?></th>
                                        <th scope="col" data-events="rewardEvents" data-field="operate" data-escape="false"><?php echo e(__('action')); ?></th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('js'); ?>
<script>
    function successFunction(response){
        console.log(response);
        if(!response.error){
            setTimeout(function(){
                location.reload();
            }, 900);
        }
    }
    
    function creditQueryParams(p) {
        let tableListType = $('.table-list-type.active').data('value');
        return {
            limit: p.limit,
            sort: p.sort,
            order: p.order,
            offset: p.offset,
            search: p.search,
            class_id: $('#filter_class_id').val(),
            student_id: $('#filter_student_id').val(),
            start_date: $('#filter_start_date').val(),
            end_date: $('#filter_end_date').val(),
        };
    }

    function loadStudentByClass(obj) {
        let val = $(obj).val();
        $("#student-id").val("");
        $("#student-id").html("");

        if (val) {
            $.ajax({
                type: "GET",
                dataType: "JSON",
                url: "<?php echo e(route('credit.class-students', '')); ?>/" + val,
                success: function(result) {
                    if (result.length > 0) {

                        var optList = "";
                        for (var x in result) {
                            var item = result[x];
                            optList += `<option value="${item.id}">${item.fullname}</option>`;
                        }

                        $("#student-id").html(optList);
                        $("#student-id").select2("destroy")
                        $("#student-id").select2();
                    }
                }
            })
        }
    }

    function loadFilterStudentByClass(obj) {
        let val = $(obj).val();
        console.log(val);
        $("#filter_student_id").val("");
        $("#filter_student_id").html("");

        if (val) {
            $.ajax({
                type: "GET",
                dataType: "JSON",
                url: "<?php echo e(route('credit.class-students', '')); ?>/" + val,
                success: function(result) {
                    if (result.length > 0) {

                        var optList = `<option value="">View All Student</option>`;
                        for (var x in result) {
                            var item = result[x];
                            optList += `<option value="${item.id}">${item.fullname}</option>`;
                        }

                        $("#filter_student_id").html(optList);
                    }
                }
            })
        }
    }


    function loadRecordsByStudent(obj) {
        let val = $(obj).val();

        if (val) {
            $.ajax({
                type: "GET",
                dataType: "JSON",
                url: "<?php echo e(route('credit.class-students', '')); ?>/" + val,
                success: function(result) {
                    console.log(val);
                }
            })
        }
    }


    $("#filter_class_id").change();

    $('#class-id').change(function() {
        loadStudentByClass(this);
    });

    $("#class-id").change();

    $('#filter_class_id').change(function() {
        loadFilterStudentByClass(this);
    });

    $('#filter_student_id').change(function() {
        loadRecordsByStudent(this);
    });


    $('#filter_start_date,#filter_end_date').on('change', function() {
        $('#table_list').bootstrapTable('refresh');
    })

</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laragon\www\schola\resources\views/credit/manage_credit.blade.php ENDPATH**/ ?>