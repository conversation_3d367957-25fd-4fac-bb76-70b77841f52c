<?php
    $lang = Session::get('language');
?>
<link rel="stylesheet" href="<?php echo e(asset('/assets/css/materialdesignicons.min.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('/assets/css/vendor.bundle.base.css')); ?>">

<link rel="stylesheet" href="<?php echo e(asset('/assets/fonts/font-awesome.min.css')); ?>"/>
<link rel="stylesheet" href="<?php echo e(asset('/assets/select2/select2.min.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('/assets/jquery-toast-plugin/jquery.toast.min.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('/assets/color-picker/color.min.css')); ?>">
<?php if($lang): ?>
    <?php if($lang->is_rtl): ?>
        <link rel="stylesheet" href="<?php echo e(asset('/assets/css/rtl.min.css')); ?>">
        <link rel="stylesheet" href="<?php echo e(asset('/assets/css/custom-rtl.css')); ?>">
    <?php else: ?>
        <link rel="stylesheet" href="<?php echo e(asset('/assets/css/style.min.css')); ?>">
        <link rel="stylesheet" href="<?php echo e(asset('/assets/css/custom.css')); ?>">
    <?php endif; ?>
<?php else: ?>
    <link rel="stylesheet" href="<?php echo e(asset('/assets/css/style.min.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('/assets/css/custom.css')); ?>">
<?php endif; ?>

<link rel="stylesheet" href="<?php echo e(asset('/assets/css/comman.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('/assets/css/datepicker.min.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('/assets/css/ekko-lightbox.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('/assets/css/jquery.tagsinput.min.css')); ?>">




<link rel="stylesheet" href="https://unpkg.com/bootstrap-table@1.22.1/dist/bootstrap-table.min.css">
<link rel="stylesheet" href="<?php echo e(asset('/assets/bootstrap-table/fixed-columns.min.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('/assets/bootstrap-table/reorder-rows.css')); ?>">
<link rel= "stylesheet" href= "https://maxst.icons8.com/vue-static/landings/line-awesome/line-awesome/1.3.0/css/line-awesome.min.css" >
<link rel="stylesheet" 
href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">


<script src="<?php echo e(asset('/assets/js/vendor.bundle.base.js')); ?>"></script>
<script src='<?php echo e(asset('/assets/js/fullcalendar.js')); ?>'></script>


<link rel="shortcut icon" href="<?php echo e($schoolSettings['favicon'] ?? $systemSettings['favicon'] ?? url('assets/vertical-logo.svg')); ?>"/>




<script src="<?php echo e(url('/js/lang')); ?>"></script>
<style>
    :root {
        --theme-color: <?=$systemSettings['theme_color']??"#22577A" ?>;
    }
</style>
<script>
    const baseUrl = "<?php echo e(URL::to('/')); ?>";
    
    $(document).ready(function () {
        const $table = $('#table_list');
        if (!$table.length) return;

        const TABLE_COLUMNS_KEY = window.location.pathname
        .slice(1) // Remove leading "/"
        .replace(/\//g, '_') // Replace "/" with underscores
        .replace(/\/$/, '') // Remove trailing slash (if any)
        + '_table_columns';

        // Save column visibility on toggle
        $table.on('column-switch.bs.table', function(e, field, checked) {
            const visibleColumns = $table.bootstrapTable('getVisibleColumns')
                .map(col => col.field)
                .filter(f => f && f !== 'state');
            localStorage.setItem(TABLE_COLUMNS_KEY, JSON.stringify(visibleColumns));
        });

        // Restore columns on page load
        $(window).on('load', function() {
            const saved = localStorage.getItem(TABLE_COLUMNS_KEY);
            if (!saved) return;

            const visibleColumns = JSON.parse(saved);
            const allColumns = $table.bootstrapTable('getOptions').columns.flat();

            // Hide all non-state columns first
            allColumns.forEach(col => {
                if (col.field && col.field !== 'state') {
                    $table.bootstrapTable('hideColumn', col.field);
                }
            });

            // Show saved columns
            visibleColumns.forEach(field => {
                $table.bootstrapTable('showColumn', field);
            });
        });
    });
    
    // Function to handle image errors
    function handleImageError(image) {
        image.classList.contains('custom-default-image')
        if (image.getAttribute('data-custom-image') != null) {
            image.src = image.getAttribute('data-custom-image');
        } else {
            image.src = "<?php echo e(asset('/assets/no_image_available.jpg')); ?>";
        }
    }

    // Create a MutationObserver to watch for DOM changes
    const observer = new MutationObserver((mutationsList) => {
        mutationsList.forEach((mutation) => {
            if (mutation.addedNodes) {
                mutation.addedNodes.forEach((node) => {
                    // Check if the added node is an image element
                    if (node instanceof HTMLImageElement) {
                        node.addEventListener('error', () => {
                            handleImageError(node);
                        });
                    }
                });
            }
        });
    });

    // Start observing changes in the DOM
    observer.observe(document, {childList: true, subtree: true});

    const onErrorImage = (e) => {
        e.target.src = "<?php echo e(asset('/assets/no_image_available.jpg')); ?>";
    };

    const onErrorImageSidebarHorizontalLogo = (e) => {
        e.target.src = "<?php echo e(asset('/assets/vertical-logo.svg')); ?>";
    };
</script>



<?php /**PATH D:\laragon\www\schola\resources\views/layouts/include.blade.php ENDPATH**/ ?>