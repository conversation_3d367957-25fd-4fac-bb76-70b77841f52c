<?php

namespace App\Http\Controllers;

use App\Repositories\User\UserInterface;
use App\Services\BootstrapTableService;
use App\Services\ResponseService;
use App\Services\UploadService;
use App\Services\UserService;
use Hash;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Throwable;

class GuardianController extends Controller {
    protected UserInterface $user;
    protected UserService $userService;

    public function __construct(UserInterface $user,UserService $userService) {
        $this->user = $user;
        $this->userService = $userService;
    }

    public function index() {
        ResponseService::noPermissionThenRedirect('guardian-list');
        return view('guardian.index');
    }

    public function store(Request $request) {
        ResponseService::noPermissionThenRedirect('guardian-create');
        $request->validate([
            'first_name' => 'required',
            'email'      => 'required|unique:users,email',
            'last_name'  => 'required',
            'gender'     => 'required',
            'mobile'     => 'required',
        ]);
        try {
            DB::beginTransaction();
            $guardian = $this->user->create($request->all());
            $guardian->assignRole('Guardian');
            DB::commit();
            ResponseService::successResponse('Data Created Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Guardian Controller -> Store method");
            ResponseService::errorResponse();
        }
    }

    public function show() {
        ResponseService::noPermissionThenRedirect('guardian-list');
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'DESC');
    
        $sql = $this->user->guardian()->whereHas('child.user', function ($q) {
            $q->owner();
        });
    
        // Add search functionality
        if (!empty($_GET['search'])) {
            $search = $_GET['search'];
            $sql->where(function ($query) use ($search) {
                $query->where('id', 'LIKE', "%$search%")
                    ->orWhere('first_name', 'LIKE', "%$search%")
                    ->orWhere('last_name', 'LIKE', "%$search%")
                    ->orWhere('gender', 'LIKE', "%$search%")
                    ->orWhere('email', 'LIKE', "%$search%")
                    ->orWhere('mobile', 'LIKE', "%$search%");
    
                // Add search for student_name
                $query->orWhereHas('child', function ($childQuery) use ($search) {
                    $childQuery->whereHas('user', function ($userQuery) use ($search) {
                        $userQuery->where(DB::raw('CONCAT(first_name, " ", last_name)'), 'LIKE', "%$search%");
                    });
                });
            });
        }
    
        $total = $sql->count();
    
        $sql->orderBy($sort, $order)->skip($offset)->take($limit);
        $res = $sql->get();
        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $no = 1;
    
        foreach ($res as $row) {
            $studentArray = [];
            
            $operate = BootstrapTableService::editButton(route('guardian.update', $row->id));
            $operate .= BootstrapTableService::button('fa fa-envelope', route('guardian.email', $row->id), ['btn-gradient-warning'], ['title' => "Resend Welcome Email"]);
            $operate .= BootstrapTableService::button('fa fa-key', 'javascript:resetPassword('.$row->id.');', ['btn-gradient-danger'], ['title' => "Reset Password"]);
            
            $tempRow = $row->toArray();
            $tempRow['no'] = $no++;
            $tempRow['operate'] = $operate;
    
            $e_invoice_guardian = DB::table('e_invoice_guardian')
                ->select('name as name_per_icno', 'ic_no', 'tax_identification_number', 'sql_code', 'address', 'city', 'postal_code', 'country', 'state','contact_no', 'email as e_invoice_email')
                ->where('guardian_id', $row->id)
                // ->where('school_id', Auth::user()->school_id)
                ->first();
    
            if ($e_invoice_guardian) {
                $tempRow = array_merge($tempRow, (array) $e_invoice_guardian);  
            }
    
            $countryList = json_decode(file_get_contents('assets/JSON/eInvoice/CountryCodes.json'));
            $collection = collect($countryList);
            if (isset($tempRow['country'])) {
                $tempRow['country'] = $collection->first(function ($value) use ($tempRow) {
                    return $value->Code == $tempRow['country']; 
                });
            }
    
            $stateList = json_decode(file_get_contents('assets/JSON/eInvoice/StateCodes.json'));
            $collection = collect($stateList);
            if (isset($tempRow['state'])) {
                $tempRow['state'] = $collection->first(function ($value) use ($tempRow) {
                    return $value->Code == $tempRow['state']; 
                });
            }
    
            $parent_id = $tempRow['id'];
            $school_id = Auth::user()->school_id;
            $student_data = DB::table('students')
                ->select('user_id')
                ->where('guardian_id', '=', $parent_id)
                ->where('school_id', '=', $school_id)
                ->get();
    
            foreach ($student_data as $student) {
                $student_name = DB::table('users')
                    ->select(DB::raw('CONCAT(users.first_name, " ", users.last_name) as full_name'))
                    ->where('id', $student->user_id)
                    ->where('school_id', '=', $school_id)
                    ->first();
    
                $studentArray[] = $student_name->full_name;
                $tempRow['student_name'] = $studentArray;
            }
    
            $rows[] = $tempRow;
        }
    
        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    }
    
    
    public function resetPassword(Request $request, $id) {
        ResponseService::noPermissionThenSendJson('guardian-edit');
        try {
            $mobile = '';
            $guardian = DB::select('SELECT mobile FROM users WHERE id = ?', [$id]);
            if(COUNT($guardian)){
                $mobile = $guardian[0]->mobile;
            }

            $data['password'] = Hash::make($mobile);
            $this->user->guardian()->where('id', $id)->update($data);
            $jsonData['message'] = "Password Reset Successfully";
            return response()->json($jsonData);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "Guardian Controller -> Update method");
            ResponseService::errorResponse();
        }
    }

    public function update(Request $request) {
        ResponseService::noPermissionThenSendJson('guardian-edit');
        $request->validate([
            'edit_id'    => 'required',
            'first_name' => 'required',
            'email'      => 'required|unique:users,email,' . $request->edit_id,
            'last_name'  => 'required',
            'gender'     => 'required',
            'mobile'     => 'required',
            'image'      => 'nullable|mimes:png,jpg,jpeg|max:4096',
            'ic_no'      => 'nullable',
            'name_per_icno'             => 'nullable',
            'tax_identification_number' => 'nullable',
            'e_invoice_email'           => 'nullable',
            'sql_code'                  => 'nullable',
            'address'                   => 'nullable',
            'city'                      => 'nullable',
            'postal_code'               => 'nullable',
            'county'                    => 'nullable',
            'state'                     => 'nullable',
        ]);
        if ((isset($request->ic_no))){
            $regex = '/^[0-9]{6}[0-9]{2}[0-9]{4}$/';
            if (!preg_match($regex, $request->ic_no)) {
                ResponseService::errorResponse("Invalid IC Number format. Use format: XXXXXXYYZZZZ", null, null);
            }
        } elseif (isset($request->tax_identification_number)){
            $isTINError = false;
            if(strlen($request->tax_identification_number) >= 11 && strlen($request->tax_identification_number) <= 13){
            }
            else{
                $isTINError = true;
            }
            if($isTINError) {            
                ResponseService::errorResponse("Invalid TIN format.\nIndividual TIN: IGXXXXXXXXX\n11 to 13 characters", null, null);
            }
        }   
        try {

            $data = $request->except('_token', 'edit_id', '_method','reset_password','ic_no','tax_identification_number','sql_code','address','city','postal_code','country','state','name_per_icno', 'e_invoice_email', 'contact_no');
            $guardian = $this->user->guardian()->where('id', $request->edit_id)->firstOrFail();
            if (!empty($request->image)) {
                if ($guardian->image) {
                    if (!empty($guardian->getRawOriginal('image'))) {
                        UploadService::delete($guardian->getRawOriginal('image'));
                    }
                }
                $data['image'] = UploadService::upload($request->image, 'guardian');
            }
            if ($request->reset_password) {
                $data['password'] = Hash::make($request->mobile);
            }

           
            $e_invoice_data = array(
                'name'                      => $request->name_per_icno,
                'ic_no'                     => $request->ic_no,
                'tax_identification_number' => $request->tax_identification_number,
                'email'                     => $request->e_invoice_email,
                'sql_code'                  =>$request->sql_code,
                'address'                   => $request->address,
                'city'                      => $request->city,
                'postal_code'               => $request->postal_code,
                'country'                   => $request->country,
                'state'                     => $request->state,
                'contact_no'                => $request->contact_no,
                'guardian_id'               => $request->edit_id,
                'school_id'                 => Auth::user()->school_id
            );
    
             // Check if the record exists
            $existingRecord = DB::table("e_invoice_guardian")
                ->where('school_id', Auth::user()->school_id)
                ->where('guardian_id', $request->edit_id)
                ->first();
            

            if ($existingRecord) {
                // Update the existing record
                DB::table("e_invoice_guardian")
                    ->where('school_id', Auth::user()->school_id)
                    ->where('guardian_id', $request->edit_id)
                    ->update($e_invoice_data);
                 
            } else {
                // Insert a new record
                DB::table("e_invoice_guardian")->insert($e_invoice_data);
            }

            
            unset($data['contact_no']);
            $this->user->guardian()->where('id', $request->edit_id)->update($data);
            $guardian->assignRole('Guardian');
            ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "Guardian Controller -> Update method");
            ResponseService::errorResponse();
        }
    }

    public function search(Request $request) {
        ResponseService::noAnyPermissionThenSendJson(['student-create', 'student-edit']);
        $parent = $this->user->guardian()->where(function ($query) use ($request) {
            $query->where('email', 'like', '%' . $request->email . '%')
                ->orWhere('first_name', 'like', '%' . $request->email . '%')
                ->orWhere('last_name', 'like', '%' . $request->email . '%')
                ;
        })
        ->where('school_id', Auth::user()->school_id)
        ->get();

        if (!empty($parent)) {
            $response = [
                'error' => false,
                'data'  => $parent
            ];
        } else {
            $response = [
                'error'   => true,
                'message' => trans('no_data_found')
            ];
        }
        return response()->json($response);
    }

    public function sendGuardianEmail(Request $request, $id) {
        
        $guardian = DB::select('SELECT id,email, CONCAT(first_name, " ", last_name) AS full_name,mobile FROM users WHERE id = ?', [$id]);
        if(COUNT($guardian)){
            $guardian = $guardian[0];
            $newChild = DB::select('SELECT students.id,CONCAT(users.first_name," ",users.last_name) as full_name,users.dob,students.admission_no,users.email FROM students JOIN users ON users.id=students.user_id WHERE students.guardian_id = ? AND users.deleted_at IS NULL ORDER BY students.created_at DESC LIMIT 1',[$id]);
            if($newChild){
                $child = $newChild[0];
            }
            $childPassword = $this->userService->makeStudentPassword($child->dob);
        }
        
        $this->userService->sendRegistrationEmail($guardian, $child, $child->admission_no, $childPassword,'resend');
        // ResponseService::successResponse('Email Sent Successfully');
        return redirect()->back()->with('success', 'Email Sent Successfully');   
    }
}
