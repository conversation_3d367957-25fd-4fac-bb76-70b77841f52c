<?php echo $__env->yieldContent('js'); ?>
<!-- Button trigger modal -->
<!-- <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#TermsBackdrop">
    Launch admission backdrop modal
</button> -->
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
<script src="<?php echo e(asset('assets/js/custom/custom.js')); ?>"></script>
<div class="modal fade formModal" id="TermsBackdrop" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="TermsBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered  modal-xl">
        <div class="modal-content row">
            <div class="col-12 rightSide">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="TermsBackdropLabel"><?php echo e(__('Terms_and_Condition')); ?></h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form class="create-form" action="<?php echo e(url('admission')); ?>" enctype="multipart/form-data" method="post">
                        <?php echo csrf_field(); ?> 
                        <div class="TermsWrapper">
                            <div class="formWrapper">
                                <div class="row">
                                    <input type="hidden" id="schoolIdInput" name="school_id">
                                    <div class="inputWrapper">
                                        <div id="termsConditionField" class="form-control" list-style: inside;></div>
                                    </div>
                                    <div class="col-12 modalfooter">

                                        <div class="inputWrapper">
                                            
                                        </div>
                                        <div>
                                        <button type="button" onclick="$('#admissionBackdrop').modal('show');" class="commonBtn" data-bs-dismiss="modal"><?php echo e(__('accept')); ?></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php /**PATH D:\laragon\www\schola\resources\views/term_condition.blade.php ENDPATH**/ ?>