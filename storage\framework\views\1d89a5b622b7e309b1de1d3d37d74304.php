

<?php $__env->startSection('title'); ?>
<?php echo e(__('Credit')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="content-wrapper">
    <div class="page-header">
        <h3 class="page-title">
            <?php echo e(__('View Credit')); ?>

        </h3>
    </div>

    <div class="col-md-12 grid-margin stretch-card search-container">
        <div class="card">
            <div class="card-body">
                <h4 class="card-title">
                    <?php echo e(__('View Total Credit')); ?>

                </h4>
                <div class="row mt-4" id="toolbar">
                    <div class="col">
                        <label for="filter_class_id"><?php echo e(__("Student Class")); ?></label>
                        <select name="filter_class_id" id="filter_class_id" class="form-control select2-dropdown select2-hidden-accessible" onchange="loadFilterStudentByClass(this)">
                            <option value=""><?php echo e(('View All Class')); ?></option>
                            <?php $__currentLoopData = $classes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($item->id); ?>"><?php echo e($item->full_name); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>

                    <div class="col">
                        <label for="filter_student_id"><?php echo e(__('Student')); ?></label>
                        <select name="filter_student_id" id="filter_student_id" class="form-control select2-dropdown select2-hidden-accessible">
                        </select>
                    </div>

                </div>


                <div class="row">
                    <div class="col-12">
                        <table aria-describedby="mydesc" class='table reward_table' id='table_list'
                            data-toggle="table" data-url="<?php echo e(route('credit.view-credit-show', 1)); ?>"
                            data-click-to-select="true" data-side-pagination="server"
                            data-pagination="true" data-page-list="[5, 10, 20, 50, 100, 200, All]"
                            data-toolbar="#toolbar" data-show-columns="true"
                            data-show-refresh="true" data-trim-on-search="false"
                            data-mobile-responsive="true" data-sort-name="id" data-sort-order="desc"
                            data-maintain-selected="true" data-export-data-type="basic"  data-show-export="true"
                            data-query-params="viewCreditQueryParams" data-escape="true" data-escape-title="false">
                            <thead>
                                <tr>
                                    <th scope="col" data-field="id" data-sortable="true" data-visible="false"><?php echo e(__('id')); ?></th>
                                    <th scope="col" data-field="no"><?php echo e(__('no.')); ?></th>
                                    <th scope="col" data-field="full_name"><?php echo e(__('Student')); ?></th>
                                    <th scope="col" data-field="updated_at" data-formatter="dateTimeFormatter" data-align="center"><?php echo e(__('Last Update')); ?></th>
                                    <!-- <th scope="col" data-field="detail" data-align="center"><?php echo e(__('Details')); ?></th> -->
                                    <th scope="col" data-field="balance" data-align="center"><?php echo e(__('Balance')); ?></th>
                                    <th scope="col" data-field="operate" data-events="staffEvents" data-escape="false"><?php echo e(__('action')); ?></th>

                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<?php $__env->stopSection(); ?>
<?php $__env->startSection('js'); ?>
<script>

    function viewCreditQueryParams(p) {
        let tableListType = $('.table-list-type.active').data('value');
        return {
            limit: p.limit,
            sort: p.sort,
            order: p.order,
            offset: p.offset,
            search: p.search,
            class_id: $('#filter_class_id').val(),
            student_id: $('#filter_student_id').val(),
            show_deleted: (tableListType === "" || tableListType === "All" || tableListType == null) ? 0 : 1,

        };
        console.log('Query Parameters:', params);
        return params;
    }

    $(document).ready(function() {
        $('#table_list').on('load-success.bs.table', function (e, data) {
            console.log('Server Response:', data);
            console.log('Total Records:', data.total);
            console.log('Current Page Records:', data.rows ? data.rows.length : 0);
        });

        // Refresh table when student filter changes
        $('#filter_student_id, #filter_class_id').on('change', function() {
            $('#table_list').bootstrapTable('refresh');
        });
    });

    function loadStudentByClass(obj) {
        let val = $(obj).val();
        $("#student-id").val("");
        $("#student-id").html("");

        if (val) {
            $.ajax({
                type: "GET",
                dataType: "JSON",
                url: "<?php echo e(route('credit.class-students', '')); ?>/" + val,
                success: function(result) {
                    if (result.length > 0) {

                        var optList = "";
                        for (var x in result) {
                            var item = result[x];
                            optList += `<option value="${item.id}">${item.fullname}</option>`;
                        }

                        $("#student-id").html(optList);
                        $("#student-id").select2("destroy")
                        $("#student-id").select2();
                    }
                }
            })
        }
    }

    function loadFilterStudentByClass(obj) {
        let val = $(obj).val();
        $("#filter_student_id").val("");
        $("#filter_student_id").html("");

        if (val) {
            $.ajax({
                type: "GET",
                dataType: "JSON",
                url: "<?php echo e(route('credit.class-students','')); ?>/" + val,
                success: function(result) {
                    if (result.length > 0) {

                        var optList = `<option value="">View All Student</option>`;
                        for (var x in result) {
                            var item = result[x];
                            optList += `<option value="${item.id}">${item.fullname}</option>`;
                        }

                        $("#filter_student_id").html(optList);
                    }
                }
            })
        }
    }


    function loadRecordsByStudent(obj) {
        let val = $(obj).val();

        if (val) {
            $.ajax({
                type: "GET",
                dataType: "JSON",
                url: "<?php echo e(route('credit.view-credit', '')); ?>/" + val,
                success: function(result) {
                    console.log(val);
                }
            })
        }
    }


    $("#filter_class_id").change();

    $('#class-id').change(function() {
        loadStudentByClass(this);
    });

    $("#class-id").change();

    $('#filter_class_id').change(function() {
        loadFilterStudentByClass(this);
    });

    $('#filter_student_id').change(function() {
        loadRecordsByStudent(this);
    });

</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laragon\www\schola\resources\views/credit/view_credit.blade.php ENDPATH**/ ?>