{"__meta": {"id": "01JXBX4BB3G2TASSHKPJPEPPZV", "datetime": "2025-06-10 11:11:21", "utime": **********.443696, "method": "GET", "uri": "/payroll/1?sort=id&order=desc&search=&month=1&year=2025", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749525079.936586, "end": **********.44371, "duration": 1.5071241855621338, "duration_str": "1.51s", "measures": [{"label": "Booting", "start": 1749525079.936586, "relative_start": 0, "end": **********.20916, "relative_end": **********.20916, "duration": 0.*****************, "duration_str": "273ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.209183, "relative_start": 0.***************, "end": **********.443712, "relative_end": 1.9073486328125e-06, "duration": 1.****************, "duration_str": "1.23s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.231693, "relative_start": 0.****************, "end": **********.241704, "relative_end": **********.241704, "duration": 0.010010957717895508, "duration_str": "10.01ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.441074, "relative_start": 1.****************, "end": **********.441563, "relative_end": **********.441563, "duration": 0.0004889965057373047, "duration_str": "489μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "89MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.10", "Environment": "local", "Debug Mode": "Enabled", "URL": "schola.one", "Timezone": "Asia/Kuala_Lumpur", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 210, "nb_statements": 210, "nb_visible_statements": 210, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.1575599999999999, "accumulated_duration_str": "158ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 110 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `users` where `id` = 2 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.264718, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "schola", "explain": null, "start_percent": 0, "width_percent": 0.413}, {"sql": "select * from `schools` where `schools`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 13}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "Role", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckRole.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.271202, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "checkSchoolStatus:13", "source": {"index": 22, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FMiddleware%2FCheckSchoolStatus.php&line=13", "ajax": false, "filename": "CheckSchoolStatus.php", "line": "13"}, "connection": "schola", "explain": null, "start_percent": 0.413, "width_percent": 0.343}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (2) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 27}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "Role", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckRole.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.277511, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "schola", "explain": null, "start_percent": 0.755, "width_percent": 0.362}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 304}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 174}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 175}], "start": **********.283845, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:266", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=266", "ajax": false, "filename": "PermissionRegistrar.php", "line": "266"}, "connection": "schola", "explain": null, "start_percent": 1.117, "width_percent": 0.578}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 613, 1259, 1260, 1261, 1262, 1263, 1265, 1266, 1267, 1268, 1269, 1270, 1271, 1272, 1273)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 304}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 174}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 175}], "start": **********.292728, "duration": 0.05092, "duration_str": "50.92ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:266", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=266", "ajax": false, "filename": "PermissionRegistrar.php", "line": "266"}, "connection": "schola", "explain": null, "start_percent": 1.695, "width_percent": 32.318}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 2 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [2, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.002552, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "schola", "explain": null, "start_percent": 34.012, "width_percent": 0.406}, {"sql": "select * from `leave_masters` where `school_id` = 1 and exists (select * from `session_years` where `leave_masters`.`session_year_id` = `session_years`.`id` and ((month(`start_date`) <= '01' and year(`start_date`) = '2025') or (month(`start_date`) >= '01' and year(`end_date`) <= '2025')) and `session_years`.`deleted_at` is null) limit 1", "type": "query", "params": [], "bindings": [1, "01", "2025", "01", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 183}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.0074, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:183", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 183}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=183", "ajax": false, "filename": "PayrollController.php", "line": "183"}, "connection": "schola", "explain": null, "start_percent": 34.419, "width_percent": 0.628}, {"sql": "select count(*) as aggregate from `staffs` where exists (select * from `users` where `staffs`.`user_id` = `users`.`id` and `deleted_at` is null and `school_id` = 1)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 201}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.0097542, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:201", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 201}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=201", "ajax": false, "filename": "PayrollController.php", "line": "201"}, "connection": "schola", "explain": null, "start_percent": 35.047, "width_percent": 0.736}, {"sql": "select * from `staffs` where exists (select * from `users` where `staffs`.`user_id` = `users`.`id` and `deleted_at` is null and `school_id` = 1) order by `id` desc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.01184, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:204", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=204", "ajax": false, "filename": "PayrollController.php", "line": "204"}, "connection": "schola", "explain": null, "start_percent": 35.783, "width_percent": 0.736}, {"sql": "select * from `users` where `users`.`id` in (6, 49, 55, 56, 57, 58, 1325, 1495, 2036, 2063, 2649, 5255, 5355, 6725, 7172, 7258, 7506, 7613, 8577, 8688, 8690)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.014146, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:204", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=204", "ajax": false, "filename": "PayrollController.php", "line": "204"}, "connection": "schola", "explain": null, "start_percent": 36.519, "width_percent": 0.343}, {"sql": "select * from `expenses` where `expenses`.`staff_id` in (1, 4, 5, 6, 7, 8, 51, 67, 100, 104, 169, 298, 307, 362, 366, 367, 420, 426, 482, 485, 486)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.0174892, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:204", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=204", "ajax": false, "filename": "PayrollController.php", "line": "204"}, "connection": "schola", "explain": null, "start_percent": 36.862, "width_percent": 0.952}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = '2025' and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = '2025' and not `type` = 'Full') as `half_leave` from `leaves` where `leaves`.`user_id` in (6, 49, 55, 56, 57, 58, 1325, 1495, 2036, 2063, 2649, 5255, 5355, 6725, 7172, 7258, 7506, 7613, 8577, 8688, 8690) and `status` = 1", "type": "query", "params": [], "bindings": ["01", "2025", "Full", "01", "2025", "Full", 6, 49, 55, 56, 57, 58, 1325, 1495, 2036, 2063, 2649, 5255, 5355, 6725, 7172, 7258, 7506, 7613, 8577, 8688, 8690, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.048644, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:204", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=204", "ajax": false, "filename": "PayrollController.php", "line": "204"}, "connection": "schola", "explain": null, "start_percent": 37.814, "width_percent": 1.111}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= 3000 AND (max_range >= 3000 OR max_range IS NULL)", "type": "query", "params": [], "bindings": [3000, 3000], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.080724, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:229", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=229", "ajax": false, "filename": "PayrollController.php", "line": "229"}, "connection": "schola", "explain": null, "start_percent": 38.925, "width_percent": 0.381}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 486 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [486, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.0823019, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 39.306, "width_percent": 0.248}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= 2500 AND (max_range >= 2500 OR max_range IS NULL)", "type": "query", "params": [], "bindings": [2500, 2500], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.085538, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:229", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=229", "ajax": false, "filename": "PayrollController.php", "line": "229"}, "connection": "schola", "explain": null, "start_percent": 39.553, "width_percent": 0.222}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 485 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [485, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.086742, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 39.775, "width_percent": 0.228}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 482 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [482, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.088658, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 40.004, "width_percent": 0.178}, {"sql": "select * from `staffs` where `staffs`.`id` = 426 limit 1", "type": "query", "params": [], "bindings": [426], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.090596, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 40.182, "width_percent": 0.146}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '03' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '03' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 7613", "type": "query", "params": [], "bindings": ["03", 2025, "Full", "03", 2025, "Full", 1, 7613], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.0921052, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 40.327, "width_percent": 0.279}, {"sql": "select * from `staffs` where `staffs`.`id` = 426 limit 1", "type": "query", "params": [], "bindings": [426], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.0932891, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 40.607, "width_percent": 0.279}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 7613", "type": "query", "params": [], "bindings": ["01", 2025, "Full", "01", 2025, "Full", 1, 7613], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.094817, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 40.886, "width_percent": 0.267}, {"sql": "select * from `staffs` where `staffs`.`id` = 426 limit 1", "type": "query", "params": [], "bindings": [426], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.095983, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 41.153, "width_percent": 0.184}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 7613", "type": "query", "params": [], "bindings": ["04", 2025, "Full", "04", 2025, "Full", 1, 7613], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.097337, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 41.337, "width_percent": 0.26}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 426 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [426, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.098399, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 41.597, "width_percent": 0.228}, {"sql": "select * from `staffs` where `staffs`.`id` = 420 limit 1", "type": "query", "params": [], "bindings": [420], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.101401, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 41.825, "width_percent": 0.279}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 7506", "type": "query", "params": [], "bindings": ["04", 2025, "Full", "04", 2025, "Full", 1, 7506], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.103269, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 42.105, "width_percent": 0.413}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 420 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [420, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.104928, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 42.517, "width_percent": 0.349}, {"sql": "select * from `staffs` where `staffs`.`id` = 367 limit 1", "type": "query", "params": [], "bindings": [367], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.107307, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 42.866, "width_percent": 0.26}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '03' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '03' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 7258", "type": "query", "params": [], "bindings": ["03", 2025, "Full", "03", 2025, "Full", 1, 7258], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.109087, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 43.126, "width_percent": 0.419}, {"sql": "select * from `staffs` where `staffs`.`id` = 367 limit 1", "type": "query", "params": [], "bindings": [367], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.110976, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 43.545, "width_percent": 0.387}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 7258", "type": "query", "params": [], "bindings": ["04", 2025, "Full", "04", 2025, "Full", 1, 7258], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.113129, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 43.932, "width_percent": 0.387}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= 1200 AND (max_range >= 1200 OR max_range IS NULL)", "type": "query", "params": [], "bindings": [1200, 1200], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.114408, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:229", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=229", "ajax": false, "filename": "PayrollController.php", "line": "229"}, "connection": "schola", "explain": null, "start_percent": 44.32, "width_percent": 0.254}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 367 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [367, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1158612, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 44.573, "width_percent": 0.298}, {"sql": "select * from `staffs` where `staffs`.`id` = 366 limit 1", "type": "query", "params": [], "bindings": [366], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.118576, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 44.872, "width_percent": 0.419}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 7172", "type": "query", "params": [], "bindings": ["04", 2025, "Full", "04", 2025, "Full", 1, 7172], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.120615, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 45.291, "width_percent": 0.368}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 366 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [366, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.122127, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 45.659, "width_percent": 0.343}, {"sql": "select * from `staffs` where `staffs`.`id` = 362 limit 1", "type": "query", "params": [], "bindings": [362], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.124536, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 46.002, "width_percent": 0.292}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 6725", "type": "query", "params": [], "bindings": ["01", 2025, "Full", "01", 2025, "Full", 1, 6725], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.126328, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 46.293, "width_percent": 0.336}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 362 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [362, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1276278, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 46.63, "width_percent": 0.241}, {"sql": "select * from `staffs` where `staffs`.`id` = 307 limit 1", "type": "query", "params": [], "bindings": [307], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.129915, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 46.871, "width_percent": 0.273}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 5355", "type": "query", "params": [], "bindings": ["02", 2025, "Full", "02", 2025, "Full", 1, 5355], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.13153, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 47.144, "width_percent": 0.248}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 307 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [307, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.132791, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 47.391, "width_percent": 0.368}, {"sql": "select * from `staffs` where `staffs`.`id` = 298 limit 1", "type": "query", "params": [], "bindings": [298], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.135237, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 47.76, "width_percent": 0.305}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 5255", "type": "query", "params": [], "bindings": ["02", 2025, "Full", "02", 2025, "Full", 1, 5255], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.1369991, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 48.064, "width_percent": 0.317}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 298 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [298, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.138264, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 48.382, "width_percent": 0.254}, {"sql": "select * from `staffs` where `staffs`.`id` = 169 limit 1", "type": "query", "params": [], "bindings": [169], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.140759, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 48.635, "width_percent": 0.273}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 2649", "type": "query", "params": [], "bindings": ["02", 2025, "Full", "02", 2025, "Full", 1, 2649], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.1424, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 48.908, "width_percent": 0.317}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 169 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [169, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.143734, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 49.226, "width_percent": 0.216}, {"sql": "select * from `staffs` where `staffs`.`id` = 104 limit 1", "type": "query", "params": [], "bindings": [104], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.1459289, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 49.441, "width_percent": 0.368}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '10' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '10' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 2063", "type": "query", "params": [], "bindings": ["10", 2024, "Full", "10", 2024, "Full", 1, 2063], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.1478472, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 49.81, "width_percent": 0.305}, {"sql": "select * from `staffs` where `staffs`.`id` = 104 limit 1", "type": "query", "params": [], "bindings": [104], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.14921, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 50.114, "width_percent": 0.216}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 2063", "type": "query", "params": [], "bindings": ["02", 2025, "Full", "02", 2025, "Full", 1, 2063], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.1508589, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 50.33, "width_percent": 0.336}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 104 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [104, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.152186, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 50.666, "width_percent": 0.33}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 100 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [100, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.154613, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 50.996, "width_percent": 0.279}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 67 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [67, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.156728, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 51.276, "width_percent": 0.286}, {"sql": "select * from `staffs` where `staffs`.`id` = 51 limit 1", "type": "query", "params": [], "bindings": [51], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.158961, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 51.561, "width_percent": 0.203}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '08' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '08' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 1325", "type": "query", "params": [], "bindings": ["08", 2024, "Full", "08", 2024, "Full", 1, 1325], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.160701, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 51.764, "width_percent": 0.355}, {"sql": "select * from `staffs` where `staffs`.`id` = 51 limit 1", "type": "query", "params": [], "bindings": [51], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.1621451, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 52.12, "width_percent": 0.171}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 1325", "type": "query", "params": [], "bindings": ["02", 2025, "Full", "02", 2025, "Full", 1, 1325], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.16372, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 52.291, "width_percent": 0.336}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 51 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [51, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.165166, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 52.628, "width_percent": 0.26}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.167701, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 52.888, "width_percent": 0.267}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["01", 2024, "Full", "01", 2024, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.1694849, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 53.154, "width_percent": 0.273}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.1708949, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 53.427, "width_percent": 0.254}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2023 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2023 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["12", 2023, "Full", "12", 2023, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.172626, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 53.681, "width_percent": 0.292}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.174129, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 53.973, "width_percent": 0.286}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["12", 2024, "Full", "12", 2024, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.175867, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 54.259, "width_percent": 0.292}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.177253, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 54.551, "width_percent": 0.197}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '11' and year(`date`) = 2023 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '11' and year(`date`) = 2023 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["11", 2023, "Full", "11", 2023, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.178911, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 54.747, "width_percent": 0.324}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.1803482, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 55.071, "width_percent": 0.286}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '06' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '06' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["06", 2024, "Full", "06", 2024, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.182505, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 55.357, "width_percent": 0.349}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.184013, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 55.706, "width_percent": 0.254}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["02", 2024, "Full", "02", 2024, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.185788, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 55.96, "width_percent": 0.324}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.187272, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 56.283, "width_percent": 0.184}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '07' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '07' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["07", 2024, "Full", "07", 2024, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.189074, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 56.467, "width_percent": 0.432}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.190799, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 56.899, "width_percent": 0.254}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '08' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '08' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["08", 2024, "Full", "08", 2024, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.192541, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 57.153, "width_percent": 0.349}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.194025, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 57.502, "width_percent": 0.178}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '11' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '11' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["11", 2024, "Full", "11", 2024, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.1958961, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 57.68, "width_percent": 0.419}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.197554, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 58.099, "width_percent": 0.19}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["01", 2025, "Full", "01", 2025, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.1992059, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 58.289, "width_percent": 0.47}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.201271, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 58.759, "width_percent": 0.324}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["02", 2025, "Full", "02", 2025, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.203154, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 59.082, "width_percent": 0.317}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.204525, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 59.4, "width_percent": 0.165}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '03' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '03' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["03", 2025, "Full", "03", 2025, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.206085, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 59.565, "width_percent": 0.349}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= 2000 AND (max_range >= 2000 OR max_range IS NULL)", "type": "query", "params": [], "bindings": [2000, 2000], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2073631, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:229", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=229", "ajax": false, "filename": "PayrollController.php", "line": "229"}, "connection": "schola", "explain": null, "start_percent": 59.914, "width_percent": 0.286}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 8 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [8, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2090268, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 60.199, "width_percent": 0.381}, {"sql": "select * from `staffs` where `staffs`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.211709, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 60.58, "width_percent": 0.26}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 57", "type": "query", "params": [], "bindings": ["01", 2024, "Full", "01", 2024, "Full", 1, 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.213518, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 60.84, "width_percent": 0.349}, {"sql": "select * from `staffs` where `staffs`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.215274, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 61.189, "width_percent": 0.305}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2023 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2023 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 57", "type": "query", "params": [], "bindings": ["12", 2023, "Full", "12", 2023, "Full", 1, 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.217307, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 61.494, "width_percent": 0.444}, {"sql": "select * from `staffs` where `staffs`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.219085, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 61.938, "width_percent": 0.235}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 57", "type": "query", "params": [], "bindings": ["12", 2024, "Full", "12", 2024, "Full", 1, 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.220848, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 62.173, "width_percent": 0.444}, {"sql": "select * from `staffs` where `staffs`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.222826, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 62.617, "width_percent": 0.311}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '11' and year(`date`) = 2023 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '11' and year(`date`) = 2023 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 57", "type": "query", "params": [], "bindings": ["11", 2023, "Full", "11", 2023, "Full", 1, 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.22463, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 62.928, "width_percent": 0.279}, {"sql": "select * from `staffs` where `staffs`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.225972, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 63.208, "width_percent": 0.228}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '05' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '05' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 57", "type": "query", "params": [], "bindings": ["05", 2024, "Full", "05", 2024, "Full", 1, 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.228152, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 63.436, "width_percent": 0.552}, {"sql": "select * from `staffs` where `staffs`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.230486, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 63.988, "width_percent": 0.26}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '06' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '06' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 57", "type": "query", "params": [], "bindings": ["06", 2024, "Full", "06", 2024, "Full", 1, 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.2322361, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 64.249, "width_percent": 0.317}, {"sql": "select * from `staffs` where `staffs`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.233727, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 64.566, "width_percent": 0.241}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '07' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '07' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 57", "type": "query", "params": [], "bindings": ["07", 2024, "Full", "07", 2024, "Full", 1, 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.235353, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 64.807, "width_percent": 0.394}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.237011, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 65.201, "width_percent": 0.248}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2383578, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 65.448, "width_percent": 0.273}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.239268, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 65.721, "width_percent": 0.171}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.240519, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 65.892, "width_percent": 0.292}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2414339, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 66.184, "width_percent": 0.159}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.242669, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 66.343, "width_percent": 0.311}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.243703, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 66.654, "width_percent": 0.273}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.245619, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 66.927, "width_percent": 0.336}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= ? AND (max_range >= ? OR max_range IS NULL)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2464151, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 67.263, "width_percent": 0.184}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = ? and `expenses`.`staff_id` is not null and `month` = ? and `year` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.247003, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 67.447, "width_percent": 0.203}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2486448, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 67.65, "width_percent": 0.165}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2500389, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 67.815, "width_percent": 0.597}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2517562, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 68.412, "width_percent": 0.171}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.253032, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 68.583, "width_percent": 0.482}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2545571, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 69.066, "width_percent": 0.178}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.255785, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 69.243, "width_percent": 0.4}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2571218, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 69.643, "width_percent": 0.165}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.25856, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 69.808, "width_percent": 0.565}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2602248, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 70.373, "width_percent": 0.254}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.261692, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 70.627, "width_percent": 0.47}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2631278, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 71.097, "width_percent": 0.159}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.264525, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 71.255, "width_percent": 0.552}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.266112, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 71.808, "width_percent": 0.178}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.267372, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 71.985, "width_percent": 0.451}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.268831, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 72.436, "width_percent": 0.184}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.270169, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 72.62, "width_percent": 0.444}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2717628, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 73.064, "width_percent": 0.26}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.273209, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 73.324, "width_percent": 0.425}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2745862, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 73.75, "width_percent": 0.171}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2758632, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 73.921, "width_percent": 0.413}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.277161, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 74.334, "width_percent": 0.146}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.278458, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 74.48, "width_percent": 0.647}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= ? AND (max_range >= ? OR max_range IS NULL)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2818398, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 75.127, "width_percent": 0.248}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = ? and `expenses`.`staff_id` is not null and `month` = ? and `year` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.282532, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 75.374, "width_percent": 0.184}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2840462, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 75.559, "width_percent": 0.197}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2854981, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 75.755, "width_percent": 0.406}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2866578, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 76.161, "width_percent": 0.152}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.287829, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 76.314, "width_percent": 0.216}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.288687, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 76.53, "width_percent": 0.19}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.289965, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 76.72, "width_percent": 0.248}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.290787, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 76.968, "width_percent": 0.146}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.292001, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 77.113, "width_percent": 0.463}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.293298, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 77.577, "width_percent": 0.19}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.294581, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 77.767, "width_percent": 0.248}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2954059, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 78.015, "width_percent": 0.165}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2966468, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 78.18, "width_percent": 0.248}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2974808, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 78.427, "width_percent": 0.146}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.298652, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 78.573, "width_percent": 0.336}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2998092, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 78.91, "width_percent": 0.279}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3013, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 79.189, "width_percent": 0.279}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3022292, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 79.468, "width_percent": 0.279}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3037121, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 79.747, "width_percent": 0.311}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= ? AND (max_range >= ? OR max_range IS NULL)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.304499, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 80.058, "width_percent": 0.235}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = ? and `expenses`.`staff_id` is not null and `month` = ? and `year` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.305358, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 80.293, "width_percent": 0.432}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.308323, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 80.725, "width_percent": 0.33}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.310374, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 81.055, "width_percent": 0.52}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.311939, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 81.575, "width_percent": 0.311}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3136718, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 81.886, "width_percent": 0.387}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.314813, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 82.273, "width_percent": 0.152}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.316007, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 82.426, "width_percent": 0.343}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.317106, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 82.768, "width_percent": 0.216}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3184, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 82.984, "width_percent": 0.273}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3193011, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 83.257, "width_percent": 0.197}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.320766, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 83.454, "width_percent": 0.406}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.32194, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 83.86, "width_percent": 0.197}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.323234, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 84.057, "width_percent": 0.406}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3244169, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 84.463, "width_percent": 0.165}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.325634, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 84.628, "width_percent": 0.267}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.326503, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 84.895, "width_percent": 0.14}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3278549, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 85.034, "width_percent": 0.457}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3291562, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 85.491, "width_percent": 0.178}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.330488, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 85.669, "width_percent": 0.311}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= ? AND (max_range >= ? OR max_range IS NULL)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3314748, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 85.98, "width_percent": 0.165}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = ? and `expenses`.`staff_id` is not null and `month` = ? and `year` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.33199, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 86.145, "width_percent": 0.184}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.333633, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 86.329, "width_percent": 0.343}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.335392, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 86.672, "width_percent": 0.641}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.337378, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 87.313, "width_percent": 0.362}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.339075, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 87.675, "width_percent": 0.52}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3405821, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 88.195, "width_percent": 0.286}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3422031, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 88.481, "width_percent": 0.539}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.343701, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 89.02, "width_percent": 0.165}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.344944, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 89.185, "width_percent": 0.527}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.346427, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 89.712, "width_percent": 0.152}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3476021, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 89.864, "width_percent": 0.616}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.349406, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 90.48, "width_percent": 0.349}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.35117, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 90.829, "width_percent": 0.787}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.353161, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 91.616, "width_percent": 0.209}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3544521, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 91.825, "width_percent": 0.432}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.355743, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 92.257, "width_percent": 0.165}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.356893, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 92.422, "width_percent": 0.425}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.358398, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 92.847, "width_percent": 0.336}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.359997, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 93.184, "width_percent": 0.52}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.361496, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 93.704, "width_percent": 0.152}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.362675, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 93.856, "width_percent": 0.482}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3640409, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 94.339, "width_percent": 0.152}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3655581, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 94.491, "width_percent": 0.749}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.367562, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 95.24, "width_percent": 0.222}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.368893, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 95.462, "width_percent": 0.489}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.370271, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 95.951, "width_percent": 0.152}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3714478, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 96.103, "width_percent": 0.597}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.373268, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 96.7, "width_percent": 0.248}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3746219, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 96.947, "width_percent": 0.457}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.375948, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 97.404, "width_percent": 0.146}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.377055, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 97.55, "width_percent": 0.425}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.378323, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 97.975, "width_percent": 0.184}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3799329, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 98.159, "width_percent": 0.584}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.381522, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 98.743, "width_percent": 0.152}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.382758, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 98.896, "width_percent": 0.489}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= ? AND (max_range >= ? OR max_range IS NULL)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.385327, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 99.384, "width_percent": 0.197}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = ? and `expenses`.`staff_id` is not null and `month` = ? and `year` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.386032, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 99.581, "width_percent": 0.419}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 13675, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Leave": {"value": 364, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FLeave.php&line=1", "ajax": false, "filename": "Leave.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 227, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Staff": {"value": 105, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FStaff.php&line=1", "ajax": false, "filename": "Staff.php", "line": "?"}}, "App\\Models\\Expense": {"value": 87, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=1", "ajax": false, "filename": "Expense.php", "line": "?"}}, "App\\Models\\User": {"value": 22, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\School": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FSchool.php&line=1", "ajax": false, "filename": "School.php", "line": "?"}}, "App\\Models\\LeaveMaster": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FLeaveMaster.php&line=1", "ajax": false, "filename": "LeaveMaster.php", "line": "?"}}}, "count": 14482, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => payroll-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1656057752 data-indent-pad=\"  \"><span class=sf-dump-note>payroll-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">payroll-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1656057752\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.006262, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "https://schola.test/payroll/1?month=1&order=desc&search=&sort=id&year=2025", "action_name": "payroll.show", "controller_action": "App\\Http\\Controllers\\PayrollController@show", "uri": "GET payroll/{payroll}", "controller": "App\\Http\\Controllers\\PayrollController@show<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=165\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=165\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/PayrollController.php:165-313</a>", "middleware": "web, Role, auth, checkSchoolStatus, status, language", "duration": "1.52s", "peak_memory": "94MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2109509644 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sort</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n  \"<span class=sf-dump-key>order</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>month</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>year</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2025</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2109509644\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1711476082 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1711476082\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1559387472 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">schola.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">k0nsyLuN5aAz6u8lgTG1r68KyFeDp1NRQbXnBoOH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">https://schola.test/payroll</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"742 characters\">XSRF-TOKEN=eyJpdiI6IlJDSk1LWG9uQXRhbzEvM3NFOWtwRXc9PSIsInZhbHVlIjoiNzF5eDNpV1BST1RVSzM5UHUrNlRlYVZ3ZmZMREh3ZFgwWW4vR0dwZnd4bjlyTURXaVZnNEo1OGFZTnc4Tm45R1RQVE9GaDBPVlBsQ1JWYmN6NVZJVVJWMHRNVTM4R0tFY2NrOFN3WWkxcjg3MCtiNEtlditvcEVXOWQwUHhqVisiLCJtYWMiOiIzOTA0YjQxYzk2Njk1YzcyNzU0ZWE3ZGRlY2I2OWY4YjBmNjAxMjFmZmY3YjkxNzVmNDNjY2E5ZGU0OTk1NmQ5IiwidGFnIjoiIn0%3D; schola_saas_school_management_system_session=eyJpdiI6InM2NS9iNmh2MkgrQkhJK1ZaSTRMb3c9PSIsInZhbHVlIjoicTZkbnNNQmZDMmVpby9YeHN1MXI0V0w2THAreThObTB0WUxwc0oyYTE4bTl6RGtPQWZNRzNMK2FPb2FaYVA2Q00vRE11dWtLUVVVbTNLNm1pYWxYWW1kNE93aVBkUnRRd0R1NnZZT2lDbTI2ZnBTYlRKdGFyZFB6WnF5MWdTWnkiLCJtYWMiOiI3NzEwZjk3MzYzMDMxYjk5MmEwNzI1Njk3OWYzYTcxNjljMWFjNjk2NDlmYTA0Y2I1MTEwNmY2ODM5MmNlNDJiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1559387472\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1097647230 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0nsyLuN5aAz6u8lgTG1r68KyFeDp1NRQbXnBoOH</span>\"\n  \"<span class=sf-dump-key>schola_saas_school_management_system_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IQrLeRaEfTugtoRzBcuJ4wOlCeh4PG6VwaBH418U</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1097647230\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-290081434 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 10 Jun 2025 03:11:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-290081434\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1733859143 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0nsyLuN5aAz6u8lgTG1r68KyFeDp1NRQbXnBoOH</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">https://schola.test/payroll</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>landing_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>language</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Language\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Language</span></span> {<a class=sf-dump-ref>#3547</a><samp data-depth=2 class=sf-dump-compact>\n    #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">languages</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n    +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n    #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n    +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n    +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n    +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">English</span>\"\n      \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"7 characters\">en.json</span>\"\n      \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>is_rtl</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">English</span>\"\n      \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"7 characters\">en.json</span>\"\n      \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>is_rtl</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n    +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n    +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">code</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">is_rtl</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n    </samp>]\n  </samp>}\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1749518683</span>\n  </samp>]\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1733859143\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://schola.test/payroll/1?month=1&order=desc&search=&sort=id&year=2025", "action_name": "payroll.show", "controller_action": "App\\Http\\Controllers\\PayrollController@show"}, "badge": null}}