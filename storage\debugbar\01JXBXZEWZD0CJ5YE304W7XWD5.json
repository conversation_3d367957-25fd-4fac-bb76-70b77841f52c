{"__meta": {"id": "01JXBXZEWZD0CJ5YE304W7XWD5", "datetime": "2025-06-10 11:26:09", "utime": **********.824466, "method": "GET", "uri": "/payroll/1?sort=id&order=desc&search=&month=1&year=2025", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.226935, "end": **********.82448, "duration": 1.5975451469421387, "duration_str": "1.6s", "measures": [{"label": "Booting", "start": **********.226935, "relative_start": 0, "end": **********.52581, "relative_end": **********.52581, "duration": 0.***************, "duration_str": "299ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.525822, "relative_start": 0.****************, "end": **********.824481, "relative_end": 9.5367431640625e-07, "duration": 1.***************, "duration_str": "1.3s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.544573, "relative_start": 0.****************, "end": **********.554405, "relative_end": **********.554405, "duration": 0.009831905364990234, "duration_str": "9.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.821953, "relative_start": 1.****************, "end": **********.82242, "relative_end": **********.82242, "duration": 0.0004668235778808594, "duration_str": "467μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "89MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.10", "Environment": "local", "Debug Mode": "Enabled", "URL": "schola.one", "Timezone": "Asia/Kuala_Lumpur", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 210, "nb_statements": 210, "nb_visible_statements": 210, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.1624200000000001, "accumulated_duration_str": "162ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 110 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `users` where `id` = 2 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.581972, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "schola", "explain": null, "start_percent": 0, "width_percent": 0.326}, {"sql": "select * from `schools` where `schools`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 13}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "Role", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckRole.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5902772, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "checkSchoolStatus:13", "source": {"index": 22, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FMiddleware%2FCheckSchoolStatus.php&line=13", "ajax": false, "filename": "CheckSchoolStatus.php", "line": "13"}, "connection": "schola", "explain": null, "start_percent": 0.326, "width_percent": 0.277}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (2) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 27}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "Role", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckRole.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5972888, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "schola", "explain": null, "start_percent": 0.603, "width_percent": 0.351}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 304}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 174}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 175}], "start": **********.60544, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:266", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=266", "ajax": false, "filename": "PermissionRegistrar.php", "line": "266"}, "connection": "schola", "explain": null, "start_percent": 0.954, "width_percent": 0.585}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 613, 1259, 1260, 1261, 1262, 1263, 1265, 1266, 1267, 1268, 1269, 1270, 1271, 1272, 1273)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 304}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 174}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 175}], "start": **********.61532, "duration": 0.046939999999999996, "duration_str": "46.94ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:266", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=266", "ajax": false, "filename": "PermissionRegistrar.php", "line": "266"}, "connection": "schola", "explain": null, "start_percent": 1.539, "width_percent": 28.9}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 2 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [2, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.413758, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "schola", "explain": null, "start_percent": 30.44, "width_percent": 0.529}, {"sql": "select * from `leave_masters` where `school_id` = 1 and exists (select * from `session_years` where `leave_masters`.`session_year_id` = `session_years`.`id` and ((month(`start_date`) <= '01' and year(`start_date`) = '2025') or (month(`start_date`) >= '01' and year(`end_date`) <= '2025')) and `session_years`.`deleted_at` is null) limit 1", "type": "query", "params": [], "bindings": [1, "01", "2025", "01", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 183}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4209409, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:183", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 183}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=183", "ajax": false, "filename": "PayrollController.php", "line": "183"}, "connection": "schola", "explain": null, "start_percent": 30.969, "width_percent": 0.671}, {"sql": "select count(*) as aggregate from `staffs` where exists (select * from `users` where `staffs`.`user_id` = `users`.`id` and `deleted_at` is null and `school_id` = 1)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 201}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.424576, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:201", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 201}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=201", "ajax": false, "filename": "PayrollController.php", "line": "201"}, "connection": "schola", "explain": null, "start_percent": 31.64, "width_percent": 0.77}, {"sql": "select * from `staffs` where exists (select * from `users` where `staffs`.`user_id` = `users`.`id` and `deleted_at` is null and `school_id` = 1) order by `id` desc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4269218, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:204", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=204", "ajax": false, "filename": "PayrollController.php", "line": "204"}, "connection": "schola", "explain": null, "start_percent": 32.41, "width_percent": 0.899}, {"sql": "select * from `users` where `users`.`id` in (6, 49, 55, 56, 57, 58, 1325, 1495, 2036, 2063, 2649, 5255, 5355, 6725, 7172, 7258, 7506, 7613, 8577, 8688, 8690)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.431052, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:204", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=204", "ajax": false, "filename": "PayrollController.php", "line": "204"}, "connection": "schola", "explain": null, "start_percent": 33.309, "width_percent": 0.831}, {"sql": "select * from `expenses` where `expenses`.`staff_id` in (1, 4, 5, 6, 7, 8, 51, 67, 100, 104, 169, 298, 307, 362, 366, 367, 420, 426, 482, 485, 486)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.439476, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:204", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=204", "ajax": false, "filename": "PayrollController.php", "line": "204"}, "connection": "schola", "explain": null, "start_percent": 34.14, "width_percent": 1.151}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = '2025' and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = '2025' and not `type` = 'Full') as `half_leave` from `leaves` where `leaves`.`user_id` in (6, 49, 55, 56, 57, 58, 1325, 1495, 2036, 2063, 2649, 5255, 5355, 6725, 7172, 7258, 7506, 7613, 8577, 8688, 8690) and `status` = 1", "type": "query", "params": [], "bindings": ["01", "2025", "Full", "01", "2025", "Full", 6, 49, 55, 56, 57, 58, 1325, 1495, 2036, 2063, 2649, 5255, 5355, 6725, 7172, 7258, 7506, 7613, 8577, 8688, 8690, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.446836, "duration": 0.00316, "duration_str": "3.16ms", "memory": 0, "memory_str": null, "filename": "PayrollController.php:204", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 204}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=204", "ajax": false, "filename": "PayrollController.php", "line": "204"}, "connection": "schola", "explain": null, "start_percent": 35.291, "width_percent": 1.946}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= 3000 AND (max_range >= 3000 OR max_range IS NULL)", "type": "query", "params": [], "bindings": [3000, 3000], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.469377, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:229", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=229", "ajax": false, "filename": "PayrollController.php", "line": "229"}, "connection": "schola", "explain": null, "start_percent": 37.237, "width_percent": 0.351}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 486 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [486, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4717429, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 37.588, "width_percent": 0.443}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= 2500 AND (max_range >= 2500 OR max_range IS NULL)", "type": "query", "params": [], "bindings": [2500, 2500], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.476393, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:229", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=229", "ajax": false, "filename": "PayrollController.php", "line": "229"}, "connection": "schola", "explain": null, "start_percent": 38.031, "width_percent": 0.425}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 485 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [485, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.479079, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 38.456, "width_percent": 0.462}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 482 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [482, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.482034, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 38.918, "width_percent": 0.332}, {"sql": "select * from `staffs` where `staffs`.`id` = 426 limit 1", "type": "query", "params": [], "bindings": [426], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.485646, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 39.25, "width_percent": 0.443}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '03' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '03' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 7613", "type": "query", "params": [], "bindings": ["03", 2025, "Full", "03", 2025, "Full", 1, 7613], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.488343, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 39.693, "width_percent": 0.406}, {"sql": "select * from `staffs` where `staffs`.`id` = 426 limit 1", "type": "query", "params": [], "bindings": [426], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.490284, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 40.1, "width_percent": 0.265}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 7613", "type": "query", "params": [], "bindings": ["01", 2025, "Full", "01", 2025, "Full", 1, 7613], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.493307, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 40.364, "width_percent": 0.517}, {"sql": "select * from `staffs` where `staffs`.`id` = 426 limit 1", "type": "query", "params": [], "bindings": [426], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.495332, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 40.882, "width_percent": 0.271}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 7613", "type": "query", "params": [], "bindings": ["04", 2025, "Full", "04", 2025, "Full", 1, 7613], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.497314, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 41.153, "width_percent": 0.431}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 426 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [426, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4996028, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 41.584, "width_percent": 0.591}, {"sql": "select * from `staffs` where `staffs`.`id` = 420 limit 1", "type": "query", "params": [], "bindings": [420], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.504315, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 42.175, "width_percent": 0.32}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 7506", "type": "query", "params": [], "bindings": ["04", 2025, "Full", "04", 2025, "Full", 1, 7506], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.507263, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 42.495, "width_percent": 0.548}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 420 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [420, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.509208, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 43.043, "width_percent": 0.376}, {"sql": "select * from `staffs` where `staffs`.`id` = 367 limit 1", "type": "query", "params": [], "bindings": [367], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.512295, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 43.418, "width_percent": 0.369}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '03' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '03' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 7258", "type": "query", "params": [], "bindings": ["03", 2025, "Full", "03", 2025, "Full", 1, 7258], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.514679, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 43.788, "width_percent": 0.437}, {"sql": "select * from `staffs` where `staffs`.`id` = 367 limit 1", "type": "query", "params": [], "bindings": [367], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.5164661, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 44.225, "width_percent": 0.277}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 7258", "type": "query", "params": [], "bindings": ["04", 2025, "Full", "04", 2025, "Full", 1, 7258], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.518504, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 44.502, "width_percent": 0.573}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= 1200 AND (max_range >= 1200 OR max_range IS NULL)", "type": "query", "params": [], "bindings": [1200, 1200], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.520551, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:229", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=229", "ajax": false, "filename": "PayrollController.php", "line": "229"}, "connection": "schola", "explain": null, "start_percent": 45.074, "width_percent": 0.332}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 367 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [367, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.522356, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 45.407, "width_percent": 0.271}, {"sql": "select * from `staffs` where `staffs`.`id` = 366 limit 1", "type": "query", "params": [], "bindings": [366], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.524786, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 45.678, "width_percent": 0.209}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '04' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 7172", "type": "query", "params": [], "bindings": ["04", 2025, "Full", "04", 2025, "Full", 1, 7172], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.526971, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 45.887, "width_percent": 0.536}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 366 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [366, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.528898, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 46.423, "width_percent": 0.308}, {"sql": "select * from `staffs` where `staffs`.`id` = 362 limit 1", "type": "query", "params": [], "bindings": [362], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.5315168, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 46.731, "width_percent": 0.265}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 6725", "type": "query", "params": [], "bindings": ["01", 2025, "Full", "01", 2025, "Full", 1, 6725], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.533688, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 46.995, "width_percent": 0.597}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 362 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [362, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.535747, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 47.593, "width_percent": 0.332}, {"sql": "select * from `staffs` where `staffs`.`id` = 307 limit 1", "type": "query", "params": [], "bindings": [307], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.538491, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 47.925, "width_percent": 0.228}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 5355", "type": "query", "params": [], "bindings": ["02", 2025, "Full", "02", 2025, "Full", 1, 5355], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.541617, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 48.153, "width_percent": 0.696}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 307 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [307, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.54421, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 48.849, "width_percent": 0.314}, {"sql": "select * from `staffs` where `staffs`.`id` = 298 limit 1", "type": "query", "params": [], "bindings": [298], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.547454, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 49.163, "width_percent": 0.388}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 5255", "type": "query", "params": [], "bindings": ["02", 2025, "Full", "02", 2025, "Full", 1, 5255], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.549834, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 49.551, "width_percent": 0.419}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 298 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [298, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.5515132, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 49.969, "width_percent": 0.277}, {"sql": "select * from `staffs` where `staffs`.`id` = 169 limit 1", "type": "query", "params": [], "bindings": [169], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.554726, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 50.246, "width_percent": 0.462}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 2649", "type": "query", "params": [], "bindings": ["02", 2025, "Full", "02", 2025, "Full", 1, 2649], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.5571399, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 50.708, "width_percent": 0.376}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 169 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [169, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.5587249, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 51.084, "width_percent": 0.265}, {"sql": "select * from `staffs` where `staffs`.`id` = 104 limit 1", "type": "query", "params": [], "bindings": [104], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.56224, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 51.348, "width_percent": 0.363}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '10' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '10' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 2063", "type": "query", "params": [], "bindings": ["10", 2024, "Full", "10", 2024, "Full", 1, 2063], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.564475, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 51.712, "width_percent": 0.326}, {"sql": "select * from `staffs` where `staffs`.`id` = 104 limit 1", "type": "query", "params": [], "bindings": [104], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.566028, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 52.038, "width_percent": 0.172}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 2063", "type": "query", "params": [], "bindings": ["02", 2025, "Full", "02", 2025, "Full", 1, 2063], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.568213, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 52.21, "width_percent": 0.511}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 104 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [104, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.57007, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 52.721, "width_percent": 0.314}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 100 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [100, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.572653, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 53.035, "width_percent": 0.24}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 67 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [67, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.57592, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 53.275, "width_percent": 0.326}, {"sql": "select * from `staffs` where `staffs`.`id` = 51 limit 1", "type": "query", "params": [], "bindings": [51], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.578739, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 53.602, "width_percent": 0.203}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '08' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '08' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 1325", "type": "query", "params": [], "bindings": ["08", 2024, "Full", "08", 2024, "Full", 1, 1325], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.580474, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 53.805, "width_percent": 0.32}, {"sql": "select * from `staffs` where `staffs`.`id` = 51 limit 1", "type": "query", "params": [], "bindings": [51], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.582511, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 54.125, "width_percent": 0.357}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 1325", "type": "query", "params": [], "bindings": ["02", 2025, "Full", "02", 2025, "Full", 1, 1325], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.584753, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 54.482, "width_percent": 0.32}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 51 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [51, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.586357, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 54.802, "width_percent": 0.209}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.589609, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 55.012, "width_percent": 0.345}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["01", 2024, "Full", "01", 2024, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.591728, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 55.356, "width_percent": 0.326}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.5932271, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 55.683, "width_percent": 0.166}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2023 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2023 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["12", 2023, "Full", "12", 2023, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.594894, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 55.849, "width_percent": 0.351}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.596927, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 56.2, "width_percent": 0.259}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["12", 2024, "Full", "12", 2024, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.5987601, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 56.459, "width_percent": 0.277}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.600128, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 56.736, "width_percent": 0.16}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '11' and year(`date`) = 2023 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '11' and year(`date`) = 2023 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["11", 2023, "Full", "11", 2023, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.60182, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 56.896, "width_percent": 0.486}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.604096, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 57.382, "width_percent": 0.32}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '06' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '06' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["06", 2024, "Full", "06", 2024, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.6060932, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 57.702, "width_percent": 0.283}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.607587, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 57.985, "width_percent": 0.172}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["02", 2024, "Full", "02", 2024, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.609417, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 58.158, "width_percent": 0.48}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.611523, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 58.638, "width_percent": 0.234}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '07' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '07' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["07", 2024, "Full", "07", 2024, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.613311, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 58.872, "width_percent": 0.277}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.6147041, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 59.149, "width_percent": 0.166}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '08' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '08' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["08", 2024, "Full", "08", 2024, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.616675, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 59.315, "width_percent": 0.48}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.618932, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 59.796, "width_percent": 0.259}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '11' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '11' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["11", 2024, "Full", "11", 2024, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.620779, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 60.054, "width_percent": 0.277}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.6222231, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 60.331, "width_percent": 0.283}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["01", 2025, "Full", "01", 2025, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.6249511, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 60.614, "width_percent": 0.529}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.626928, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 61.144, "width_percent": 0.197}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '02' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["02", 2025, "Full", "02", 2025, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.6286108, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 61.341, "width_percent": 0.252}, {"sql": "select * from `staffs` where `staffs`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.630171, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 61.593, "width_percent": 0.332}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '03' and year(`date`) = 2025 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '03' and year(`date`) = 2025 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 58", "type": "query", "params": [], "bindings": ["03", 2025, "Full", "03", 2025, "Full", 1, 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.632515, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 61.926, "width_percent": 0.345}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= 2000 AND (max_range >= 2000 OR max_range IS NULL)", "type": "query", "params": [], "bindings": [2000, 2000], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.633818, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:229", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 229}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=229", "ajax": false, "filename": "PayrollController.php", "line": "229"}, "connection": "schola", "explain": null, "start_percent": 62.271, "width_percent": 0.234}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = 8 and `expenses`.`staff_id` is not null and `month` = '1' and `year` = '2025' limit 1", "type": "query", "params": [], "bindings": [8, "1", "2025"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.6353571, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "PayrollController.php:257", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=257", "ajax": false, "filename": "PayrollController.php", "line": "257"}, "connection": "schola", "explain": null, "start_percent": 62.505, "width_percent": 0.283}, {"sql": "select * from `staffs` where `staffs`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.6386662, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 62.788, "width_percent": 0.271}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '01' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 57", "type": "query", "params": [], "bindings": ["01", 2024, "Full", "01", 2024, "Full", 1, 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.640548, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 63.059, "width_percent": 0.289}, {"sql": "select * from `staffs` where `staffs`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.641945, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 63.348, "width_percent": 0.16}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2023 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2023 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 57", "type": "query", "params": [], "bindings": ["12", 2023, "Full", "12", 2023, "Full", 1, 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.643606, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 63.508, "width_percent": 0.406}, {"sql": "select * from `staffs` where `staffs`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.6455429, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 63.915, "width_percent": 0.265}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '12' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 57", "type": "query", "params": [], "bindings": ["12", 2024, "Full", "12", 2024, "Full", 1, 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.6474042, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 64.179, "width_percent": 0.271}, {"sql": "select * from `staffs` where `staffs`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.6487648, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 64.45, "width_percent": 0.16}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '11' and year(`date`) = 2023 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '11' and year(`date`) = 2023 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 57", "type": "query", "params": [], "bindings": ["11", 2023, "Full", "11", 2023, "Full", 1, 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.651074, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 64.61, "width_percent": 0.794}, {"sql": "select * from `staffs` where `staffs`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.654578, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 65.405, "width_percent": 0.345}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '05' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '05' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 57", "type": "query", "params": [], "bindings": ["05", 2024, "Full", "05", 2024, "Full", 1, 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.656966, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 65.749, "width_percent": 0.536}, {"sql": "select * from `staffs` where `staffs`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.659375, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 66.285, "width_percent": 0.345}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '06' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '06' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 57", "type": "query", "params": [], "bindings": ["06", 2024, "Full", "06", 2024, "Full", 1, 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.66142, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 66.63, "width_percent": 0.326}, {"sql": "select * from `staffs` where `staffs`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, {"index": 34, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.662905, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Expense.php:86", "source": {"index": 21, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=86", "ajax": false, "filename": "Expense.php", "line": "86"}, "connection": "schola", "explain": null, "start_percent": 66.956, "width_percent": 0.228}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '07' and year(`date`) = 2024 and `type` = 'Full') as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = '07' and year(`date`) = 2024 and not `type` = 'Full') as `half_leave` from `leaves` where `status` = 1 and `user_id` = 57", "type": "query", "params": [], "bindings": ["07", 2024, "Full", "07", 2024, "Full", 1, 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/PayrollController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\PayrollController.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.6646988, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Expense.php:90", "source": {"index": 15, "namespace": null, "name": "app/Models/Expense.php", "file": "D:\\laragon\\www\\schola\\app\\Models\\Expense.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=90", "ajax": false, "filename": "Expense.php", "line": "90"}, "connection": "schola", "explain": null, "start_percent": 67.184, "width_percent": 0.468}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6668699, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 67.652, "width_percent": 0.345}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.668755, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 67.997, "width_percent": 0.456}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.670162, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 68.452, "width_percent": 0.246}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6717958, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 68.698, "width_percent": 0.456}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.673346, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 69.154, "width_percent": 0.32}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6749928, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 69.474, "width_percent": 0.363}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.676112, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 69.837, "width_percent": 0.203}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.677453, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 70.041, "width_percent": 0.314}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= ? AND (max_range >= ? OR max_range IS NULL)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6783319, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 70.355, "width_percent": 0.259}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = ? and `expenses`.`staff_id` is not null and `month` = ? and `year` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.679307, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 70.613, "width_percent": 0.363}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.681514, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 70.976, "width_percent": 0.203}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.682903, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 71.18, "width_percent": 0.462}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.684426, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 71.641, "width_percent": 0.191}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.686787, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 71.832, "width_percent": 0.579}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.688557, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 72.411, "width_percent": 0.209}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.689914, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 72.62, "width_percent": 0.419}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.691305, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 73.039, "width_percent": 0.16}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6928291, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 73.199, "width_percent": 0.825}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6950831, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 74.024, "width_percent": 0.296}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.696898, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 74.32, "width_percent": 0.566}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.698631, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 74.886, "width_percent": 0.271}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7008202, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 75.157, "width_percent": 0.603}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.702602, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 75.76, "width_percent": 0.234}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.704062, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 75.994, "width_percent": 0.505}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7056131, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 76.499, "width_percent": 0.277}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.707309, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 76.776, "width_percent": 0.56}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.709031, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 77.337, "width_percent": 0.222}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.710523, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 77.558, "width_percent": 0.486}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7125602, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 78.045, "width_percent": 0.277}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.714729, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 78.322, "width_percent": 0.603}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.716548, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 78.925, "width_percent": 0.234}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.718003, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 79.159, "width_percent": 0.437}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= ? AND (max_range >= ? OR max_range IS NULL)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7213452, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 79.596, "width_percent": 0.277}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = ? and `expenses`.`staff_id` is not null and `month` = ? and `year` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.72215, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 79.873, "width_percent": 0.197}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7238681, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 80.07, "width_percent": 0.185}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.725221, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 80.255, "width_percent": 0.234}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.726008, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 80.489, "width_percent": 0.154}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.727491, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 80.643, "width_percent": 0.456}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.728852, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 81.098, "width_percent": 0.283}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7303731, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 81.382, "width_percent": 0.277}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.731324, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 81.659, "width_percent": 0.16}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.732551, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 81.819, "width_percent": 0.222}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7333548, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 82.04, "width_percent": 0.142}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.734657, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 82.182, "width_percent": 0.339}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7357008, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 82.521, "width_percent": 0.16}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.736953, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 82.681, "width_percent": 0.24}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.737782, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 82.921, "width_percent": 0.154}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7389789, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 83.075, "width_percent": 0.228}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7397819, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 83.303, "width_percent": 0.148}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7410219, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 83.45, "width_percent": 0.228}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.741833, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 83.678, "width_percent": 0.203}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.743209, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 83.881, "width_percent": 0.234}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= ? AND (max_range >= ? OR max_range IS NULL)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7438169, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 84.115, "width_percent": 0.16}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = ? and `expenses`.`staff_id` is not null and `month` = ? and `year` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.744354, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 84.275, "width_percent": 0.185}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7459018, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 84.46, "width_percent": 0.148}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.747077, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 84.608, "width_percent": 0.265}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7480178, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 84.873, "width_percent": 0.16}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.749235, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 85.033, "width_percent": 0.259}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.75014, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 85.291, "width_percent": 0.154}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7513568, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 85.445, "width_percent": 0.252}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7522469, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 85.698, "width_percent": 0.154}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.753473, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 85.851, "width_percent": 0.24}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7543218, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 86.092, "width_percent": 0.154}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7555459, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 86.246, "width_percent": 0.252}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.756425, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 86.498, "width_percent": 0.154}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.757675, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 86.652, "width_percent": 0.252}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.758554, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 86.904, "width_percent": 0.154}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7598479, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 87.058, "width_percent": 0.332}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.760973, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 87.391, "width_percent": 0.24}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.762544, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 87.631, "width_percent": 0.332}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.763625, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 87.963, "width_percent": 0.16}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.764884, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 88.123, "width_percent": 0.259}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= ? AND (max_range >= ? OR max_range IS NULL)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.765783, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 88.382, "width_percent": 0.172}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = ? and `expenses`.`staff_id` is not null and `month` = ? and `year` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.766328, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 88.554, "width_percent": 0.222}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7680109, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 88.776, "width_percent": 0.154}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7692642, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 88.93, "width_percent": 0.493}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7707179, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 89.422, "width_percent": 0.154}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.771944, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 89.576, "width_percent": 0.449}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7733119, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 90.026, "width_percent": 0.154}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.774608, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 90.18, "width_percent": 0.474}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.776028, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 90.654, "width_percent": 0.154}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.777268, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 90.808, "width_percent": 0.443}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.778628, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 91.251, "width_percent": 0.203}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7799652, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 91.454, "width_percent": 0.505}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.781471, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 91.959, "width_percent": 0.154}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.782737, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 92.113, "width_percent": 0.449}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.784101, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 92.562, "width_percent": 0.154}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7853658, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 92.716, "width_percent": 0.462}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7868068, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 93.178, "width_percent": 0.16}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.788127, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 93.338, "width_percent": 0.474}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.789613, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 93.812, "width_percent": 0.209}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.791052, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 94.022, "width_percent": 0.523}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.792615, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 94.545, "width_percent": 0.16}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.793942, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 94.705, "width_percent": 0.499}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7954638, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 95.204, "width_percent": 0.16}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.796847, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 95.364, "width_percent": 0.536}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7984252, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 95.9, "width_percent": 0.166}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.799726, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 96.066, "width_percent": 0.468}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.801126, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 96.534, "width_percent": 0.154}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.802334, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 96.688, "width_percent": 0.437}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.803765, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 97.125, "width_percent": 0.24}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8052368, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 97.365, "width_percent": 0.48}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.806707, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 97.845, "width_percent": 0.154}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8078818, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 97.999, "width_percent": 0.456}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8092608, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 98.455, "width_percent": 0.142}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.810459, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 98.596, "width_percent": 0.443}, {"sql": "select * from `staffs` where `staffs`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8117938, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 99.04, "width_percent": 0.148}, {"sql": "select `leaves`.*, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and `type` = ?) as `full_leave`, (select count(*) from `leave_details` where `leaves`.`id` = `leave_details`.`leave_id` and month(`date`) = ? and year(`date`) = ? and not `type` = ?) as `half_leave` from `leaves` where `status` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.812961, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 99.187, "width_percent": 0.425}, {"sql": "SELECT * FROM epf_contributions WHERE min_range <= ? AND (max_range >= ? OR max_range IS NULL)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.815566, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 99.612, "width_percent": 0.185}, {"sql": "select * from `expenses` where `expenses`.`staff_id` = ? and `expenses`.`staff_id` is not null and `month` = ? and `year` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8161411, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 99.797, "width_percent": 0.203}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 13675, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Leave": {"value": 364, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FLeave.php&line=1", "ajax": false, "filename": "Leave.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 227, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Staff": {"value": 105, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FStaff.php&line=1", "ajax": false, "filename": "Staff.php", "line": "?"}}, "App\\Models\\Expense": {"value": 87, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FExpense.php&line=1", "ajax": false, "filename": "Expense.php", "line": "?"}}, "App\\Models\\User": {"value": 22, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\School": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FSchool.php&line=1", "ajax": false, "filename": "School.php", "line": "?"}}, "App\\Models\\LeaveMaster": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FLeaveMaster.php&line=1", "ajax": false, "filename": "LeaveMaster.php", "line": "?"}}}, "count": 14482, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => payroll-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1095406101 data-indent-pad=\"  \"><span class=sf-dump-note>payroll-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">payroll-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1095406101\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.419537, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "https://schola.test/payroll/1?month=1&order=desc&search=&sort=id&year=2025", "action_name": "payroll.show", "controller_action": "App\\Http\\Controllers\\PayrollController@show", "uri": "GET payroll/{payroll}", "controller": "App\\Http\\Controllers\\PayrollController@show<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=165\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FPayrollController.php&line=165\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/PayrollController.php:165-313</a>", "middleware": "web, Role, auth, checkSchoolStatus, status, language", "duration": "1.61s", "peak_memory": "94MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-659530897 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sort</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n  \"<span class=sf-dump-key>order</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>month</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>year</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2025</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-659530897\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1133615592 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1133615592\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1808075895 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">schola.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">k0nsyLuN5aAz6u8lgTG1r68KyFeDp1NRQbXnBoOH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">https://schola.test/payroll</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"742 characters\">XSRF-TOKEN=eyJpdiI6InY1Nk9GTlo4WUhkTk5UWGkvQkdoR3c9PSIsInZhbHVlIjoibkF6a0xiSi9jOXUwSmVINzNpYy9LQW1iMGc0dmNlbWpLbGpNaGlUMGhqeitUVFZlaGN4QzhacDNqVXNISzlXRy85ZVdGL2RwYkI0cmJSM1FzaVBTKy9uR2hxUFVKbFp5RWZ3N0hLbzgwWk1zVlNCbno4eXFOMTZqZUswcFBFQUgiLCJtYWMiOiI4NWIwZTk3Y2ZmZmNjYWZjMGQ1YzcwMWJhYmI3MTc3YmUwMjJjN2Q0N2JlY2Q5MjVlMzdjMmNjMWVjNjg1N2U2IiwidGFnIjoiIn0%3D; schola_saas_school_management_system_session=eyJpdiI6Imo4ckRadlFLam1xME82b0hHUXlIbUE9PSIsInZhbHVlIjoiN3FWbnVMT3JWdmk1UHNsNmVEaEJDd05oZ045VzQ1MCszc3U3VzZzUWEwSFJxcXdMTi9GdG9ZWk56RWVWUWRIY2kxckdqbW83VjFPcVVBeG1UemFrWUpGL05BR3NaMUlocEtrNXRBQWZYWEtFNmdaRmNLODMxZW1Nc2IxbEtFUjMiLCJtYWMiOiIyNjM2MTBhYmQxNGU2MWExNTI1OWU1ZWYzYzY5NTZiYjMzYTEzZDJhOThkY2NlMWFkYzc3ZmM1Y2E2Mjc4NDIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1808075895\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1096828894 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0nsyLuN5aAz6u8lgTG1r68KyFeDp1NRQbXnBoOH</span>\"\n  \"<span class=sf-dump-key>schola_saas_school_management_system_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IQrLeRaEfTugtoRzBcuJ4wOlCeh4PG6VwaBH418U</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1096828894\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2026552292 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 10 Jun 2025 03:26:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2026552292\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1568687495 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0nsyLuN5aAz6u8lgTG1r68KyFeDp1NRQbXnBoOH</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">https://schola.test/payroll</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>landing_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>language</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Language\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Language</span></span> {<a class=sf-dump-ref>#3547</a><samp data-depth=2 class=sf-dump-compact>\n    #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">languages</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n    +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n    #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n    +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n    +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n    +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">English</span>\"\n      \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"7 characters\">en.json</span>\"\n      \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>is_rtl</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">English</span>\"\n      \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"7 characters\">en.json</span>\"\n      \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>is_rtl</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n    +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n    +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">code</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">is_rtl</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n    </samp>]\n  </samp>}\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1749518683</span>\n  </samp>]\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1568687495\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://schola.test/payroll/1?month=1&order=desc&search=&sort=id&year=2025", "action_name": "payroll.show", "controller_action": "App\\Http\\Controllers\\PayrollController@show"}, "badge": null}}