'click .activate-student': function (e) {
    e.preventDefault();
    var duplicateActiveStudent = {{ json_encode($duplicateActiveStudent) }}; // Embed the PHP variable in JavaScript
    showDeletePopupModal($(e.currentTarget).attr('href'), {
        text: duplicateActiveStudent ? window.trans["Duplicate Active Student Exists"] : window.trans["You want to Activate the Student"],
        confirmButtonText: window.trans["Yes Activate"],
        cancelButtonText: window.trans["Cancel"],
        icon: 'question',
        successCallBack: function () {
            $('#table_list').bootstrapTable('refresh');
        }
    })
}