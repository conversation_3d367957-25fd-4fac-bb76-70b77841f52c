

<?php $__env->startSection('content'); ?>
    <style>
        :root {
            --primary-color: <?php echo e($settings['theme_primary_color'] ?? '#56cc99'); ?>;
            --secondary-color: <?php echo e($settings['theme_secondary_color'] ?? '#215679'); ?>;
            --secondary-color1: <?php echo e($settings['theme_secondary_color_1'] ?? '#38a3a5'); ?>;
            --primary-background-color: <?php echo e($settings['theme_primary_background_color'] ?? '#f2f5f7'); ?>;
            --text--secondary-color: <?php echo e($settings['theme_text_secondary_color'] ?? '#5c788c'); ?>;
        }
    </style>
    <script src="<?php echo e(asset('assets/home_page/js/jquery-1-12-4.min.js')); ?>"></script>

    <header class="navbar">
        <div class="container">
            <div class="navbarWrapper">
                <div class="navLogoWrapper">
                    <div class="navLogo">
                        <a href="<?php echo e(url('/')); ?>">
                            <img src="<?php echo e($settings['horizontal_logo'] ?? asset('assets/landing_page_images/Logo1.svg')); ?>"
                                class="logo" alt="">
                        </a>
                    </div>
                </div>
                <div class="menuListWrapper">
                    <ul class="listItems">
                        <li>
                            <a href="#home"><?php echo e(__('home')); ?></a>
                        </li>
                        <li>
                            <a href="#features"><?php echo e(__('features')); ?></a>
                        </li>
                        <li>
                            <a href="#about-us"><?php echo e(__('about_us')); ?></a>
                        </li>
                        <li>
                            <a href="#pricing"><?php echo e(__('pricing')); ?></a>
                        </li>
                        <?php if(count($faqs)): ?>
                            <li>
                                <a href="#faq"><?php echo e(__('faqs')); ?></a>
                            </li>
                        <?php endif; ?>
                        <li>
                            <a href="#contact-us"><?php echo e(__('contact')); ?></a>
                        </li>
                        <?php if(count($guidances)): ?>
                            <li>
                                <div class="dropdown">
                                    <a class="btn btn-secondary dropdown-toggle" href="#" role="button"
                                        id="dropdownMenuLink" data-bs-toggle="dropdown" aria-expanded="false">
                                        <?php echo e(__('guidance')); ?>

                                    </a>
                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                        <?php $__currentLoopData = $guidances; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $guidance): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li><a class="dropdown-item"
                                                    href="<?php echo e($guidance->link); ?>"><?php echo e($guidance->name); ?></a></li>
                                            <?php if(count($guidances) > $key + 1): ?>
                                                <hr>
                                            <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </div>
                            </li>
                        <?php endif; ?>
                        <li>
                            <div class="dropdown">
                                <a class="btn btn-secondary dropdown-toggle" href="#" role="button"
                                    id="dropdownMenuLink" data-bs-toggle="dropdown" aria-expanded="false">
                                    <?php echo e(__('language')); ?>

                                </a>

                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                    <?php $__currentLoopData = $languages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li><a class="dropdown-item"
                                                href="<?php echo e(url('set-language') . '/' . $language->code); ?>"><?php echo e($language->name); ?></a>
                                        </li>
                                        <?php if(count($languages) > $key + 1): ?>
                                            <hr>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>
                        </li>

                    </ul>
                    <div class="hamburg">
                        <span data-bs-toggle="offcanvas" data-bs-target="#offcanvasRight" aria-controls="offcanvasRight"><i
                                class="fa-solid fa-bars"></i></span>
                    </div>
                </div>

                <div class="loginBtnsWrapper">
                    <button class="commonBtn redirect-login"><?php echo e(__('login')); ?></button>
                    <button class="commonBtn" data-bs-toggle="modal"
                        data-bs-target="#staticBackdrop"><?php echo e(__('start_trial')); ?></button>
                </div>
            </div>

            <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasRight" aria-labelledby="offcanvasRightLabel">
                <div class="offcanvas-header">
                    <div class="navLogoWrapper">
                        <div class="navLogo">
                            <img src="<?php echo e($settings['horizontal_logo'] ?? asset('assets/landing_page_images/Logo1.svg')); ?>"
                                alt="">
                        </div>
                    </div>
                    <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas"
                        aria-label="Close"></button>
                </div>
                <div class="offcanvas-body">
                    <ul class="listItems">
                        <li>
                            <a href="#home"><?php echo e(__('home')); ?></a>
                        </li>
                        <li>
                            <a href="#features"><?php echo e(__('features')); ?></a>
                        </li>
                        <li>
                            <a href="#about-us"><?php echo e(__('about_us')); ?></a>
                        </li>
                        <li>
                            <a href="#pricing"><?php echo e(__('pricing')); ?></a>
                        </li>
                        <?php if(count($faqs)): ?>
                            <li>
                                <a href="#faq"><?php echo e(__('faqs')); ?></a>
                            </li>
                        <?php endif; ?>
                        <li>
                            <a href="#contact-us"><?php echo e(__('contact')); ?></a>
                        </li>
                        <?php if(count($guidances)): ?>
                            <li>
                                <div class="dropdown">
                                    <a class="btn btn-secondary dropdown-toggle" href="#" role="button"
                                        id="dropdownMenuLink" data-bs-toggle="dropdown" aria-expanded="false">
                                        <?php echo e(__('guidance')); ?>

                                    </a>
                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                        <?php $__currentLoopData = $guidances; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $guidance): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li><a class="dropdown-item"
                                                    href="<?php echo e($guidance->link); ?>"><?php echo e($guidance->name); ?></a></li>
                                            <?php if(count($guidances) > $key + 1): ?>
                                                <hr>
                                            <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </div>
                            </li>
                        <?php endif; ?>
                        <li>
                            <div class="dropdown">
                                <a class="btn btn-secondary dropdown-toggle" href="#" role="button"
                                    id="dropdownMenuLink" data-bs-toggle="dropdown" aria-expanded="false">
                                    <?php echo e(__('language')); ?>

                                </a>

                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                    <?php $__currentLoopData = $languages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li><a class="dropdown-item"
                                                href="<?php echo e(url('set-language') . '/' . $language->code); ?>"><?php echo e($language->name); ?></a>
                                        </li>
                                        <?php if(count($languages) > $key + 1): ?>
                                            <hr>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>
                        </li>

                    </ul>

                    
                    <button class="commonBtn redirect-login"><?php echo e(__('login')); ?></button>
                    <button class="commonBtn" data-bs-toggle="modal" data-bs-dismiss="offcanvas"
                        data-bs-target="#staticBackdrop"><?php echo e(__('start_trial')); ?></button>
                    
                </div>
            </div>
        </div>
    </header>

    <!-- navbar ends here  -->

    <div class="main">

        <section class="heroSection" id="home">
            <div class="linesBg">
                <div class="colorBg">
                    <div class="container">
                        <div class="row">
                            <div class="col-md-12 col-lg-6">
                                <div class="flex_column_start">
                                    <span class="commonTitle"><?php echo e($settings['system_name'] ?? 'Schola SaaS'); ?></span>
                                    <span class="commonDesc">
                                        <?php echo e($settings['tag_line']); ?>

                                    </span>
                                    <span class="commonText">
                                        <?php echo e($settings['hero_description']); ?></span>
                                    <div class="buttonWrapper" style="width:auto;">
                                        <div>
                                            <div>
                                                <button class="commonBtn" data-bs-toggle="modal"
                                                    data-bs-target="#staticBackdrop"><?php echo e(__('register_your_school')); ?></button>
                                            </div>
                                            <div>
                                                <button class="commonBtn" data-bs-toggle="modal" data-bs-target="#addmissionBackdrop"
                                                    id="admissionPopup"
                                                    style="background-color:#097969;padding:12px 40px;"><?php echo e(__('online_admission')); ?></button>
                                            </div>
                                            <!-- <div>
                                                <button class="commonBtn" id="attendance"
                                                    style="padding:12px 32px;"><?php echo e(__('attendance_tracker')); ?></button>
                                            </div> -->
                                            <!-- <div>
                                                <button class="commonBtn" style= "padding-right:60px; text-indent: 20px;" id="reward" data-bs-toggle="modal"><?php echo e(__('reward_point')); ?></button>
                                            </div> -->
                                            <div>
                                                <button class="commonBtn" id="attendance_timetable" style="padding:12px 32px;"><?php echo e(__('attendance_tracker')); ?></button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 col-lg-6 heroImgWrapper">
                                <div class="heroImg">
                                    <img src="<?php echo e($settings['home_image'] ?? asset('assets/landing_page_images/heroImg.png')); ?>"
                                        alt="">
                                    <div class="topRated card">
                                        <div>
                                            <img src="<?php echo e($settings['hero_title_2_image'] ?? asset('assets/landing_page_images/user.png')); ?>"
                                                alt="">
                                        </div>
                                        <div>
                                            <span><?php echo e($settings['hero_title_2']); ?></span>
                                        </div>
                                    </div>
                                    <div class="textWrapper">
                                        <span><?php echo e($settings['hero_title_1']); ?></span>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <?php echo $__env->make('registration_form', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php echo $__env->make('admission_form', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php echo $__env->make('term_condition', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        </section>
        <!-- heroSection ends here  -->

        <section class="features commonMT container" id="features">
            <div class="row">
                <div class="col-12">
                    <div class="sectionTitle">
                        <span><?php echo e(__('explore_our_top_features')); ?></span>

                    </div>
                </div>
                <div class="col-12">
                    <div class="row cardWrapper">
                        <?php $__currentLoopData = $features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($key < 9): ?>
                                <div class="col-sm-12 col-md-6 col-lg-4">
                                    <div class="card">
                                        <div>
                                            <img src="<?php echo e(asset('assets/landing_page_images/features/')); ?>/<?php echo e($feature->name); ?>.svg"
                                                alt="">
                                        </div>
                                        <div><span><?php echo e(__($feature->name)); ?></span></div>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="col-sm-12 col-md-6 col-lg-4 default-feature-list" style="display: none">
                                    <div class="card">
                                        <div>
                                            <img src="<?php echo e(asset('assets/landing_page_images/features/')); ?>/<?php echo e($feature->name); ?>.svg"
                                                alt="">
                                        </div>
                                        <div><span><?php echo e(__($feature->name)); ?></span></div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-12">
                            <button class="commonBtn view-more-feature"
                                value="1"><?php echo e(__('view_more_features')); ?></button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- features ends here  -->

        <?php if($settings['display_school_logos'] ?? '1'): ?>
            <section class="swiperSect container commonMT">
                <div class="row">
                    <div class="col-12">
                        <div class="commonSlider">
                            <div class="slider-content owl-carousel">
                                <!-- Example slide -->
                                <?php $__currentLoopData = $schoolSettings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $school): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if(Storage::disk('public')->exists($school->getRawOriginal('data')) && $school->data): ?>
                                        <div class="swiperDataWrapper">
                                            <div class="card">
                                                <img src="<?php echo e($school->data); ?>" class="normalImg" alt="">
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <!-- Add more swiperDataWrapper elements here -->
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        <?php endif; ?>
        <!-- swiperSect ends here  -->
        <?php if($settings['display_counters'] ?? '1'): ?>
            <section class="counterSect commonMT container">
                <div class="">
                    <div class="row counterBG">
                        <div class="col-4 col-sm-4 col-md-4 col-lg-4">
                            <div class="card">
                                <div><span class="numb" data-target="<?php echo e($counter['school']); ?>">0</span><span>+</span>
                                </div>
                                <div><span class="text"><?php echo e(__('schools')); ?></span></div>
                            </div>
                        </div>
                        <div class="col-4 col-sm-4 col-md-4 col-lg-4">
                            <div class="card">
                                <div><span class="numb" data-target="<?php echo e($counter['teacher']); ?>">0</span><span>+</span>
                                </div>
                                <div><span class="text"><?php echo e(__('teachers')); ?></span></div>
                            </div>
                        </div>
                        <div class="col-4 col-sm-4 col-md-4 col-lg-4">
                            <div class="card">
                                <div><span class="numb" data-target="<?php echo e($counter['student']); ?>">0</span><span>+</span>
                                </div>
                                <div><span class="text"><?php echo e(__('students')); ?></span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        <?php endif; ?>

        <?php $__currentLoopData = $featureSections; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if(($key + 1) % 2 != 0): ?>
                <section class="left-section-<?php echo e($section->id); ?> commonMT container">
                    <div class="row">
                        <div class="col-12">
                            <div class="sectionTitle">
                                <span class="greenText"><?php echo e($section->title); ?></span>
                                <span>
                                    <?php echo e($section->heading); ?>

                                </span>

                            </div>
                        </div>
                        <div class="col-12 tabsContainer">
                            <div class="row">
                                <div class="col-lg-6 tabsMainWrapper">
                                    <div class="tabsWrapper">
                                        <div class="tabs">
                                            <?php $__currentLoopData = $section->feature_section_list; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $section_feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="tab tab-<?php echo e($section_feature->id); ?>-<?php echo e($key); ?>">
                                                    <span><?php echo e($section_feature->feature); ?></span>
                                                    <span>
                                                        <?php echo e($section_feature->description); ?>

                                                    </span>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    </div>

                                </div>

                                <div class="col-lg-6 contentWrapper">
                                    <div class="content-container">
                                        <?php $__currentLoopData = $section->feature_section_list; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $section_feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="content tab-<?php echo e($section_feature->id); ?>-<?php echo e($key); ?>">
                                                <img src="<?php echo e($section_feature->image); ?>" alt="">
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </section>
            <?php else: ?>
                <section class="right-section-<?php echo e($section->id); ?> right-feature-section commonMT">
                    <div class="container">
                        <div class="row">
                            <div class="col-12">
                                <div class="sectionTitle">
                                    <span class="greenText"><?php echo e($section->title); ?></span>
                                    <span>
                                        <?php echo e($section->heading); ?>

                                    </span>

                                </div>
                            </div>
                            <div class="col-12 tabsContainer">
                                <div class="row reverseWrapper">
                                    <div class="col-lg-6 contentWrapper">
                                        <div class="content-container">
                                            <?php $__currentLoopData = $section->feature_section_list; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $section_feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="content tab-<?php echo e($section_feature->id); ?>-<?php echo e($key); ?>">
                                                    <img src="<?php echo e($section_feature->image); ?>" alt="">
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    </div>

                                    <div class="col-lg-6 tabsMainWrapper">
                                        <div class="tabsWrapper">
                                            <div class="tabs">
                                                <?php $__currentLoopData = $section->feature_section_list; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $section_feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="tab tab-<?php echo e($section_feature->id); ?>-<?php echo e($key); ?>">
                                                        <span><?php echo e($section_feature->feature); ?></span>
                                                        <span>
                                                            <?php echo e($section_feature->description); ?>

                                                        </span>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </section>
            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

        <section class="whyBest container commonMT" id="about-us">
            <div class="row">
                <div class="col-lg-6">
                    <div class="whyBestTextWrapper">
                        <p><?php echo e($settings['about_us_title']); ?></p>
                        <p><?php echo e($settings['about_us_heading']); ?></p>
                    </div>
                    <p class="whyBestPara">
                        <?php echo e($settings['about_us_description']); ?>

                    </p>

                    <div class="listWrapper">
                        <?php $__currentLoopData = $about_us_lists; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $point): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <span>
                                <i class="fa-regular fa-circle-check"></i>
                                <?php echo e($point); ?>

                            </span>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>

                <div class="col-lg-6">
                    <img src="<?php echo e($settings['about_us_image'] ?? asset('assets/landing_page_images/whyBestImg.png')); ?>"
                        alt="">
                </div>
            </div>
        </section>
        <!-- whyBest ends here  -->

        <section class="pricing" id="">
            <div class="container commonMT">
                <div class="row">
                    <div class="col-12">
                        <div class="sectionTitle">
                            <span><?php echo e(__('feature_highlights')); ?></span>
                        </div>
                    </div>
                    <div class="col-12 swiperWrapper">
                        <div class="commonSlider">
                            <div class="slider-content owl-carousel">

                                <?php $__currentLoopData = $videoLinks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $videoLink): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="swiperDataWrapper">
                                        <div class="videoBox premium">
                                            <div class="startUpWrapper">
                                                <div class="textDivVideo">
                                                    <span class="title"><?php echo e($videoLink->title); ?></span>
                                                </div>
                                                <div class="listWrapper">
                                                    <?php
                                                        // Extract the video ID from the YouTube link
                                                        preg_match(
                                                            '/(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/',
                                                            $videoLink->video_link,
                                                            $matches,
                                                        );
                                                        $videoID = $matches[1] ?? null;
                                                    ?>

                                                    <?php if($videoID): ?>
                                                        <div class="swiper-slide">
                                                            <iframe width="345" height="250"
                                                                src="https://www.youtube.com/embed/<?php echo e($videoID); ?>"
                                                                frameborder="0"
                                                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                                                allowfullscreen></iframe>
                                                        </div>
                                                    <?php else: ?>
                                                        <div class="swiper-slide">
                                                            <p>Invalid YouTube link: <?php echo e($videoLink->video_link); ?></p>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="textDivVideoDescription">
                                                    <span class="description"><?php echo e($videoLink->description); ?></span>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- video ends here  -->

        <section class="pricing" id="pricing">
            <div class="container commonMT">
                <div class="row">
                    <div class="col-12">
                        <div class="sectionTitle">
                            <span><?php echo e(__('flexible_pricing_packages')); ?></span>

                        </div>
                    </div>
                    <div class="col-12 swiperWrapper">
                        <div class="commonSlider">
                            <div class="slider-content owl-carousel">

                                <?php $__currentLoopData = $packages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $package): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($package->highlight): ?>
                                        <div class="swiperDataWrapper">
                                            <div class="pricingBox premium">
                                                <div class="startUpWrapper">
                                                    <?php if($package->is_trial == 1): ?>
                                                        <!-- <span class="badge postpaid"><?php echo e(__('free')); ?></span> -->
                                                    <?php else: ?>
                                                        <?php if($package->type == 1): ?>
                                                            <!-- <span class="badge postpaid"><?php echo e(__('postpaid')); ?></span> -->
                                                        <?php else: ?>
                                                            <!-- <span class="badge prepaid"><?php echo e(__('prepaid')); ?></span> -->
                                                        <?php endif; ?>
                                                    <?php endif; ?>
                                                    <div class="textDiv">
                                                        <span class="title"><?php echo e(__($package->name)); ?></span>
                                                        <!-- <?php if($package->is_trial == 1): ?>
    <span>
                                                                <?php echo e($settings['student_limit']); ?> <?php echo e(__('student_limit')); ?>

                                                            </span>
                                                            <span>
                                                                <?php echo e($settings['staff_limit']); ?> <?php echo e(__('staff_limit')); ?>

                                                            </span>
<?php elseif($package->type == 0 && $package->is_trial == 0): ?>
    <span>
                                                                <?php echo e(number_format($package->no_of_students, 0)); ?> <?php echo e(__('student_limit')); ?>

                                                            </span>
                                                            <span>
                                                                <?php echo e(number_format($package->no_of_staffs, 0)); ?> <?php echo e(__('staff_limit')); ?>

                                                            </span>
                                                            <span>
                                                                <?php echo e($settings['currency_symbol'] ?? 'RM'); ?> <?php echo e(number_format($package->charges, 2)); ?> <?php echo e(__('package_amount')); ?>

                                                            </span>
<?php elseif($package->type == 1 && $package->is_trial == 0): ?>
    <span>
                                                                <?php echo e($settings['currency_symbol'] ?? 'RM'); ?> <?php echo e(number_format($package->student_charge, 2)); ?> <?php echo e(__('per_student_charges')); ?>

                                                            </span>
                                                            <span>
                                                                <?php echo e($settings['currency_symbol'] ?? 'RM'); ?> <?php echo e(number_format($package->staff_charge, 2)); ?> <?php echo e(__('per_staff_charges')); ?>

                                                            </span>
    <?php endif; ?> -->
                                                        <span class="days"><?php echo e($package->days); ?>

                                                            <?php echo e(__('days')); ?></span>
                                                    </div>
                                                    <div class="listWrapper">
                                                        <?php $__currentLoopData = $features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <?php if(in_array($feature->id, $package->package_feature->pluck('feature_id')->toArray())): ?>
                                                                <span>
                                                                    <img src="<?php echo e(asset('assets/landing_page_images/right.svg')); ?>"
                                                                        class="rightTickImg" alt="">
                                                                    <?php echo e(__($feature->name)); ?>

                                                                </span>
                                                            <?php else: ?>
                                                                <span class="lineThrough">
                                                                    <img src="<?php echo e(asset('assets/landing_page_images/cross.svg')); ?>"
                                                                        class="wrongTickImg" alt="">
                                                                    <?php echo e(__($feature->name)); ?>

                                                                </span>
                                                            <?php endif; ?>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </div>
                                                    <button class="pricingBtn" data-bs-toggle="modal"
                                                        data-bs-target="#staticBackdrop"><?php echo e(__('get_started')); ?></button>
                                                </div>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <div class="swiperDataWrapper">
                                            <div class="pricingBox">
                                                <div class="startUpWrapper">
                                                    <?php if($package->is_trial == 1): ?>
                                                        <span class="badge postpaid"><?php echo e(__('free')); ?></span>
                                                    <?php else: ?>
                                                        <?php if($package->type == 1): ?>
                                                            <span class="badge postpaid"><?php echo e(__('postpaid')); ?></span>
                                                        <?php else: ?>
                                                            <span class="badge prepaid"><?php echo e(__('prepaid')); ?></span>
                                                        <?php endif; ?>
                                                    <?php endif; ?>
                                                    <div class="textDiv">
                                                        <span class="title"><?php echo e(__($package->name)); ?></span>
                                                        <?php if($package->is_trial == 1): ?>
                                                            <?php if(false): ?>
                                                                <span>
                                                                    <?php echo e($settings['student_limit']); ?>

                                                                    <?php echo e(__('student_limit')); ?>

                                                                </span>
                                                                <span>
                                                                    <?php echo e($settings['staff_limit']); ?>

                                                                    <?php echo e(__('staff_limit')); ?>

                                                                </span>
                                                            <?php endif; ?>
                                                        <?php elseif($package->type == 0 && $package->is_trial == 0): ?>
                                                            <span>
                                                                <?php echo e(number_format($package->no_of_students, 0)); ?>

                                                                <?php echo e(__('student_limit')); ?>

                                                            </span>
                                                            <span>
                                                                <?php echo e(number_format($package->no_of_staffs, 0)); ?>

                                                                <?php echo e(__('staff_limit')); ?>

                                                            </span>
                                                            <span>
                                                                <?php echo e($settings['currency_symbol'] ?? 'RM'); ?>

                                                                <?php echo e(number_format($package->charges, 2)); ?>

                                                                <?php echo e(__('package_amount')); ?>

                                                            </span>
                                                        <?php elseif($package->type == 1 && $package->is_trial == 0): ?>
                                                            <span>
                                                                <?php echo e($settings['currency_symbol'] ?? 'RM'); ?>

                                                                <?php echo e(number_format($package->student_charge, 2)); ?>

                                                                <?php echo e(__('per_student_charges')); ?>

                                                            </span>
                                                            <span>
                                                                <?php echo e($settings['currency_symbol'] ?? 'RM'); ?>

                                                                <?php echo e(number_format($package->staff_charge, 2)); ?>

                                                                <?php echo e(__('per_staff_charges')); ?>

                                                            </span>
                                                        <?php endif; ?>
                                                        <span class="days"><?php echo e($package->days); ?>

                                                            <?php echo e(__('days')); ?></span>
                                                    </div>
                                                    <div class="listWrapper">
                                                        <?php $__currentLoopData = $features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <?php if(in_array($feature->id, $package->package_feature->pluck('feature_id')->toArray())): ?>
                                                                <span>
                                                                    <img src="<?php echo e(asset('assets/landing_page_images/right.svg')); ?>"
                                                                        class="rightTickImg" alt="">
                                                                    <?php echo e(__($feature->name)); ?>

                                                                </span>
                                                            <?php else: ?>
                                                                <span class="lineThrough">
                                                                    <img src="<?php echo e(asset('assets/landing_page_images/cross.svg')); ?>"
                                                                        class="wrongTickImg" alt="">
                                                                    <?php echo e(__($feature->name)); ?>

                                                                </span>
                                                            <?php endif; ?>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </div>
                                                    <button class="pricingBtn" data-bs-toggle="modal"
                                                        data-bs-target="#staticBackdrop"><?php echo e(__('get_started')); ?></button>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- pricing ends here  -->

        <?php if(isset($settings['custom_package_status']) && $settings['custom_package_status']): ?>
            <section class="customPack container commonMT">
                <div class="wrapper">
                    <div class="row">
                        <div class="col-sm-12 col-md-6 col-lg-6">
                            <div>
                                <p class="title"><?php echo e(__('custom_package')); ?></p>
                                <p class="desc">
                                    <?php echo e($settings['custom_package_description'] ?? ''); ?>

                                </p>
                            </div>
                        </div>

                        <div class="col-sm-12 col-md-6 col-lg-6">
                            <a href="#contact-us" class="commonBtn text-center"><?php echo e(__('get_in_touch')); ?></a>
                        </div>

                    </div>
                </div>
            </section>
        <?php endif; ?>

        <?php if(count($faqs)): ?>
            <section class="faqs commonMT" id="faq">
                <div class="container">
                    <div class="row">
                        <div class="col-12">
                            <div class="sectionTitle">
                                <span><?php echo e(__('frequently_asked_questions')); ?></span>

                            </div>
                        </div>

                        <div class="col-12">
                            <div class="accordion" id="accordionExample">
                                <?php $__currentLoopData = $faqs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $faq): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="accordion-item">
                                        <h2 class="accordion-header" id="headingOne">
                                            <button class="accordion-button collapsed" type="button"
                                                data-bs-toggle="collapse"
                                                data-bs-target="#collapseOne-<?php echo e($faq->id); ?>" aria-expanded="true"
                                                aria-controls="collapseOne-<?php echo e($faq->id); ?>">
                                                <span>
                                                    <?php echo e($faq->title); ?>

                                                </span>
                                            </button>
                                        </h2>
                                        <div id="collapseOne-<?php echo e($faq->id); ?>" class="accordion-collapse collapse"
                                            aria-labelledby="headingOne" data-bs-parent="#accordionExample">
                                            <div class="accordion-body">
                                                <span>
                                                    <?php echo e($faq->description); ?>

                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        <?php endif; ?>
        <!-- faqs ends here  -->

        <section class="getInTouch commonMT" id="contact-us">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="sectionTitle">
                            <span class="greenText"><?php echo e(__('lets_get_in_touch')); ?></span>
                            <span><?php echo e(__('have_a_question_or_just_want_to_say_hi_Wed_love_to_hear_from_you')); ?>

                            </span>

                        </div>
                        <div class="col-12">
                            <div class="row wrapper">
                                <div class="col-lg-6">
                                    <form action="<?php echo e(url('contact')); ?>" method="post" role="form"
                                        class="php-email-form mb-5 create-form-with-captcha">
                                        <?php echo csrf_field(); ?>
                                        <div class="card">
                                            <div>
                                                <input type="text" required name="name" id="name"
                                                    placeholder="<?php echo e(__('enter_your_name')); ?>">
                                            </div>
                                            <div>
                                                <input type="email" required name="email" id="email"
                                                    placeholder="<?php echo e(__('enter_your_email')); ?>">
                                            </div>
                                            <div>
                                                <textarea name="message" required id="message" cols="30" rows="6"
                                                    placeholder="<?php echo e(__('send_your_message')); ?>"></textarea>
                                            </div>
                                            <?php if(config('services.recaptcha.key') ?? ''): ?>
                                                <div>
                                                    <div class="g-recaptcha"
                                                        data-sitekey=<?php echo e(config('services.recaptcha.key')); ?>></div>
                                                </div>
                                            <?php endif; ?>
                                            <div>
                                                <button class="commonBtn"><?php echo e(__('send')); ?></button>
                                            </div>
                                            <div>
                                                <img src="<?php echo e(asset('assets/landing_page_images/GetInTouchDots.png')); ?>"
                                                    class="sideImg dots" alt="">
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                <div class="col-lg-6 infoBox">
                                    <div class="infoWrapper">
                                        <div>
                                            <span class="icon"><i class="fa-solid fa-phone-volume"></i></span>
                                        </div>
                                        <div>
                                            <span><?php echo e(__('phone')); ?></span>
                                            <span><?php echo e(__('mobile')); ?> : <?php echo e($settings['mobile'] ?? ''); ?></span>
                                        </div>
                                    </div>
                                    <div class="infoWrapper">
                                        <div>
                                            <span class="icon"><i class="fa-solid fa-envelope-open-text"></i></span>
                                        </div>
                                        <div>
                                            <span><?php echo e(__('email')); ?></span>
                                            <span><?php echo e($settings['mail_send_from'] ?? '<EMAIL>'); ?></span>
                                        </div>
                                    </div>
                                    <div class="infoWrapper">
                                        <div>
                                            <span class="icon"><i class="fa-solid fa-location-dot"></i></span>
                                        </div>
                                        <div>
                                            <span><?php echo e(__('location')); ?></span>
                                            <span><?php echo e($settings['address'] ?? ''); ?></span>
                                        </div>
                                    </div>
                                    <div>
                                        <img src="<?php echo e(asset('assets/landing_page_images/lineCircle.png')); ?>"
                                            class="lineCircle sideImg" alt="">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </section>

        <section class="picturee" id="">
            <div class="container commonMT">
                <div class="row">
                    <div class="col-12">
                        <div class="sectionTitle">
                            <span><?php echo e(__('my_partner')); ?></span>
                        </div>
                    </div>
                    <div class="col-12 abc">
                        <!-- <div class="commonSlider"> -->
                        <!-- <div class="slider-content owl-carousel"> -->
                        <?php $__currentLoopData = $pictureLinks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pictureLink): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="swiperDataWrapper">
                                <div class="pictureBox">
                                    <?php if($pictureLink->id): ?>
                                        <img src="<?php echo e(asset('storage/' . $pictureLink->picture)); ?>"
                                            alt="<?php echo e($pictureLink->title); ?>" width="50" height="50"
                                            border-radius="50%">
                                    <?php else: ?>
                                        <div class="swiper-slide">
                                            <p>no image available</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
    </div>
    </section>
    <section class="ourApp container commonMT">
        <div class="row">
            <div class="col-lg-6">
                <img src="<?php echo e($settings['download_our_app_image'] ?? asset('assets/landing_page_images/ourApp.png')); ?>"
                    class="ourAppImg" alt="">
            </div>
            <div class="col-lg-6 content">
                <div class="text">
                    <span class="title"><?php echo e(__('download_our_app_now')); ?></span>
                    <span>
                        <?php echo e($settings['download_our_app_description'] ?? ''); ?>

                    </span>
                </div>
                <div class="storeImgs">
                    <a href="<?php echo e($settings['app_link'] ?? ''); ?>" target="_blank"> <img
                            src="<?php echo e(asset('assets/landing_page_images/Google play.png')); ?>" alt=""> </a>
                    <a href="<?php echo e($settings['ios_app_link'] ?? ''); ?>" target="_blank"> <img
                            src="<?php echo e(asset('assets/landing_page_images/iOS app Store.png')); ?>" alt=""> </a>
                </div>
            </div>
        </div>
    </section>
    </div>

    <!-- Include SweetAlert library -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('script'); ?>
    <script async src="https://www.google.com/recaptcha/api.js"></script>
    <?php $__currentLoopData = $featureSections; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <script>
            document.addEventListener('DOMContentLoaded', () => {
                const tabs = document.querySelectorAll('.left-section-<?php echo e($section->id); ?> .tab');
                const contents = document.querySelectorAll('.left-section-<?php echo e($section->id); ?> .content');

                function switchTab(event, tabNumber) {
                    tabs.forEach((tab) => {
                        tab.classList.remove('active');
                    });

                    event.target.classList.add('active');

                    contents.forEach((content) => {
                        content.classList.remove('active');
                    });

                    contents[tabNumber - 1].classList.add('active');
                }

                tabs.forEach((tab, index) => {
                    tab.addEventListener('click', (event) => {
                        switchTab(event, index + 1);
                    });
                });

                setTimeout(() => {
                    tabs[0].click();
                }, 1000);
            });

            document.addEventListener('DOMContentLoaded', () => {
                const tabs = document.querySelectorAll('.right-section-<?php echo e($section->id); ?> .tab');
                const contents = document.querySelectorAll('.right-section-<?php echo e($section->id); ?> .content');

                function switchTab(event, tabNumber) {
                    tabs.forEach((tab) => {
                        tab.classList.remove('active');
                    });

                    event.target.classList.add('active');

                    contents.forEach((content) => {
                        content.classList.remove('active');
                    });

                    contents[tabNumber - 1].classList.add('active');
                }

                tabs.forEach((tab, index) => {
                    tab.addEventListener('click', (event) => {
                        switchTab(event, index + 1);
                    });
                });

                setTimeout(() => {
                    tabs[0].click();
                }, 1000);
            });
        </script>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <script>
        $('.redirect-login').click(function(e) {
            e.preventDefault();
            window.location.href = "<?php echo e(url('login')); ?>"
        });
    </script>
    <script>
        <?php if(Session::has('success')): ?>
            $.toast({
                text: '<?php echo e(Session::get('success')); ?>',
                showHideTransition: 'slide',
                icon: 'success',
                loaderBg: '#f96868',
                position: 'top-right',
                bgColor: '#20CFB5'
            });
        <?php endif; ?>

        <?php if(Session::has('error')): ?>
            $.toast({
                text: '<?php echo e(Session::get('error')); ?>',
                showHideTransition: 'slide',
                icon: 'error',
                loaderBg: '#f2a654',
                position: 'top-right',
                bgColor: '#FE7C96'
            });
        <?php endif; ?>
    </script>
    <script>
        $('#admissionPopup').click(async function() {
            Swal.fire({
                title: "Submit your school code",
                input: "text",
                inputAttributes: {
                    autocapitalize: "off"
                },
                showCancelButton: true,
                confirmButtonText: "Confirm",
                showLoaderOnConfirm: true,
                preConfirm: async (schoolCode) => {
                    if (!schoolCode) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Invalid School Code',
                            text: 'The school code entered is invalid. Please try again.'
                        });
                        return;
                    }
                    try {
                        const response = await fetch(`admission-form/${schoolCode}`);
                        if (!response.ok) {
                            const errorData = await response.json();
                            Swal.fire({
                                icon: 'error',
                                title: 'Oh no!',
                                text: errorData.message ||
                                    'An unexpected error occurred. Please try again.'
                            });
                            return false;
                        }
                        const resultData = await response.json();
                        const schoolId = resultData.school_id;

                        // Fetch terms and conditions
                        const termsResponse = await fetch(`/terms-condition/${schoolId}`);
                        if (!termsResponse.ok) {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: 'Could not retrieve terms and conditions. Please try again.'
                            });
                            return false;
                        }
                        const termsData = await termsResponse.json();
                        if (termsData.terms_condition) {
                            $('#termsConditionField').html(htmlEntityDecode(termsData
                                .terms_condition));
                            $('#TermsBackdrop').modal(
                                'show'); // Show modal if terms_condition is present
                        } else {
                            $('#admissionBackdrop').modal('show');
                        }

                        return resultData; // Return data for further processing
                    } catch (error) {
                        Swal.showValidationMessage(`Request failed: ${error}`);
                        return false;
                    }
                },
                allowOutsideClick: () => !Swal.isLoading()
            }).then((result) => {
                if (result.isConfirmed) {
                    if (result.value) {
                        const schoolId = result.value.school_id;
                        const classSections = result.value.class_sections;
                        const sessionYears = result.value.session_years;
                        const extraFields = result.value.extra_fields;
                        const admissionNo = result.value.admission_no;

                        //School Id
                        $('#schoolIdInput').val(schoolId);

                        //Class Section
                        const selectElement = $('#class_section');
                        selectElement.empty();

                        if (classSections && classSections.length > 0) {
                            $.each(classSections, function(index, section) {
                                const optionValue = section.id;
                                const optionText =
                                    `${section.class_name} ${section.section_name || ""} - ${section.medium_name || ""}`;

                                selectElement.append($('<option>', {
                                    value: optionValue,
                                    text: optionText
                                }));
                            });
                        } else {
                            selectElement.append($('<option>', {
                                value: '',
                                text: 'No class sections available'
                            }));
                        }

                        //Session Year
                        const sessionYearSelect = $('#session_year');
                        sessionYearSelect.empty();

                        if (sessionYears && sessionYears.length > 0) {
                            $.each(sessionYears, function(index, year) {
                                const option = $('<option>', {
                                    value: year.id,
                                    text: year.name,
                                    selected: year.default == 1
                                });
                                sessionYearSelect.append(option);
                            });
                        } else {
                            sessionYearSelect.append($('<option>', {
                                value: '',
                                text: 'No session years available'
                            }));
                        }

                        const extraFieldsContainer = $('#extraFieldsContainer');
                        extraFieldsContainer.empty();

                        if (extraFields && Array.isArray(extraFields) && extraFields.length > 0) {
                            $.each(extraFields, function(index, field) {
                                let fieldHtml = '';
                                if(field.type != 'fullwidthtitle'){
                                    fieldHtml += '<div class="inputWrapper">';
                                    if (field.type !== 'radio' && field.type !== 'checkbox') {
                                        fieldHtml +=
                                            `<label>${field.name} ${field.is_required ? '<span class="text-danger">*</span>' : ''}</label>`;
                                    }
                                    fieldHtml +=
                                        `<input type="hidden" id="${field.type}_${index}_id" name="extra_fields[${index}][id]">`;
                                    fieldHtml +=
                                        `<input type="hidden" id="${field.type}_${index}_id" name="extra_fields[${index}][form_field_id]" value="${field.id}">`;
                                }


                                switch (field.type) {
                                    case 'text':
                                        fieldHtml +=
                                            `<input type="text" class="form-control text-fields" id="${field.type}_${index}" placeholder="${field.name}" name="extra_fields[${index}][data]" ${field.is_required ? 'required' : ''}>`;
                                        break;
                                    case 'number':
                                        fieldHtml +=
                                            `<input type="number" min="0" class="form-control number-fields" id="${field.type}_${index}" placeholder="${field.name}" name="extra_fields[${index}][data]" ${field.is_required ? 'required' : ''}>`;
                                        break;
                                    case 'dropdown':
                                        fieldData = JSON.parse(field.default_values);
                                        if (Array.isArray(fieldData)) {
                                            fieldHtml +=
                                                `<select class="form-control select-fields" id="${field.type}_${index}" name="extra_fields[${index}][data]" ${field.is_required ? 'required' : ''}><option value="">Select ${field.name}</option>`;
                                            $.each(fieldData, function(optIndex, optValue) {
                                                fieldHtml +=
                                                    `<option value="${optValue}">${optValue}</option>`;
                                            });
                                            fieldHtml += `</select>`;
                                        } else {
                                            console.error(
                                                'Expected an array for default_values in dropdown'
                                            );
                                        }
                                        break;
                                    case 'radio':
                                        fieldData = JSON.parse(field.default_values);
                                        if (Array.isArray(fieldData)) {
                                            fieldHtml +=
                                                `<fieldset id="${field.type}_${index}">
                                                <legend>${field.name} ${field.is_required ? '<span class="text-danger">*</span>' : ''}</legend>`;
                                            $.each(fieldData, function(optIndex, optValue) {
                                                fieldHtml += `<div class="form-check form-check-inline">
                                                    <input style="width:0%;" class="form-check-input" type="radio" id="${field.type}_${index}_${optIndex}" name="extra_fields[${index}][data]" value="${optValue}" ${field.is_required ? 'required' : ''}>
                                                    <label class="form-check-label" for="${field.type}_${index}_${optIndex}">${optValue}</label>
                                                </div>`;
                                            });
                                            fieldHtml += `</fieldset>`;
                                        }
                                        break;
                                    case 'checkbox':
                                        fieldData = JSON.parse(field.default_values);
                                        if (Array.isArray(fieldData)) {
                                            fieldHtml +=
                                                `<fieldset id="${field.type}_${index}">
                                                <legend>${field.name} ${field.is_required ? '<span class="text-danger">*</span>' : ''}</legend>`;
                                            $.each(fieldData, function(optIndex, optValue) {
                                                fieldHtml += `<div class="form-check">
                                                    <input style="width:0%;" class="form-check-input" type="checkbox" id="${field.type}_${index}_${optIndex}" name="extra_fields[${index}][data][]" value="${optValue}">
                                                    <label class="form-check-label" for="${field.type}_${index}_${optIndex}">${optValue}</label>
                                                </div>`;
                                            });
                                            fieldHtml += `</fieldset>`;
                                        }
                                        break;
                                    case 'textarea':
                                        fieldHtml +=
                                            `<textarea class="form-control text-area-fields" id="${field.type}_${index}" placeholder="${field.name}" name="extra_fields[${index}][data]" ${field.is_required ? 'required' : ''}></textarea>`;
                                        break;
                                    case 'file':
                                        fieldHtml +=
                                            `<input type="file" class="form-control file" id="${field.type}_${index}" name="extra_fields[${index}][data]" ${field.is_required ? 'required' : ''}>`;
                                        break;
                                    case 'fullwidthtitle':
                                        fieldHtml += `<label style="display:block; text-align:center; font-weight:bold;">${field.name}</label><hr>`;
                                        break;
                                    default:
                                        fieldHtml +=
                                            `<input type="text" class="form-control text-fields" id="${field.type}_${index}" placeholder="${field.name}" name="extra_fields[${index}][data]" ${field.is_required ? 'required' : ''}>`;
                                }

                                const formGroup = $('<div>', {
                                    class: field.type != 'fullwidthtitle' ? 'col-lg-6' : 'col-lg-12',
                                    html: fieldHtml
                                });
                                fieldHtml += '</div>';
                                extraFieldsContainer.append(formGroup);
                            });
                        }

                        //Admission No
                        $('#admission_no').val(admissionNo);

                        const dataPath = 'assets/JSON/eInvoice/CountryCodes.json';
                        $.getJSON(dataPath,function(data){
                            $.each(data,function(i,option){
                                $('#countryCodes').append($('<option/>').attr("value", option.Code).text(option.Country));
                            });
                        });    
                        const dataPath2 = 'assets/JSON/eInvoice/StateCodes.json';
                        $.getJSON(dataPath2,function(data){
                            $.each(data,function(i,option){
                                $('#stateCodes').append($('<option/>').attr("value", option.Code).text(option.State));
                            });
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Invalid School Code',
                            text: 'The school ID entered is invalid. Please try again.'
                        });
                    }
                }
            });
        });

        function htmlEntityDecode(str) {
            var txt = document.createElement("textarea");
            txt.innerHTML = str;
            return txt.value;
        }
    </script>
    <script>
        <?php if(Session::has('admissionSuccess')): ?>
            $(document).ready(function() {
                Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: '<?php echo e(Session::get('admissionSuccess')); ?>',
                    showConfirmButton: false,
                    timer: 3000 // Close after 3 seconds
                });
            });
        <?php endif; ?>

        <?php if(session('errorAlert')): ?>
            Swal.fire({
                icon: 'error',
                title: 'Error Occured',
                text: '<?php echo e(session('errorAlert')); ?>',
                confirmButtonText: 'OK'
            });
        <?php endif; ?>
    </script>
    <script>
        $(document).ready(function() {
            $('#attendance').click(async function() {
                const {
                    value: schoolCode
                } = await Swal.fire({
                    title: "Enter your school code",
                    input: "text",
                    inputPlaceholder: "Enter school code here",
                    showCancelButton: true,
                    inputValidator: async (value) => {
                        if (!value) {
                            return "You need to write something!";
                        }

                try {
                    const response = await fetch(`/attendance-schoolsub/${value}`);
                    const result = await response.json();

                    if (!response.ok) {
                        return result.message;
                    }
                    sessionStorage.setItem('subOptions', JSON.stringify(result.options));
                    sessionStorage.setItem('school', JSON.stringify(result.school_id));
                    sessionStorage.setItem('teachers', JSON.stringify(result.teachers));
                    return;
                } catch (error) {
                    return `Request failed: ${error.message}`;
                }
            }
        });

        if (schoolCode) {
            const schoolId = JSON.parse(sessionStorage.getItem('school'));
            const optionsData = JSON.parse(sessionStorage.getItem('subOptions'));
            const teachersData = JSON.parse(sessionStorage.getItem('teachers'));

            // Select between Teacher or Student
            const { value: role } = await Swal.fire({
                title: "Select Role",
                input: "select",
                inputOptions: {
                    teacher: 'Teacher',
                    student: 'Student'
                },
                inputPlaceholder: "Choose a role",
                showCancelButton: true,
                inputValidator: (value) => {
                    if (!value) {
                        return "You need to select a role!";
                    }
                }
            });

            if (role) {
                const subName = Object.values(optionsData).map(option => option.name);
                const subIds = Object.values(optionsData).map(option => option.id);
                subName.unshift("All");
                subIds.unshift("all");

                const { value: selectedOption } = await Swal.fire({
                    title: "Select a subject",
                    input: "select",
                    inputOptions: subName.reduce((obj, name, index) => ({ ...obj, [index]: name }), {}),
                    inputPlaceholder: "Choose a subject",
                    showCancelButton: true,
                    inputValidator: (value) => {
                        if (!value) {
                            return "You need to select a subject!";
                        }
                    }
                });

                if (selectedOption !== undefined) {
                    const selectedSubjectId = subIds[selectedOption];
                    
                    if (role === 'student') {
                        // If role is Student, select a teacher
                        const teacherNames = teachersData.map(teacher => teacher.full_name);
                        const teacherIds = teachersData.map(teacher => teacher.id);

                        const { value: selectedTeacherIndex } = await Swal.fire({
                            title: "Select a teacher",
                            input: "select",
                            inputOptions: teacherNames.reduce((obj, name, index) => ({ ...obj, [index]: name }), {}),
                            inputPlaceholder: "Choose a teacher",
                            showCancelButton: true,
                            inputValidator: (value) => {
                                if (!value) {
                                    return "You need to select a teacher!";
                                }
                            }
                        });

                        if (selectedTeacherIndex !== undefined) {
                            const selectedTeacherId = teacherIds[selectedTeacherIndex];
                            const url = `/attendancetracker/${schoolId}/${selectedSubjectId}/${selectedTeacherId}/${role}`;
                            window.open(url, '_blank');
                        }
                    } else {
                        // If role is Teacher, proceed directly
                        const defaultTeacherId = 0; // Adjust this as necessary
                        const url = `/attendancetracker/${schoolId}/${selectedSubjectId}/${defaultTeacherId}/${role}`;
                        window.open(url, '_blank');
                    }
                }
            }
        }
    });

    $('#attendance_timetable').click(async function() {
        const { value: schoolCode } = await Swal.fire({
            title: "Enter your school code",
            input: "text",
            inputPlaceholder: "Enter school code here",
            showCancelButton: true,
            inputValidator: async (value) => {
                if (!value) {
                    return "You need to write something!";
                }

                try {
                    const response = await fetch(`/attendance-schoolsub/${value}`);
                    const result = await response.json();

                    if (!response.ok) {
                        return result.message;
                    }
                    sessionStorage.setItem('school', JSON.stringify(result.school_id));
                    return;
                } catch (error) {
                    return `Request failed: ${error.message}`;
                }
            }
        });

        if (schoolCode) {
            const schoolId = JSON.parse(sessionStorage.getItem('school'));
            var encrypt = window.btoa(schoolId);
            const url = `<?php echo e(route('attendance-tracker.rfid')); ?>?s=${(encrypt)}`;
            window.open(url, '_blank');
        }
    });
}); 

//reward
$(document).ready(function() {
    $('#reward').click(async function() {
        const { value: schoolCode } = await Swal.fire({
            title: "Enter your school code",
            input: "text",
            inputPlaceholder: "Enter school code here",
            showCancelButton: true,
            inputValidator: async (value) => {
                if (!value) {
                    return "You need to write something!";
                }

                try {
                    const response = await fetch(`/reward-schoolsub/${value}`);
                    const result = await response.json();

                    if (!response.ok) {
                        return result.message;
                    }
                    sessionStorage.setItem('school', JSON.stringify(result.school_id));
                    sessionStorage.setItem('teachers', JSON.stringify(result.teachers));
                    return;
                } catch (error) {
                    return `Request failed: ${error.message}`;
                }
            }
        });

        if (schoolCode) {
            const schoolId = JSON.parse(sessionStorage.getItem('school'));
            const teachersData = JSON.parse(sessionStorage.getItem('teachers'));
            
            
            const teacherNames = teachersData.map(teacher => teacher.full_name);
            const teacherIds = teachersData.map(teacher => teacher.id);


            const { value: selectedTeacherIndex } = await Swal.fire({
                title: "Select a teacher",
                input: "select",
                inputOptions: teacherNames.reduce((obj, name, index) => ({ ...obj, [index]: name }), {}),
                inputPlaceholder: "Choose a teacher",
                showCancelButton: true,
                inputValidator: (value) => {
                    if (!value) {
                        return "You need to select a teacher!";
                    }
                }
            });

            if (selectedTeacherIndex !== undefined) {

                    const selectedTeacherId = teacherIds[selectedTeacherIndex];
                    const url = `/rewardpoint/${schoolId}/${selectedTeacherId}`;
                    window.open(url, '_blank');
                

            }
        }
        
    });
}); 
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.home_page.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laragon\www\schola\resources\views/home.blade.php ENDPATH**/ ?>