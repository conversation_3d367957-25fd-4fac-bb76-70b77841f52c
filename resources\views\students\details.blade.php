@extends('layouts.master')

@section('title')
    {{ __('students') }}
@endsection

@section('content')
    <div class="content-wrapper">
        <div class="page-header">
            <h3 class="page-title">
                {{ __('manage') . ' ' . __('students') }}
            </h3>
        </div>

        <div class="row">
            <div class="col-lg-12 grid-margin stretch-card">
                <div class="card">
                    <div class="card-body">
                        <h4 class="card-title">
                            {{ __('list') . ' ' . __('students') }}
                        </h4>
                        <div style="color:red;">{{__('student_detail_note')}}</div>
                        <div class="row" id="toolbar">
                            <div class="form-group col-sm-12 col-md-4">
                                <label class="filter-menu">{{ __('Class Section') }} <span class="text-danger">*</span></label>
                                <select name="filter_class_section_id" id="filter_class_section_id" class="form-control">
                                    <option value="">{{ __('select_class_section') }}</option>
                                    @foreach ($class_sections as $class_section)
                                        <option value={{ $class_section->id }}>{{$class_section->full_name}}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="form-group col-sm-12 col-md-4">
                                <label class="filter-menu">{{ __('Session Year') }} <span class="text-danger">*</span></label>
                                <select name="filter_session_year_id" id="filter_session_year_id" class="form-control">
                                    @foreach ($sessionYears as $sessionYear)
                                        <option value={{ $sessionYear->id }} {{$sessionYear->default==1?"selected":""}}>{{$sessionYear->name}}</option>
                                    @endforeach
                                </select>
                            </div>
                            @can('student-delete')
                                <div class="form-group col-12">
                                    <button id="update-status" class="btn btn-success" disabled><span class="update-status-btn-name">{{ __('Inactive') }}</span></button>
                                </div>
                            @endcan
                        </div>

                        @can('student-delete')
                            <div class="col-12 mt-4 text-right">
                                <b><a href="#" class="table-list-type active mr-2" data-id="0">{{__('active')}}</a></b> | <a href="#" class="ml-2 table-list-type" data-id="1">{{__("Inactive")}}</a> | <a href="#" class="ml-2 table-list-type" data-id="2">{{__("leave_school")}}</a>
                            </div>
                        @endcan
                        <div class="row">
                            <div class="col-12">
                                <table aria-describedby="mydesc" class='table' id='table_list'
                                       data-toggle="table" data-url="{{ route('students.show',[1]) }}" data-click-to-select="true"
                                       data-side-pagination="server" data-pagination="true"
                                       data-page-list="[5, 10, 20, 50, 100, 200, All]" data-search="true" data-toolbar="#toolbar"
                                       data-toolbar="#toolbar" data-show-columns="true" data-show-refresh="true" data-fixed-columns="false"
                                       data-trim-on-search="false" data-mobile-responsive="true" data-sort-name="id"
                                       data-sort-order="desc" data-maintain-selected="true" data-export-types="['pdf','json', 'xml', 'csv', 'txt', 'sql', 'doc', 'excel']" data-show-export="true"
                                       data-export-options='{ "fileName": "students-list-<?= date('d-m-y') ?>" ,"ignoreColumn": ["operate"]}' data-query-params="studentDetailsQueryParams"
                                       data-check-on-init="true" data-escape="true">
                                    <thead>
                                    <tr>
                                        <th data-field="state" data-checkbox="true"></th>
                                        <th scope="col" data-field="id" data-sortable="true" data-visible="false">{{ __('id') }}</th>
                                        <th scope="col" data-field="no">{{ __('no.') }}</th>
                                        <th scope="col" data-field="user.id" data-visible="false">{{ __('User Id') }}</th>
                                        <th scope="col" data-field="user.full_name" data-formatter="nameFormatter">{{ __('name') }}</th>
                                        <th scope="col" data-field="user.mobile">{{ __('mobile') }}</th>
                                        <th scope="col" data-field="ic_no_2">{{ __('ic_no') }}</th>
                                        <th scope="col" data-field="user.dob" data-formatter="dateFormatter">{{ __('dob') }}</th>
                                        <th scope="col" data-field="user.image" data-formatter="imageFormatter">{{ __('image') }}</th>
                                        <th scope="col" data-field="user.rfid_id">{{ __('RFID') }}</th>
                                        <th scope="col" data-field="class_section.full_name">{{ __('class_section') }}</th>
                                        <th scope="col" data-field="admission_no"data-sortable="true"> {{ __('Gr Number') }}</th>
                                        <th scope="col" data-field="roll_number">{{ __('roll_no') }}</th>
                                        <th scope="col" data-field="user.gender">{{ __('gender') }}</th>
                                        <th scope="col" data-field="admission_date" data-formatter="dateFormatter">{{ __('admission_date') }}</th>
                                        <th scope="col" data-field="guardian.email">{{ __('guardian') . ' ' . __('email') }}</th>
                                        <th scope="col" data-field="guardian.full_name">{{ __('guardian') . ' ' . __('name') }}</th>
                                        <th scope="col" data-field="guardian.mobile">{{ __('guardian') . ' ' . __('mobile') }}</th>
                                        <th scope="col" data-field="guardian.gender">{{ __('guardian') . ' ' . __('gender') }}</th>

                                        {{-- Admission form fields --}}
                                        @foreach ($extraFields as $field)
                                            <th scope="col" data-sortable="true" data-visible="false" data-escape="false" data-field="{{ $field->name }}">{{ $field->name }}</th>
                                        @endforeach
                                        {{-- End admission form fields --}}
                                        @canany(['student-edit','student-delete'])
                                            <th data-events="studentEvents" class="" scope="col" data-field="operate" data-escape="false">{{ __('action') }}</th>
                                        @endcanany
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @can('student-edit')
        <div class="modal fade" id="editModal" data-backdrop="static" tabindex="-1" role="dialog"
             aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title" id="exampleModalLabel">{{ __('edit') . ' ' . __('students') }}</h4><br>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true"><i class="fa fa-close"></i></span>
                        </button>
                    </div>
                    <form id="edit-form" class="edit-student-registration-form" novalidate="novalidate" action="{{ url('students') }}" enctype="multipart/form-data">
                        @csrf
                        <div class="modal-body">
                            <div class="row">
                                <div class="form-group col-sm-12 col-md-12 col-lg-6 col-xl-4">
                                    <label>{{ __('admission_no') }} <span class="text-danger">*</span></label>
                                    {!! Form::text('admission_no', null, ['placeholder' => __('admission_no'), 'class' => 'form-control', 'id' => 'edit_admission_no' ,'readonly'=>true]) !!}

                                </div>

                                <div class="form-group col-sm-12 col-md-12 col-lg-6 col-xl-4">
                                    <label>{{ __('Session Year') }} <span class="text-danger">*</span></label>
                                    <select required name="session_year_id" class="form-control" id="session_year_id">
                                        @foreach ($sessionYears as $sessionYear)
                                            <option value="{{ $sessionYear->id }}">{{$sessionYear->name}}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="form-group col-sm-12 col-md-12 col-lg-6 col-xl-4">
                                    <label>{{ __('Class Section') }} <span class="text-danger">*</span></label>
                                    <select required name="class_section_id" class="form-control" id="edit_student_class_section_id">
                                        <option value="">{{ __('select_class_section') }}</option>
                                        @foreach ($class_sections as $class_section)
                                            <option value={{ $class_section->id }}>{{$class_section->full_name}}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <hr>
                            <div class="row mt-5">
                                <div class="form-group col-sm-12 col-md-12 col-lg-6 col-xl-4">
                                    <label>{{ __('first_name') }} <span class="text-danger">*</span></label>
                                    {!! Form::text('first_name', null, ['placeholder' => __('first_name'), 'class' => 'form-control', 'id' => 'edit_first_name']) !!}
                                </div>

                                <div class="form-group col-sm-12 col-md-12 col-lg-6 col-xl-4">
                                    <label>{{ __('last_name') }} <span class="text-danger">*</span></label>
                                    {!! Form::text('last_name', null, ['placeholder' => __('last_name'), 'class' => 'form-control', 'id' => 'edit_last_name']) !!}
                                </div>

                                <div class="form-group col-sm-12 col-md-12 col-lg-6 col-xl-4">
                                    <label>{{ __('dob') }} <span class="text-danger">*</span></label>
                                    {!! Form::text('dob', null, ['placeholder' => __('dob'), 'class' => 'datepicker-popup-no-future form-control', 'id' => 'edit_dob']) !!}
                                    <span class="input-group-addon input-group-append">
                                    </span>
                                </div>

                                <div class="form-group col-sm-12 col-md-4">
                                    <label>{{ __('gender') }} <span class="text-danger">*</span></label><br>
                                    <div class="d-flex">
                                        <div class="form-check form-check-inline">
                                            <label class="form-check-label">
                                                {!! Form::radio('gender', 'male', false ,['id' => 'male']) !!}
                                                {{ __('male') }}
                                            </label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <label class="form-check-label">
                                                {!! Form::radio('gender', 'female', false , ['id' => 'female']) !!}
                                                {{ __('female') }}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group col-sm-12 col-md-12 col-lg-6 col-xl-4">
                                    <label>{{ __('image') }} </label>
                                    <input type="file" name="image" class="file-upload-default"/>
                                    <div class="input-group col-xs-12">
                                        <input type="text" class="form-control file-upload-info" disabled="" placeholder="{{ __('image') }}" required="required" id="edit_image"/>
                                        <span class="input-group-append">
                                            <button class="file-upload-browse btn btn-theme" type="button">{{ __('upload') }}</button>
                                        </span>
                                    </div>
                                    <div style="width: 100px;">
                                        <img src="" id="edit-student-image-tag" class="img-fluid w-100" alt=""/>
                                    </div>
                                </div>

                                <div class="form-group col-sm-12 col-md-12 col-lg-6 col-xl-4">
                                <label>{{ __('mobile') }} <span class="text-danger"></span></label>
                                {!! Form::number('mobile', null, ['placeholder' => __('mobile'), 'min' => 1 , 'class' => 'form-control remove-number-increment', 'id' => 'edit_mobile']) !!}
                                </div>

                                <div class="form-group col-sm-12 col-md-12 col-lg-6 col-xl-4">
                                    <label>{{ __('ic_no') }} <span class="text-danger"></span></label>
                                    {!! Form::text('ic_no_2', null, ['placeholder' => __('ic_no'), 'class' => 'form-control', 'id' => 'edit_ic_no_2']) !!}
                                </div>
                                

                                <div class="row">
                                <div class="form-group col-sm-12 col-md-6">
                                    <label>{{ __('current_address') }} <span class="text-danger">*</span></label>
                                    {!! Form::textarea('current_address', null, ['required', 'placeholder' => __('current_address'), 'class' => 'form-control', 'rows' => 3,'id'=>'edit-current-address']) !!}
                                </div>
                                <div class="form-group col-sm-12 col-md-6">
                                    <label>{{ __('permanent_address') }} <span class="text-danger"></span></label>
                                    {!! Form::textarea('permanent_address', null, ['placeholder' => __('permanent_address'), 'class' => 'form-control', 'rows' => 3,'id'=>'edit-permanent-address']) !!}
                                </div>
                            </div>


                            @if(!empty($extraFields))
                                <div class="row other-details">

                                    {{-- Loop the FormData --}}
                                    @foreach ($extraFields as $key => $data)
                                        @php $fieldName = str_replace(' ', '_', $data->name) @endphp
                                        {{-- Edit Extra Details ID --}}
                                        {{ Form::hidden('extra_fields['.$key.'][id]', '', ['id' => $fieldName.'_id','class' => 'extra_field_id']) }}

                                        {{-- Form Field ID --}}
                                        {{ Form::hidden('extra_fields['.$key.'][form_field_id]', $data->id) }}

                                        {{-- FormFieldType --}}
                                        {{ Form::hidden('extra_fields['.$key.'][input_type]', $data->type) }}

                                        <div class='form-group col-md-12 col-lg-6 col-xl-4 col-sm-12'>

                                            {{-- Add lable to all the elements excluding checkbox --}}
                                            @if($data->type != 'radio' && $data->type != 'checkbox')
                                                <label>{{$data->name}} @if($data->is_required)
                                                        <span class="text-danger">*</span>
                                                    @endif</label>
                                            @endif

                                            {{-- Text Field --}}
                                            @if($data->type == 'text')
                                                {{ Form::text('extra_fields['.$key.'][data]', '', ['class' => 'form-control text-fields', 'id' => $fieldName, 'placeholder' => $data->name, ($data->is_required == 1 ? 'required' : '')]) }}
                                                {{-- Number Field --}}
                                            @elseif($data->type == 'number')
                                                {{ Form::number('extra_fields['.$key.'][data]', '', ['min' => 0, 'class' => 'form-control number-fields', 'id' => $fieldName, 'placeholder' => $data->name, ($data->is_required == 1 ? 'required' : '')]) }}

                                                {{-- Dropdown Field --}}
                                            @elseif($data->type == 'dropdown')
                                                {{ Form::select(
                                                    'extra_fields['.$key.'][data]',$data->default_values,
                                                    null,
                                                    [
                                                        'id' => $fieldName,
                                                        'class' => 'form-control select-fields',
                                                        ($data->is_required == 1 ? 'required' : ''),
                                                        'placeholder' => 'Select '.$data->name
                                                    ]
                                                )}}

                                                {{-- Radio Field --}}
                                            @elseif($data->type == 'radio')
                                                <label class="d-block">{{$data->name}} @if($data->is_required)
                                                        <span class="text-danger">*</span>
                                                    @endif</label>
                                                <div class="row form-check-inline ml-1">
                                                    @foreach ($data->default_values as $keyRadio => $value)
                                                        <div class="col-md-12 col-lg-12 col-xl-6 col-sm-12 form-check">
                                                            <label class="form-check-label">
                                                                {{ Form::radio('extra_fields['.$key.'][data]', $value, null, ['id' => $fieldName.'_'.$keyRadio, 'class' => 'radio-fields',($data->is_required == 1 ? 'required' : '')]) }}
                                                                {{$value}}
                                                            </label>
                                                        </div>
                                                    @endforeach
                                                </div>

                                                {{-- Checkbox Field --}}
                                            @elseif($data->type == 'checkbox')
                                                <label class="d-block">{{$data->name}} @if($data->is_required)
                                                        <span class="text-danger">*</span>
                                                    @endif</label>
                                                <div class="row form-check-inline ml-1">
                                                    @foreach ($data->default_values as $chkKey => $value)
                                                        <div class="col-lg-12 col-xl-6 col-md-12 col-sm-12 form-check">
                                                            <label class="form-check-label">
                                                                {{ Form::checkbox('extra_fields['.$key.'][data][]', $value, null, ['id' => $fieldName.'_'.$chkKey, 'class' => 'form-check-input chkclass checkbox-fields',($data->is_required == 1 ? 'required' : '')]) }} {{ $value }}
                                                            </label>
                                                        </div>
                                                    @endforeach
                                                </div>

                                                {{-- Textarea Field --}}
                                            @elseif($data->type == 'textarea')
                                                {{ Form::textarea('extra_fields['.$key.'][data]', '', ['placeholder' => $data->name, 'id' => $fieldName, 'class' => 'form-control textarea-fields', ($data->is_required ? 'required' : '') , 'rows' => 3]) }}

                                                {{-- File Upload Field --}}
                                            @elseif($data->type == 'file')
                                                <div class="input-group col-xs-12">
                                                    {{ Form::file('extra_fields['.$key.'][data]', ['class' => 'file-upload-default', 'id' => $fieldName,($data->is_required == 1 ? '' : '')]) }}
                                                    {{ Form::text('', '', ['class' => 'form-control file-upload-info', 'disabled' => '', 'placeholder' => __('image')]) }}
                                                    <span class="input-group-append">
                                                        <button class="file-upload-browse btn btn-theme" type="button">{{ __('upload') }}</button>
                                                    </span>
                                                </div>
                                                <div id="file_div_{{$fieldName}}" class="mt-2 d-none file-div">
                                                    <a href="" id="file_link_{{$fieldName}}" target="_blank">{{ __('click_here_to_view_file') }}</a>
                                                    <input type="hidden" class="file_exist" name="extra_fields[{{$key}}][file_exists]" id="file_exist_{{$fieldName}}" value="false">
                                                </div>
                                            @endif
                                        </div>
                                    @endforeach
                                        <div class="form-group col-md-12 col-lg-6 col-xl-4 col-sm-12">
                                            <label>{{ __('rfid_id') }} <span class="text-danger"></span></label>
                                            {!! Form::text('rfid_id', null, ['placeholder' => __('rfid_id'), 'class' => 'form-control', 'id' => 'edit_rfid_id']) !!}
                                        </div>
                                        <div class="form-group col-sm-12 col-md-12 col-lg-6 col-xl-4">
                                            <label>{{ __('admission_date') }} <span class="text-danger">*</span></label>
                                            {!! Form::text('admission_date', null, ['placeholder' => __('admission_date'), 'class' => 'datepicker-popup form-control', 'id' => 'edit_admission_date']) !!}
                                            <span class="input-group-addon input-group-append">
                                            </span>
                                        </div>
                                </div>
                            @endif

                            <div class="row">
                        <div class="form-group col-sm-12 col-md-6">
                            <div class="d-flex justify-content-center">
                                <div class="form-check w-fit-content">
                                    <label class="form-check-label ml-4">
                                        <input type="checkbox" class="form-check-input" name="reset_password" value="1">
                                        {{ __('reset_password') }}
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group col-sm-12 col-md-6 mt-4 mt-md-0">
                            <div class="d-flex justify-content-center">
                                <div class="form-check w-fit-content">
                                    <label class="form-check-label ml-4">
                                        <input type="checkbox" 
                                            class="form-check-input" 
                                            name="skip_package_credit" 
                                            id="skip_package_credit"
                                            value="1">
                                        {{ __('Skip Package Credit') }}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>


                            

                            <hr>
                            {{-- Guardian Details --}}
                            <div class="row mt-5">
                                <div class="form-group col-sm-12 col-md-12">
                                    <label>{{ __('guardian') . ' ' . __('email') }} <span class="text-danger">*</span></label>
                                    <select class="edit-guardian-search form-control" name="guardian_id"></select>
                                    <input type="hidden" id="edit_guardian_email" name="guardian_email">
                                </div>

                                <div class="form-group col-sm-12 col-md-12 col-lg-6 col-xl-4">
                                    <label>{{ __('guardian') . ' ' . __('first_name') }} <span class="text-danger">*</span></label>
                                    {!! Form::text('guardian_first_name', null, ['placeholder' => __('guardian') . ' ' . __('first_name'), 'class' => 'form-control', 'id' => 'edit_guardian_first_name']) !!}
                                </div>

                                <div class="form-group col-sm-12 col-md-12 col-lg-6 col-xl-4">
                                    <label>{{ __('guardian') . ' ' . __('last_name') }} <span class="text-danger">*</span></label>
                                    {!! Form::text('guardian_last_name', null, ['placeholder' => __('guardian') . ' ' . __('last_name'), 'class' => 'form-control', 'id' => 'edit_guardian_last_name']) !!}
                                </div>
                                <div class="form-group col-sm-12 col-md-12 col-lg-6 col-xl-4">
                                    <label>{{ __('guardian') . ' ' . __('mobile') }} <span class="text-danger">*</span></label>
                                    {!! Form::number('guardian_mobile', null, ['placeholder' => __('guardian') . ' ' . __('mobile'), 'class' => 'form-control remove-number-increment', 'min' => 1  ,'id' => 'edit_guardian_mobile']) !!}
                                </div>
                                <div class="form-group col-sm-12 col-md-12">
                                    <label>{{ __('gender') }} <span class="text-danger">*</span></label><br>
                                    <div class="d-flex">
                                        <div class="form-check form-check-inline">
                                            <label class="form-check-label">
                                                {!! Form::radio('guardian_gender', 'male', false , ['id' =>"edit-guardian-male"]) !!}
                                                {{-- <input type="radio" name="guardian_gender" value="male" id="edit_guardian_male"  disabled> --}}
                                                {{ __('male') }}
                                            </label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <label class="form-check-label">
                                                {!! Form::radio('guardian_gender', 'female', false , ['id' =>"edit-guardian-female"]) !!}
                                                {{-- <input type="radio" name="guardian_gender" value="female" id="edit_guardian_female" disabled> --}}
                                                {{ __('female') }}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group col-sm-12 col-md-12 col-lg-6 col-xl-4">
                                    <label>{{ __('image') }} </label>
                                    <input type="file" name="guardian_image" class="file-upload-default"/>
                                    <div class="input-group col-xs-12">
                                        <input type="text" class="form-control file-upload-info" disabled="" placeholder="{{ __('image') }}" required="required" id="edit_image"/>
                                        <span class="input-group-append">
                                            <button class="file-upload-browse btn btn-theme" type="button">{{ __('upload') }}</button>
                                        </span>
                                    </div>
                                    <div style="width: 100px;">
                                        <img src="" id="edit-guardian-image-tag" class="img-fluid w-100" alt=""/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('Cancel') }}</button>
                            <input class="btn btn-theme" type="submit" value="{{ __('submit') }}"></input>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endcan
@endsection
@section('script')
    <script>
        let userIds;
        setTimeout(function(){
            addScrollableTable();
        },1000);
        $('.table-list-type').on('click', function (e) {
            let value = $(this).data('id');
            let ActiveLang = window.trans['Active'];
            let DeactiveLang = window.trans['Inactive'];
            console.log(value);
            if (value === "" || value === "active" || value == 0) {
                $("#update-status").data("id");
                $('#update-status').show();
                $('.update-status-btn-name').html(DeactiveLang);
            } else if (value == 2){
                $('#update-status').hide();
            }else {
                $('#update-status').show();
                $('.update-status-btn-name').html(ActiveLang);
            }
        })


        function updateUserStatus(tableId, buttonClass) {
            let selectedRows = $(tableId).bootstrapTable('getSelections');
            let selectedRowsValues = selectedRows.map(function (row) {
                return row.user_id;
            });
            userIds = JSON.stringify(selectedRowsValues);

            if (buttonClass != null) {
                if (selectedRowsValues.length) {
                    $(buttonClass).prop('disabled', false);
                } else {
                    $(buttonClass).prop('disabled', true);
                }
            }
        }

        function nameFormatter(value, row, index) {
            let iconHtml = '';

            if (row.e_invoice_status == true) {
                iconHtml = '<i class="fa fa-check-circle" style="color: green; font-size:20px;" title="E-invoice completed"></i>';
            } else {
                iconHtml = '<i class="fa fa-exclamation-circle" style="color: orange; font-size:20px;" title="Pending e-invoice data"></i>';
            }
            return `${value} ${iconHtml}`; 
        }

        var tableList = $('#table_list');
        tableList.on('check.bs.table', function (e, row) {
            updateUserStatus("#table_list", '#update-status');
        });

        tableList.on('uncheck.bs.table', function (e, row) {
            updateUserStatus("#table_list", '#update-status');
        });

        tableList.on('check-all.bs.table', function (e, rows) {
            updateUserStatus("#table_list", '#update-status');
        });

        tableList.on('uncheck-all.bs.table', function (e, rows) {
            updateUserStatus("#table_list", '#update-status');
        });

        $("#update-status").on('click', function (e) {
            Swal.fire({
                title: window.trans["Are you sure"],
                text: window.trans["Change Status For Selected Users"],
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: window.trans["Yes, Change it"],
                cancelButtonText: window.trans["Cancel"]
            }).then((result) => {
                if (result.isConfirmed) {
                    let url = baseUrl + '/students/change-status-bulk';
                    let data = new FormData();
                    data.append("ids", userIds)

                    function successCallback(response) {
                        $('#table_list').bootstrapTable('refresh');
                        $('#update-status').prop('disabled', true);
                        userIds = null;
                        console.log(response);
                        if(response.status == "warning"){
                            const nameArray = response.message.split(',');
                            const formattedNames = nameArray.map(name => `- ${name.trim()}`).join('<br>');
                            Swal.fire({
                                icon: 'warning',
                                title: 'Warning!',
                                html: 'Class Section cannot be empty.<br>' + 'Student Failed to update:<br>' + formattedNames,
                                text: response.message,
                                showConfirmButton: true,
                            });
                        } else {
                            showSuccessToast(response.message);
                        }
                    }

                    function errorCallback(response) {
                        showErrorToast(response.message);
                    }

                    ajaxRequest('POST', url, data, null, successCallback, errorCallback);
                }
            })
        })

        window.studentEvents = {
            'click .edit-data': function (e, value, row) {
                // $('#editModal input').val(''); // Clear text, email, and other input fields
                $('#editModal input[type="text"]').val(''); // Clear text, email, and other input fields
                $('#editModal input[type="hidden"].extra_field_id').val(''); 
                $('#editModal textarea').val('');        // Clear textarea fields
                $('#editModal input[type="checkbox"]').prop('checked', false);  // Uncheck checkboxes
                $('#editModal input[type="radio"]').prop('checked', false);     // Uncheck radio buttons
                $('#editModal select').prop('selectedIndex', 0);
                $('#editModal .file-div').addClass('d-none');
                $('#editModal .file_exist').val('false');

                $('#edit_first_name').val(row.user.first_name);
                $('#edit_last_name').val(row.user.last_name);
                $('#edit_mobile').val(row.user.mobile);
                $('#edit_dob').val(moment(row.user.dob, 'YYYY-MM-DD').format('DD-MM-YYYY'));
                $('#session_year_id').val(row.session_year_id);
                if(row.class_section){
                    $('#edit_student_class_section_id').val(row.class_section.id);
                }
                $('#edit_admission_no').val(row.admission_no);
                $('#edit-student-image-tag').attr('src', row.user.image);
                $('#edit-current-address').val(row.user.current_address);
                $('#edit-permanent-address').val(row.user.permanent_address);
                $('#edit_rfid_id').val(row.user.rfid_id);
                $('#edit_admission_date').val(moment(row.admission_date, 'YYYY-MM-DD').format('DD-MM-YYYY'));
                $('#edit_ic_no_2').val(row.ic_no_2);
                $('#skip_package_credit').prop('checked', row.credit_status == 1);

                if (row.user.gender == 'Male') {
                    $(document).find('#female').prop('checked', false);
                    $(document).find('#male').prop('checked', true);
                } else {
                    $(document).find('#male').prop('checked', false);
                    $(document).find('#female').prop('checked', true);
                }

                setTimeout(() => {

                    // Fill the Extra Field's Data
                    if (row.extra_fields.length) {
                        // console.log(row.extra_fields);
                        $.each(row.extra_fields, function (index, value) {

                            let fieldName = $.escapeSelector(value.form_field.name.replace(/ /g, '_'));

                            $(`#${fieldName}_id`).val(value.id);

                            if (value.form_field.default_values && value.form_field.default_values.length) {
                                $.each(value.form_field.default_values, function (key) {

                                    if (typeof (value.data) == 'object') {
                                        $.each(value.data, function (dataKey, dataValue) {
                                            let checked = ($('#' + fieldName + '_' + dataKey).val() == dataValue);
                                            $('#' + fieldName + '_' + dataKey).prop('checked', checked);
                                        });
                                    } else if (value.form_field.type == 'dropdown') {
                                        for(var i = 0; i < value.form_field.default_values.length; i++){
                                            if(value.form_field.default_values[i] == value.data){
                                                $('#' + fieldName).val(i);
                                            }else if(i == value.data){
                                                $('#' + fieldName).val(i);
                                            }
                                        }
                                    } else if (value.form_field.type == 'radio') {
                                        let checked = ($('#' + fieldName + '_' + key).val() == value.data);
                                        $('#' + fieldName + '_' + key).prop('checked', checked);
                                    } else {
                                        $('#' + fieldName + '_' + key).prop('checked', false);
                                        // Check data is json format or not
                                        if (isJSON(value.data)) { // Checkbox
                                            let chkArray = JSON.parse(value.data);
                                            $.each(chkArray, function (chkKey, chkValue) {
                                                if ($('#' + fieldName + '_' + key).val() == chkValue) {
                                                    $('#' + fieldName + '_' + key).prop('checked', true);
                                                }
                                            })
                                        }
                                    }
                                });
                            } else {
                                // console.log(value.form_field.type);
                                if (value.form_field.type == 'file') {
                                    if (value.data) {
                                        $('#file_div_' + fieldName).removeClass('d-none').find('#file_link_' + fieldName).attr('href', value.file_url);
                                        $('#'+fieldName).prop('required', false);
                                        $('#file_exist_' + fieldName).val("true");
                                    } else {
                                        $('#file_div_' + fieldName).addClass("d-none").find('#file_link_' + fieldName).attr('href', "");
                                    }
                                } else {
                                    $('#' + fieldName).val(value.data);
                                }
                            }
                        });
                    } else {
                        $('.text-fields').val('');
                        $('.number-fields').val('');
                        $('.select-fields').val('');
                        $('.radio-fields').prop('checked', false);
                        $('.checkbox-fields').prop('checked', false);
                        $('.textarea-fields').val('');
                        $('.file-div').addClass('d-none');
                    }
                }, 1000);

                function isJSON(data) {
                    try {
                        JSON.parse(data);
                        return true;
                    } catch (error) {
                        return false;
                    }
                }

                // Guardian Data
                $(".edit-guardian-search").select2("trigger", "select", {
                    data: {
                        id: row.guardian_id || "",
                        text: row.guardian.email || "",
                        edit_data: true,
                    }
                });

                //Adding delay to fill data so that select2 code and this code don't conflict each other
                setTimeout(function () {
                    $('#edit_guardian_first_name').val(row.guardian.first_name);
                    $('#edit_guardian_last_name').val(row.guardian.last_name);
                    $('#edit_guardian_mobile').val(row.guardian.mobile);
                    $('#edit-guardian-image-tag').attr('src', row.guardian.image);

                }, 500);
                if (row.guardian.gender == 'Male') {
                    $(document).find('#edit-guardian-female').prop('checked', false);
                    $(document).find('#edit-guardian-male').prop('checked', true);
                } else {
                    $(document).find('#edit-guardian-male').prop('checked', false);
                    $(document).find('#edit-guardian-female').prop('checked', true);
                }
            }, 'click .deactivate-student': function (e) {
                e.preventDefault();
                showDeletePopupModal($(e.currentTarget).attr('href'), {
                    text: window.trans["You want to inactive the Student"],
                    confirmButtonText: window.trans["Yes inactive"],
                    cancelButtonText: window.trans["Cancel"],
                    icon: 'question',
                    successCallBack: function () {
                        $('#table_list').bootstrapTable('refresh');
                    }
                })
            }, 'click .activate-student': function (e, value, row, index) { // Add index parameter
                e.preventDefault();
                var studentName = row.user.full_name; // Access the name directly from the row data
                console.log('Student Name:', studentName);

                // Extract first and last names
                var firstName = row.user.first_name;
                var lastName = row.user.last_name;

                $.ajax({
                    url: '{{ route('check-duplicate-student') }}',
                    type: 'GET',
                    data: {
                        first_name: firstName,
                        last_name: lastName,
                        check_active_only: true // Add this line to check for active students only
                    },
                    success: function(response) {
                        if (response.duplicate) {
                            // Show warning message, but proceed
                            Swal.fire({
                                icon: 'warning',
                                title: 'Warning!',
                                text: 'Duplicate student found. Proceeding with activation.',
                                confirmButtonText: 'OK'
                            }).then(() => {
                                // Proceed with the current logic (same as the else block)
                                showDeletePopupModal($(e.currentTarget).attr('href'), {
                                    text: window.trans["You want to Activate the Student"],
                                    confirmButtonText: window.trans["Yes Activate"],
                                    cancelButtonText: window.trans["Cancel"],
                                    icon: 'question',
                                    successCallBack: function () {
                                        $('#table_list').bootstrapTable('refresh');
                                    }
                                });
                            });
                        } else {
                            // Proceed with the current logic
                            showDeletePopupModal($(e.currentTarget).attr('href'), {
                                text: window.trans["You want to Activate the Student"],
                                confirmButtonText: window.trans["Yes Activate"],
                                cancelButtonText: window.trans["Cancel"],
                                icon: 'question',
                                successCallBack: function () {
                                    $('#table_list').bootstrapTable('refresh');
                                }
                            });
                        }
                    },
                    error: function(xhr) {
                        console.error('Error checking for duplicate student:', xhr.responseText);
                        // Optionally, show an error message to the user
                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: 'An error occurred while checking for duplicate students.',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }
        };

        $(document).on('click', '.validate-einvoice', function(e) {
            e.preventDefault();
            const icNo = $(this).data('ic-no');
            const tin = $(this).data('tin');

            console.log('Button attributes:', {
                'data-ic-no': $(this).data('ic-no'),
                'data-tin': $(this).data('tin'),
                'full-data': $(this).data()
            });

            if (!icNo || !tin) {
                showErrorToast('Guardian IC and TIN are required');
                return;
            }
            
            $.ajax({
                url: "{{ route('students.validate-tin') }}",
                type: "POST",
                data: {
                    id_type: 'NRIC',
                    id_value: icNo,
                    tin: tin,
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    console.log('Success:', response);
                    if (response.success) {
                        showSuccessToast(response.message);
                        $validation == 1;
                        tableList.bootstrapTable('refresh');
                    } else {
                        showErrorToast(response.message);
                    }
                },
                error: function(xhr) {
                    console.error('Error:', xhr.responseJSON);
                    $validation == 0;
                    showErrorToast(xhr.responseJSON.message);
                }
            });
        });

        // First, get your extra field names
        const extraFields = @json($extraFields->pluck('name'));
        let isCustomSorted = false;
        let customSortedData = null;

        // Initialize bootstrap-table
        $('#table_list').bootstrapTable({
            sortName: 'id',
            serverSort: false,
            sidePagination: 'server',
            pagination: true,
            pageSize: 10,
            onSort: function(name, order) {
                if (!extraFields.includes(name)) {
                    isCustomSorted = false;
                    customSortedData = null;
                    return;
                }
                
                isCustomSorted = true;
                
                // Get all data from server first
                $.ajax({
                    url: $(this).bootstrapTable('getOptions').url,
                    method: 'GET',
                    data: {
                        limit: -1, // Get all records
                        sort: name,
                        order: order
                    },
                    success: (response) => {
                        // Custom sorting for extra fields
                        let data = response.rows || response;
                        data.sort((a, b) => {
                            let aVal = String(a[name] || '').toLowerCase();
                            let bVal = String(b[name] || '').toLowerCase();
                            
                            if (order === 'desc') {
                                return bVal.localeCompare(aVal);
                            }
                            return aVal.localeCompare(bVal);
                        });
                        
                        customSortedData = data;
                        
                        // Update table with paginated portion of sorted data
                        let pageSize = $(this).bootstrapTable('getOptions').pageSize;
                        let pageNumber = $(this).bootstrapTable('getOptions').pageNumber;
                        let startIndex = (pageNumber - 1) * pageSize;
                        let endIndex = startIndex + pageSize;
                        
                        $(this).bootstrapTable('load', {
                            total: data.length,
                            rows: data.slice(startIndex, endIndex)
                        });
                    }
                });
                
                return false;
            },
            onPageChange: function(page, size) {
                // Kill all sorting and remove sort indicators
                isCustomSorted = false;
                customSortedData = null;
                //$(this).bootstrapTable('unsort');
                $(this).bootstrapTable('refreshOptions', {
                    sortName: '',
                    sortOrder: ''
                });
                // Force remove sort indicators from column headers
                $(this).find('th').removeClass('asc desc').find('.sortable').removeClass('asc desc');
            }
        });
    </script>
@endsection
