<?php

namespace App\Http\Controllers\Api;

use App\Helpers\EInvoiceHelper;
use DOMDocument;
use DOMElement;
use DOMXPath;

use DB;
use PDF;
use Throwable;
use Google\Client;
use App\Models\Chat;
use App\Models\User;
use App\Models\School;
use App\Models\Feature;
use App\Models\Students;
use Illuminate\Support\Str;
use App\Models\ClassTeacher;
use Illuminate\Http\Request;
use App\Models\SchoolSetting;
use App\Models\SystemSetting;
use App\Models\SubjectTeacher;
use Illuminate\Support\Carbon;
use App\Services\CachingService;
use App\Models\UserNotifications;
use App\Services\ResponseService;
use Spatie\Permission\Models\Role;
use App\Http\Controllers\Controller;
use GuzzleHttp\Client as HttpClient;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Cache;
use Berkayk\OneSignal\OneSignalClient;
use Illuminate\Support\Facades\Artisan;
use Stripe\Exception\ApiErrorException;
use App\Repositories\Chat\ChatInterface;
use App\Repositories\User\UserInterface;
use App\Services\GeneralFunctionService;
use App\Services\Payment\PaymentService;
use Illuminate\Support\Facades\Password;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Validator;
use App\Repositories\Files\FilesInterface;
use App\Repositories\Leave\LeaveInterface;
use App\Repositories\Grades\GradesInterface;
use App\Repositories\Medium\MediumInterface;
use App\Repositories\Gallery\GalleryInterface;
use App\Repositories\Holiday\HolidayInterface;
use App\Repositories\Message\MessageInterface;
use App\Repositories\Student\StudentInterface;
use App\Repositories\Attachment\AttachmentInterface;

use App\Repositories\ExamResult\ExamResultInterface;
use App\Repositories\LeaveDetail\LeaveDetailInterface;
use App\Repositories\LeaveMaster\LeaveMasterInterface;
use App\Repositories\SessionYear\SessionYearInterface;
use App\Repositories\StudentFees\StudentFeesInterface;
use App\Repositories\ClassSection\ClassSectionInterface;
use App\Repositories\SchoolSetting\SchoolSettingInterface;
use App\Repositories\PaymentTransaction\PaymentTransactionInterface;
use App\Repositories\PaymentConfiguration\PaymentConfigurationInterface;
use App\Models\StudentFee;
use App\Models\StudentFeesDetail;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Psr7\Response;
use \Datetime;
use Klsheng\Myinvois\MyInvoisClient;
use Klsheng\Myinvois\Helper\MyInvoisHelper;
use Klsheng\Myinvois\Example\Ubl\CreateDocumentExample;
use Klsheng\Myinvois\Ubl\Constant\InvoiceTypeCodes;

class ApiController extends Controller
{
    private CachingService $cache;
    private HolidayInterface $holiday;
    private StudentInterface $student;
    private PaymentConfigurationInterface $paymentConfiguration;
    private PaymentTransactionInterface $paymentTransaction;
    private GalleryInterface $gallery;
    private SessionYearInterface $sessionYear;
    private LeaveDetailInterface $leaveDetail;
    private LeaveMasterInterface $leaveMaster;
    private LeaveInterface $leave;
    private UserInterface $user;
    private MediumInterface $medium;
    private ClassSectionInterface $classSection;
    private FilesInterface $files;
    private ExamResultInterface $examResult;
    private GradesInterface $grade;
    private ChatInterface $chat;
    private MessageInterface $message;
    private AttachmentInterface $attachment;
    private StudentFeesInterface $studentFees;

    public function __construct(CachingService $cache, HolidayInterface $holiday, StudentInterface $student, PaymentConfigurationInterface $paymentConfiguration, PaymentTransactionInterface $paymentTransaction, GalleryInterface $gallery, SessionYearInterface $sessionYear, LeaveDetailInterface $leaveDetail, LeaveMasterInterface $leaveMaster, LeaveInterface $leave, UserInterface $user, MediumInterface $medium, ClassSectionInterface $classSection, ExamResultInterface $examResult, GradesInterface $grade, FilesInterface $files, ChatInterface $chat, MessageInterface $message, AttachmentInterface $attachment,StudentFeesInterface $studentFees)
    {
        $this->cache = $cache;
        $this->holiday = $holiday;
        $this->student = $student;
        $this->paymentConfiguration = $paymentConfiguration;
        $this->paymentTransaction = $paymentTransaction;
        $this->gallery = $gallery;
        $this->sessionYear = $sessionYear;
        $this->leaveDetail = $leaveDetail;
        $this->leaveMaster = $leaveMaster;
        $this->leave = $leave;
        $this->user = $user;
        $this->medium = $medium;
        $this->classSection = $classSection;
        $this->files = $files;
        $this->examResult = $examResult;
        $this->grade = $grade;
        $this->chat = $chat;
        $this->message = $message;
        $this->attachment = $attachment;
        $this->studentFees = $studentFees;
    }

    public function logout(Request $request)
    {
        try {
            $user = $request->user();
            $user->fcm_id = '';
            $user->save();
            $user->currentAccessToken()->delete();
            ResponseService::successResponse('Logout Successfully done');
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getHolidays(Request $request)
    {
        try {
            // $query->whereDate('date', '>=',$sessionYear->start_date)
            //     ->whereDate('date', '<=',$sessionYear->end_date);

            $sessionYear = $this->cache->getDefaultSessionYear();
            if ($request->child_id) {
                $child = $this->student->findById($request->child_id);
                $data = $this->holiday->builder()->where('school_id', $child->user->school_id);
            } else {
                $data = $this->holiday->builder();
            }

            $today = now()->format('Y-m-d');

            $data = $data->whereDate('date', '>=', $today) // Only future holidays
                ->whereDate('date', '>=', $sessionYear->start_date)
                ->whereDate('date', '<=', $sessionYear->end_date)
                ->orderBy('date', 'asc') // Sort ascending (next holiday first)
                ->get();

           
            ResponseService::successResponse("Holidays Fetched Successfully", $data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getSettings(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|in:privacy_policy,contact_us,about_us,terms_condition,app_settings,fees_settings'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $systemSettings = $this->cache->getSystemSettings();
            if ($request->type == "app_settings") {
                $data = array(
                    'app_link'                 => $systemSettings['app_link'] ?? "",
                    'ios_app_link'             => $systemSettings['ios_app_link'] ?? "",
                    'app_version'              => $systemSettings['app_version'] ?? "",
                    'ios_app_version'          => $systemSettings['ios_app_version'] ?? "",
                    'force_app_update'         => $systemSettings['force_app_update'] ?? "",
                    'app_maintenance'          => $systemSettings['app_maintenance'] ?? "",
                    'teacher_app_link'         => $systemSettings['teacher_app_link'] ?? "",
                    'teacher_ios_app_link'     => $systemSettings['teacher_ios_app_link'] ?? "",
                    'teacher_app_version'      => $systemSettings['teacher_app_version'] ?? "",
                    'teacher_ios_app_version'  => $systemSettings['teacher_ios_app_version'] ?? "",
                    'teacher_force_app_update' => $systemSettings['teacher_force_app_update'] ?? "",
                    'teacher_app_maintenance'  => $systemSettings['teacher_app_maintenance'] ?? "",
                    'tagline'                  => $systemSettings['tag_line'] ?? "",
                );

                if(isset($request->package_name)){
                    if($request->package_name == 'one.schola.aecparent'){
                        $data['app_link'] = 'https://play.google.com/store/apps/details?id=one.schola.aecparent';
                        $data['ios_app_link'] = 'https://apps.apple.com/us/app/aec-parent/id6736944389';
                    }
                }
            } else {
                $data = isset($systemSettings[$request->type]) ? htmlspecialchars_decode($systemSettings[$request->type]) : "";
            }
            ResponseService::successResponse("Data Fetched Successfully", $data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    protected function forgotPassword(Request $request)
    {
        $request->validate([
            'email' => "required|email"
        ]);
        try {
            $response = Password::sendResetLink(['email' => $request->email]);
            if ($response == Password::RESET_LINK_SENT) {
                ResponseService::successResponse("Forgot Password email send successfully");
            } else {
                ResponseService::errorResponse("Cannot send Reset Password Link.Try again later", null, config('constants.RESPONSE_CODE.RESET_PASSWORD_FAILED'));
            }
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    protected function changePassword(Request $request)
    {
        $request->validate([
            'current_password'     => 'required',
            'new_password'         => 'required|min:6',
            'new_confirm_password' => 'same:new_password',
        ]);

        try {
            $user = $request->user();
            if (Hash::check($request->current_password, $user->password)) {
                $user->update(['password' => Hash::make($request->new_password)]);
                ResponseService::successResponse("Password Changed successfully.");
            } else {
                ResponseService::errorResponse("Invalid Password", null, config('constants.RESPONSE_CODE.INVALID_PASSWORD'));
            }
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getPaymentMethod(Request $request)
    {
        if (Auth::user()->hasRole('Guardian')) {
            $validator = Validator::make($request->all(), [
                'child_id' => 'required|numeric',
            ]);
            if ($validator->fails()) {
                ResponseService::validationError($validator->errors()->first());
            }
        }
        try {

            $response = $this->paymentConfiguration->builder()->select('payment_method', 'status')->pluck('status', 'payment_method');
            ResponseService::successResponse("Payment Details Fetched", $response);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getPaymentConfirmation(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $paymentTransaction = app(PaymentTransactionInterface::class)->builder()->where('id', $request->id)->first();
            if (empty($paymentTransaction)) {
                ResponseService::errorResponse("No Data Found");
            }
            $data = PaymentService::create($paymentTransaction->payment_gateway, $paymentTransaction->school_id)->retrievePaymentIntent($paymentTransaction->order_id);

            $data = PaymentService::formatPaymentIntent($paymentTransaction->payment_gateway, $data);

            // Success
            ResponseService::successResponse("Payment Details Fetched", $data, ['payment_transaction' => $paymentTransaction]);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getPaymentTransactions(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'latest_only' => 'nullable|boolean'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {/*
            $paymentTransactions = app(PaymentTransactionInterface::class)->builder();
            if ($request->latest_only) {
                $paymentTransactions->where('created_at', '>', Carbon::now()->subMinutes(30)->toDateTimeString());
            }
            $paymentTransactions = $paymentTransactions->with('school')->orderBy('id', 'DESC')->get();
            $schoolSettings = app(SchoolSettingInterface::class)->builder()
                ->where(function ($q) {
                    $q->where('name', 'currency_code')->orWhere('name', 'currency_symbol');
                })->whereIn('school_id', $paymentTransactions->pluck('school_id'))->get();

            $paymentTransactions = collect($paymentTransactions)->map(function ($data) use ($schoolSettings) {
                $getSchoolSettings = $schoolSettings->filter(function ($settings) use ($data) {
                    return $settings->school_id == $data->school_id;
                })->pluck('data', 'name');
                $data->currency_code = $getSchoolSettings['currency_code'] ?? '';
                $data->currency_symbol = $getSchoolSettings['currency_symbol'] ?? '';
                if ($data->payment_status == "pending") {
                    try {
                        if ($data->order_id) {
                            $paymentIntent = PaymentService::create($data->payment_gateway, $data->school_id)->retrievePaymentIntent($data->order_id);
                            $paymentIntent = PaymentService::formatPaymentIntent($data->payment_gateway, $paymentIntent);    
                        }
                    } catch (ApiErrorException) {
                        $this->paymentTransaction->update($data->id, ['payment_status' => "failed", 'school_id' => $data->school_id]);
                    }

                    if (!empty($paymentIntent) && $paymentIntent['status'] != "pending") {
                        $this->paymentTransaction->update($data->id, ['payment_status' => $paymentIntent['status'] ?? "failed", 'school_id' => $data->school_id]);
                    }
                }
                return $data;
            });
            */
            ResponseService::successResponse("Payment Transactions Fetched", []);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getGallery(Request $request)
    {
        $statement = '';
        try {
            if ($request->gallery_id) {
                $data = $this->gallery->builder()->with('file')->where('id', $request->gallery_id)->orderBy('id', 'DESC')->first();
            } else {
                if ($request->child_id) {
                    //For parent retrieve child gallery
                    $statement = $request->session_year_id ?
                    ("SELECT sub.subject_id AS class_subject_id, sec.section_id AS class_section_id FROM class_sections sec, class_subjects sub WHERE sec.class_id = sub.class_id AND sec.id = (SELECT class_section_id FROM students WHERE id = ".$request->child_id." AND session_year_id = ".$request->session_year_id.")") :
                    ("SELECT sub.subject_id AS class_subject_id, sec.section_id AS class_section_id FROM class_sections sec, class_subjects sub WHERE sec.class_id = sub.class_id AND  sec.id = (SELECT class_section_id FROM students WHERE id = ".$request->child_id.")");

                    $child = $this->student->findById($request->child_id);
                    $data = $this->gallery->builder()->with('file')->where('school_id', $child->user->school_id)->orderBy('updated_at', 'DESC');
                    if ($request->session_year_id) {
                        $data = $data->where('session_year_id',$request->session_year_id);
                    }
                    $data = $data->get();
                } else {
                    //For child retrieve own gallery
                    $statement = $request->session_year_id ?
                    ("SELECT sub.subject_id AS class_subject_id, sec.section_id AS class_section_id FROM class_sections sec, class_subjects sub WHERE sec.class_id = sub.class_id AND sec.id = (SELECT class_section_id FROM students WHERE user_id = ". Auth::user()->id." AND session_year_id = ".$request->session_year_id.")") :
                    ("SELECT sub.subject_id AS class_subject_id, sec.section_id AS class_section_id FROM class_sections sec, class_subjects sub WHERE sec.class_id = sub.class_id AND  sec.id = (SELECT class_section_id FROM students WHERE user_id = ". Auth::user()->id.")");

                    if ($request->session_year_id) {
                        $data = $this->gallery->builder()->with('file')->where('session_year_id',$request->session_year_id)->orderBy('id', 'DESC');
                    } else {
                        $data = $this->gallery->builder()->with('file')->orderBy('id', 'DESC');
                    }
                    $data = $data->get();
                }

                $studentSubjects = DB::select($statement);
                $tempData = array();
                for($i = 0; $i < COUNT($data); $i++){
                    // if(isset($data[$i]->class_subject_id) && isset($data[$i]->class_section_id)){
                    //     if($data[$i]->class_subject_id == 0 && $data[$i]->class_section_id == 0){
                    //         $tempData[] = $data[$i];
                    //     }
                    //     else{
                    $isAdded = false;

                            if(isset($data[$i]->student_tag)){
                                if($data[$i]->student_tag != ''){
                                    $studentIds = explode(',', $data[$i]->student_tag);
                                    if($request->child_id){
                                        if (in_array($request->child_id, $studentIds)){
                                            $tempData[] = $data[$i];
                                            $isAdded = true;
                                        }
                                    }
                                    else{
                                        $student = DB::select('SELECT user_id FROM students WHERE id = '. ($request->child_id ? $request->child_id : Auth::user()->id));
                                        if(COUNT($student)){
                                            if (in_array($student[0]->user_id, $studentIds)){
                                                $tempData[] = $data[$i];
                                                $isAdded = true;
                                            }
                                        }
                                    }
                                    continue;
                                }
                                else{
                                   // $tempData[] = $data[$i];
                                }
                            }
                            
                            if(!$isAdded){
                                if(isset($data[$i]->class_subject_id) && isset($data[$i]->class_section_id)){
                                    if($data[$i]->class_subject_id == 0 && $data[$i]->class_section_id == 0){
                                        $tempData[] = $data[$i];
                                    }
                                    else{
                                        $studentsData = DB::select('SELECT * FROM `students` WHERE id = '.($request->child_id ? $request->child_id : Auth::user()->id));
                                        $classSectionId = '';
                                        if(COUNT($studentsData)){
                                            $classSectionId = $studentsData[0]->class_section_id;
                                        }
                                        if($data[$i]->class_section_id == $classSectionId){
                                            $tempData[] = $data[$i];
                                        }
                                    }
                                }
                            }
                                
                            // for($j = 0; $j < COUNT($studentSubjects); $j++){
                            //     if($data[$i]->class_subject_id == $studentSubjects[$j]->class_subject_id && $data[$i]->class_section_id == $studentSubjects[$j]->class_section_id){
                            //         $tempData[] = $data[$i];
                            //     }
                            // }
                    //     }
                    // }
                    // else{
                    //     $tempData[] = $data[$i];
                    // }
                }
                //if(COUNT($tempData) > 0){
                //    $data = $tempData;
                //}
            }
            ResponseService::successResponse("Gallery Fetched Successfully", $tempData);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getSessionYear(Request $request)
    {
        try {
            if ($request->child_id) {
                $child = $this->student->findById($request->child_id);
                $data = $this->sessionYear->builder()->where('school_id', $child->user->school_id)->get();
            } else {
                $data = $this->sessionYear->builder()->get();
            }
            ResponseService::successResponse("Session Year Fetched Successfully", $data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getLeaves(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Staff Leave Management');
        try {
            $leave = $this->leaveDetail->builder()->with([
                'leave' => function($query) {
                    $query->select('id', 'user_id', 'leave_category_id')
                        ->with(['leave_category' => function($q) {
                            $q->select('id', 'name')
                                ->where('school_id', Auth::user()->school_id);
                        }]);
                },
                'leave.user:id,first_name,last_name,image',
                'leave.user.roles'
            ])
            ->whereHas('leave', function ($q) {
                $q->where('status', 1);
            });
            
            // $leave = $this->leaveDetail->builder()->with('leave:id,user_id,leave_category_id', 'leave.user:id,first_name,last_name,image', 'leave.user.roles')
            //     ->whereHas('leave', function ($q) {
            //         $q->where('status', 1);
            //     });
            if ($request->type == 0 || $request->type == null) {
                $leave->whereDate('date', '<=', Carbon::now()->format('Y-m-d'))->whereDate('date', '>=', Carbon::now()->format('Y-m-d'));
            }
            if ($request->type == 1) {
                $tomorrow_date = Carbon::now()->addDay()->format('Y-m-d');
                $leave->whereDate('date', '<=', $tomorrow_date)->whereDate('date', '>=', $tomorrow_date);
            }
            if ($request->type == 2) {
                $upcoming_date = Carbon::now()->addDays(1)->format('Y-m-d');
                $leave->whereDate('date', '>', $upcoming_date);
            }
            $leave = $leave->orderBy('date', 'ASC')->get()->append(['leave_date']);
            ResponseService::successResponse("Data Fetched Successfully", $leave);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function applyLeaves(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'leave_category_id' => 'required',
            'reason'  => 'required',
            'files.*' => 'nullable',
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            DB::beginTransaction();
            $dates = array_column($request->leave_details, 'date');
            $from_date = min($dates);
            $to_date = max($dates);

            $staffId = DB::table('staffs')->where('user_id',Auth::user()->id)->pluck('id')->first();
            $schoolId = Auth::user()->school_id;
            $sessionYearId = DB::table('session_years')
                ->where('school_id', $schoolId)
                ->where('start_date', '<=', $from_date)
                ->where('end_date', '>=', $to_date)
                ->value('id');
            if (!$sessionYearId) {
                return ResponseService::errorResponse('No valid academic session found for the selected dates.');
            }
            $leave_master_id = DB::table('leave_masters')->where('staff_id',$staffId)->where('leave_category_id',$request->leave_category_id)->where('session_year_id', $sessionYearId)->pluck('id')->first();

            // Add year validation
            // if (Carbon::parse($from_date)->year !== Carbon::parse($to_date)->year) {
            //     return ResponseService::errorResponse('Please apply leave within the same year. Cross-year leave applications are not allowed.');
            // }

            $leaveMaster = DB::table('leave_masters')
                ->where('leave_category_id', $request->leave_category_id)
                // ->where('session_year_id', $sessionYearId)
                ->where('staff_id', $staffId)
                ->first();
            if (!$leaveMaster) {
                return ResponseService::errorResponse('No leave master record found for the selected leave category.');
            }

            $totalRequestLeaves = 0;
            foreach ($request->leave_details as $leave) {
                if ($leave['type'] === 'Full') {
                    $totalRequestLeaves += 1;
                } elseif ($leave['type'] === 'First Half' || $leave['type'] === 'Second Half') {
                    $totalRequestLeaves += 0.5; 
                }
            }

             // Calculate total leaves taken
            $totalLeaves = DB::table('leave_details')
                ->join('leaves', 'leaves.id', '=', 'leave_details.leave_id')
                ->join('leave_masters', 'leaves.leave_master_id', '=', 'leave_masters.id')
                ->where('leave_details.school_id', $schoolId)
                ->where('leave_masters.staff_id', $staffId)
                ->where('leave_masters.session_year_id', $sessionYearId)
                ->where('leave_masters.leave_category_id', $request->leave_category_id)
                ->select(DB::raw("SUM(CASE WHEN leave_details.type = 'full' THEN 1 ELSE 0.5 END) AS total_leaves_taken"))
                ->value('total_leaves_taken');

            // Calculate total leaves left
            $totalLeavesLeft = $leaveMaster->leaves - $totalLeaves;

            if ($totalRequestLeaves > $totalLeavesLeft) {
                return ResponseService::errorResponse('You have ' . $totalLeavesLeft . ' leave days only.');
            }




            $sessionStartDate = DB::table('session_years')
                ->where('school_id', $schoolId)
                ->where('start_date', '<=', $from_date)
                ->where('end_date', '>=', $to_date)
                ->value('start_date');
            $sessionEndDate = DB::table('session_years')
                ->where('school_id', $schoolId)
                ->where('start_date', '<=', $from_date)
                ->where('end_date', '>=', $to_date)
                ->value('end_date');
            $public_holiday = $this->holiday->builder()->whereDate('date', '>=', $sessionStartDate)->whereDate('date', '<=', $sessionEndDate)->get()->pluck('date')->toArray();


            $leave_data = [
                'user_id' => Auth::user()->id,
                'reason' => $request->reason,
                'from_date' => $from_date,
                'to_date' => $to_date,
                'status' => 0,
                'leave_category_id' => $request->leave_category_id,
                'leave_master_id' => $leaveMaster->id
            ];

            $holidays = explode(',', $leaveMaster->holiday);

            $leave = $this->leave->create($leave_data);
            foreach ($request->leave_details as $key => $leaves) {
                $day = date('l', strtotime($leaves['date']));
                if (!in_array($day, $holidays) && !in_array($leaves['date'], $public_holiday)) {
                    $data[] = [
                        'leave_id' => $leave->id,
                        'date' => $leaves['date'],
                        'type' => $leaves['type']
                    ];
                }
            }
            if ($request->hasFile('files')) {
                $fileData = []; // Empty FileData Array
                // Create A File Model Instance
                $leaveModelAssociate = $this->files->model()->modal()->associate($leave); // Get the Association Values of File with Assignment
            
                foreach ($request->file('files') as $file_upload) {
                    // Create Temp File Data Array
                    $tempFileData = [
                        'modal_type' => $leaveModelAssociate->modal_type,
                        'modal_id'   => $leaveModelAssociate->modal_id,
                        'file_name'  => $file_upload->getClientOriginalName(),
                        'type'       => 1,
                        'file_url'   => $file_upload->store('files', 'public') // Store file and get the file path
                    ];
                    $fileData[] = $tempFileData; // Store Temp File Data in Multi-Dimensional File Data Array
                }
                $this->files->createBulk($fileData); // Store File Data
            }

            $this->leaveDetail->createBulk($data);

            $user = $this->user->builder()->whereHas('roles.permissions', function ($q) {
                $q->where('name', 'approve-leave');
            })->pluck('id');

            $type = "Leave";
            $title = Auth::user()->full_name . ' has submitted a new leave request.';
            $body = $request->reason;
            send_notification($user, $title, $body, $type);


            $data = [
                'user_id' => Auth::user()->id,
                'school_id' => Auth::user()->school_id, 
                'date' => now(), 
                'status' => 0, 
                'type' => 5,
            ];
        
            DB::table('admission_notification')->insert($data);
        

            DB::commit();
            ResponseService::successResponse("Data Stored Successfully");
        } catch (Throwable $e) {
            if (Str::contains($e->getMessage(), [
                'does not exist','file_get_contents'
            ])) {
                DB::commit();
                ResponseService::warningResponse("Data Stored successfully. But App push notification not send.");
            } else {
                ResponseService::logErrorResponse($e);
                ResponseService::errorResponse();
            }
        }
    }

    public function getMyLeaves(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Staff Leave Management');
        $validator = Validator::make($request->all(), [
            'month'  => 'in:1,2,3,4,5,6,7,8,9,10,11,12',
            'status' => 'in:0,1,2'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $sessionYear = $this->cache->getDefaultSessionYear();
            $leaveMaster = $this->leaveMaster->builder();
            $sql = $this->leave->builder()->with('leave_detail')->where('user_id', Auth::user()->id)->withCount(['leave_detail as full_leave' => function ($q) {
                $q->where('type', 'Full');
            }])->withCount(['leave_detail as half_leave' => function ($q) {
                $q->whereNot('type', 'Full');
            }]);

            if ($request->session_year_id) {
                $sql->whereHas('leave_master', function ($q) use ($request) {
                    $q->where('session_year_id', $request->session_year_id);
                });
                $leaveMaster->where('session_year_id', $request->session_year_id);
            } else {
                $sql->whereHas('leave_master', function ($q) use ($sessionYear) {
                    $q->where('session_year_id', $sessionYear->id);
                });
                $leaveMaster->where('session_year_id', $sessionYear->id);
            }
            if (isset($request->status)) {
                $sql->where('status', $request->status);
            }
            if ($request->month) {
                $sql->whereHas('leave_detail', function ($q) use ($request) {
                    $q->whereMonth('date', $request->month);
                });
            }
            $leaveMaster = $leaveMaster->first();
            $sql = $sql->get();
            $sql = $sql->map(function ($sql) {
                $total_leaves = ($sql->half_leave / 2) + $sql->full_leave;
                $sql->days = $total_leaves;
                return $sql;
            });
            $data = [
                'monthly_allowed_leaves' => $leaveMaster->leaves,
                'taken_leaves' => $sql->sum('days'),
                'leave_details' => $sql
            ];

            ResponseService::successResponse("Data Fetched Successfully", $data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function deleteLeaves(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Staff Leave Management');
        $validator = Validator::make($request->all(), [
            'leave_id'  => 'required',
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            DB::beginTransaction();
            $leave = $this->leave->findById($request->leave_id);
            if ($leave->status != 0) {
                ResponseService::errorResponse("You cannot delete this leave");
            }
            $this->leave->deleteById($request->leave_id);
            DB::commit();
            ResponseService::successResponse("Data Deleted Successfully");
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getStaffLeaveDetail(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Staff Leave Management');
        $validator = Validator::make($request->all(), [
            'staff_id' => 'required',
            'month'  => 'in:1,2,3,4,5,6,7,8,9,10,11,12',
            'status' => 'in:0,1,2'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $staff = DB::table('staffs')->where('user_id',$request->staff_id)->first();
            $sessionYear = $this->cache->getDefaultSessionYear();
            $leaveMaster = $this->leaveMaster->builder();
            if($request->leave_category_id){
                $leaveMaster->where('leave_category_id',$request->leave_category_id)
                            ->where('staff_id',$staff->id);
            }
            $sql = $this->leave->builder()->with('leave_detail','file')->withCount(['leave_detail as full_leave' => function ($q) {
                $q->where('type', 'Full');
            }])->withCount(['leave_detail as half_leave' => function ($q) {
                $q->whereNot('type', 'Full');
            }])->where('user_id', $request->staff_id)->where('leave_category_id',$request->leave_category_id);

            if ($request->session_year_id) {
                $sql->whereHas('leave_master', function ($q) use ($request) {
                    $q->where('session_year_id', $request->session_year_id);
                });
                $leaveMaster->where('session_year_id', $request->session_year_id);
            } else {
                $sql->whereHas('leave_master', function ($q) use ($sessionYear) {
                    $q->where('session_year_id', $sessionYear->id);
                });
                $leaveMaster->where('session_year_id', $sessionYear->id);
            }
            if (isset($request->status)) {
                $sql->where('status', $request->status);
            }
            if ($request->month) {
                $sql->whereHas('leave_detail', function ($q) use ($request) {
                    $q->whereMonth('date', $request->month);
                });
            }
            $leaveMaster = $leaveMaster->first();
            if (!$leaveMaster) {
                ResponseService::errorResponse("Leave settings not found");
            }
            $sql = $sql->get();
            $sql = $sql->map(function ($sql) {
                $total_leaves = ($sql->half_leave / 2) + $sql->full_leave;
                $sql->days = $total_leaves;
                if ($sql->status == 1) {
                    $sql->taken_leaves = $total_leaves;
                }
                return $sql;
            });

            $leaveDetails = DB::table('leave_details as ld')
                            ->join('leaves as l','l.id','=','ld.leave_id')
                            ->where('l.leave_master_id',$leaveMaster->id)
                            ->where('l.status','=',1)
                            ->select('ld.*')
                            ->get();
            $takenLeaves = 0;
            foreach($leaveDetails as $l){
                if($l->type == 'Full'){
                    $takenLeaves += 1;
                } else {
                    $takenLeaves += 0.5;
                }
            }
            $data = [
                'monthly_allowed_leaves' => $leaveMaster->leaves,
                'total_leaves' => $sql->sum('days'),
                'taken_leaves' => (double) $takenLeaves,
                'leave_details' => $sql
            ];

            ResponseService::successResponse("Data Fetched Successfully", $data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getMedium()
    {
        try {
            $sql = $this->medium->builder()->get();
            ResponseService::successResponse("Data Fetched Successfully", $sql);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getClass(Request $request)
    {
        try {
            $currentSemester = $this->cache->getDefaultSemesterData();
          

            // Teacher
            if (Auth::user()->hasRole('Teacher')) {
                $user = $request->user()->teacher;
                // //Find the class in which teacher is assigns as Class Teacher
                $class_section_ids = [];
                if ($user->class_teacher) {
                 
                    $class_teacher = $user->class_teacher()->whereHas('class_section', function ($query) {
                        $query->whereNull('deleted_at')->whereHas('class', function ($q) {
                            $q->whereNull('deleted_at');
                        });
                    })->get();
                    $class_section_ids = $class_teacher->pluck('class_section_id')->toArray();
                   
                }
                // //Find the Classes in which teacher is taking subjects
                $class_section = $this->classSection->builder()->with('class.stream', 'section', 'medium')->whereNotIn('id', $class_section_ids);
                $class_section = $class_section->whereHas('subject_teachers.class_subject', function ($q) use ($currentSemester) {
                    (!empty($currentSemester)) ? $q->where('semester_id', $currentSemester->id)->orWhereNull('semester_id') : $q->orWhereNull('semester_id');
                })->get();
            } else {
                // Staff
                $class_section = $this->classSection->builder()->with('class', 'section', 'medium', 'class.stream')->get();
            }
            ResponseService::successResponse('Data Fetched Successfully', null, [
                'class_teacher' => $class_teacher ?? [],
                'other'         => $class_section,
                'semester'      => $currentSemester ?? null
            ]);
        } catch (\Throwable $th) {
            ResponseService::logErrorResponse($th);
            ResponseService::errorResponse();
        }
    }

    public function getSubjectsAttendance(Request $request)
    {
        try {
            // Teacher
            if (Auth::user()->hasRole('Teacher')) {
                $user = $request->user()->teacher;
                $currentSchoolId = Auth::user()->school_id;
                // $subject_id = DB::table('subjects as s')
                // ->distinct()
                // ->select('s.id', 's.name')
                // ->leftJoin('subject_students as ss', 'ss.subject_id', '=', 's.id')
                // ->leftJoin('class_subjects as cs', 'cs.subject_id', '=', 's.id')
                // ->where('s.school_id', $currentSchoolId)
                // ->whereNull('s.deleted_at')
                // ->get();

                $subject_id = DB::table('subject_teachers as st')
                    ->join('subjects as s', 'st.subject_id', '=', 's.id')
                    ->where('st.teacher_id', Auth::user()->id)
                    ->whereNull('s.deleted_at')
                    ->select('s.id', 's.name')
                    ->distinct()
                    ->get();

                // $classSections = $this->classSection->builder()->ClassTeacher()->with('class', 'class.stream', 'section', 'medium')->get();
                $classSections = $this->classSection->all(['*'], ['class', 'class.stream', 'section', 'medium']);
                $classSectionIds = [];
                foreach($classSections as $class){
                    $data = [
                        'id' => $class->id,
                        'name' => $class->full_name
                    ];
                    $classSectionIds[] = $data;
                }
            } 
            ResponseService::successResponse('Data Fetched Successfully', null, [
                'subjects'         => $subject_id,
                'classSections'    => $classSectionIds
            ]);
        } catch (\Throwable $th) {
            ResponseService::logErrorResponse($th);
            ResponseService::errorResponse();
        }
    }

    public function updateProfile(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'first_name' => 'required',
            'last_name' => 'required',
            'mobile' => 'required',
            'email' => 'required',
            'dob' => 'required',
            'current_address' => 'required',
            'permanent_address' => 'required',
            'gender' => 'required|in:male,female',
            'image'           => 'nullable|mimes:jpeg,png,jpg,svg|max:5120',
        ]);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $user_data = [
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'mobile' => $request->mobile,
                'email' => $request->email,
                'dob' => $request->dob,
                'current_address' => $request->current_address,
                'permanent_address' => $request->permanent_address,
                'gender' => $request->gender,
            ];

            if ($request->hasFile('image')) {
                $user_data['image'] = $request->file('image');
            }

            $user = $this->user->update(Auth::user()->id, $user_data);

            ResponseService::successResponse('Data Updated Successfully', $user);
        } catch (\Throwable $th) {
            ResponseService::logErrorResponse($th);
            ResponseService::errorResponse();
        }
    }

    public function sendPush($token, Request $request){
        $FcmTokens = User::where('fcm_id', '=', $token)->get()->pluck('fcm_id');
        $cache = app(CachingService::class);

        $project_id = $cache->getSystemSettings('firebase_project_id');
        $url = 'https://fcm.googleapis.com/v1/projects/' . $project_id . '/messages:send';

        $access_token = getAccessToken();
        $title = "This is a push title";
        $body = "This is a push body";
        $type = "Announcement";
        $customData = [];
        foreach ($FcmTokens as $FcmToken) {

            $data = [
                "message" => [
                    "token" => $FcmToken,
                    "notification" => [
                        "title" => $title,
                        "body" => $body
                    ],
                    "data" => [
                        "title" => $title,
                        "body" => $body,
                        "type" => $type,
                        ...$customData
                    ],
                    "android" => [
                        "notification"=> [
                            'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
                        ],
                    
                    ],
                    "apns" => [
                        "headers" => [
                            "apns-priority" => "10" // Set APNs priority to 10 (high) for immediate delivery
                        ],
                        "payload" => [
                            "aps" => [
                                "alert" => [
                                    "title" => $title,
                                    "body" => $body,
                                ],
                                "type" => $type,
                                ...$customData
                            ]
                        ]
                    ]
                ]
            ];

            $encodedData = json_encode($data);
        
            $headers = [
                'Authorization: Bearer ' . $access_token,
                'Content-Type: application/json',
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);

            // Disabling SSL Certificate support temporarly
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $encodedData);

            // Execute post
            $result = curl_exec($ch);
            // dd($result);
            if ($result == FALSE) {
                die('Curl failed: ' . curl_error($ch));
            }
            // Close connection
            curl_close($ch);
        }    
        ResponseService::successResponse('Push sent successfully', null, [
            'status' => 'true',
            'token' => $token
        ]);
    }

    public function getExamResultPdf(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Exam Management');
        try {
            $validator = Validator::make($request->all(), [
                'exam_id'  => 'required',
                'student_id' => 'required',
            ]);
            if ($validator->fails()) {
                ResponseService::validationError($validator->errors()->first());
            }

            $exam_id = $request->exam_id;
            $student_id = $request->student_id;

            $result = $this->examResult->builder()->with(['exam', 'session_year', 'user' => function ($q) use ($exam_id) {
                $q->with(['student' => function ($q) {
                    $q->with('guardian', 'class_section.class.stream', 'class_section.section', 'class_section.medium');
                }])
                    ->with(['exam_marks' => function ($q) use ($exam_id) {
                        $q->whereHas('timetable', function ($q) use ($exam_id) {
                            $q->where('exam_id', $exam_id);
                        })->with(['class_subject' => function ($q) {
                            $q->withTrashed()->with('subject:id,name,type');
                        }])
                            ->with('timetable');
                    }]);
            }])->where('exam_id', $exam_id)
                ->select('*', DB::raw('DENSE_RANK() OVER (PARTITION BY class_section_id ORDER BY percentage DESC) as ranking'))
                ->get()->where('student_id', $student_id)->first();

            if (!$result) {
                return redirect()->back()->with('error', trans('no_records_found'));
            }

            $grades = $this->grade->builder()->orderBy('starting_range', 'ASC')->get();

            $settings = $this->cache->getSchoolSettings('*',$result->school_id);
            $data = explode("storage/", $settings['horizontal_logo'] ?? '');
            $settings['horizontal_logo'] = end($data);

            $pdf = PDF::loadView('exams.exam_result_pdf', compact('result', 'settings', 'grades'))->output();

            return $response = array(
                'error' => false,
                'pdf'   => base64_encode($pdf),
            );
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getLeaveCategory(Request $request){
        $staffId = DB::table('staffs')->where('user_id',Auth::user()->id)->pluck('id')->first();
        $leaveCategory = DB::table('leave_category')
            ->join('leave_masters','leave_masters.leave_category_id','=','leave_category.id')
            ->where('leave_masters.staff_id',$staffId)
            ->where('leave_category.school_id',Auth::user()->school_id)
            ->whereNull('leave_category.deleted_at')
            ->select('leave_category.id','leave_category.name')
            ->distinct()
            ->get();
        ResponseService::successResponse("Data Fetched Successfully", $leaveCategory);
    }

    public function leaveSettings(Request $request)
    {
        try {

            if ($request->session_year_id) {
                $session_year_id = $request->session_year_id;
            } else {
                $sessionYear = $this->cache->getDefaultSessionYear();
                $session_year_id = $sessionYear->id;
            }
            $sql = $this->leaveMaster->builder()->where('session_year_id',$session_year_id)->get();
            ResponseService::successResponse("Data Fetched Successfully", $sql);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }
    
    public function updateOneSignalPlayerId(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'onesignal_player_id' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => true,
                'message' => $validator->errors()->first(),
            ], 400);
        }

        try {
            $user = Auth::user();
            $user->onesignal_player_id = $request->onesignal_player_id;
            $user->save();

            return response()->json([
                'error' => false,
                'message' => 'OneSignal Player ID updated successfully',
            ], 200);
        } catch (\Throwable $e) {
            return response()->json([
                'error' => true,
                'message' => 'Failed to update OneSignal Player ID',
            ], 500);
        }
    }

    public function sendOneSignalNotification(Request $request){
        $validator = Validator::make($request->all(), [
            'onesignal_player_id' => 'required|string',
            'message' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => true,
                'message' => $validator->errors()->first(),
            ], 400);
        }
        // try {
        //     // Send OneSignal push notification
        //     $oneSignal = new OneSignalClient(
        //         env('ONESIGNAL_APP_ID'),
        //         env('ONESIGNAL_REST_API_KEY'),
        //         env('ONESIGNAL_USER_AUTH_KEY')
        //     );

        //     $response = $oneSignal->sendNotificationToUser(
        //         $request->message,
        //         $request->onesignal_player_id,
        //         $url = null,
        //         $data = ['message' => $request->message],
        //         $buttons = null,
        //         $schedule = null
        //     );
                
        //     return response()->json([
        //             'error' => false,
        //             'message' => 'Notification sent successfully',
        //             'onesignal_response' => $response,
        //         ], 200);
        // } catch (\Throwable $e) {
        //     return response()->json([
        //         'error' => true,
        //         'message' => 'Failed to send notification',
        //         'exception' => $e->getMessage(),
        //     ], 500);
        // }
        try {
            $client = new HttpClient();
            $response = $client->post('https://onesignal.com/api/v1/notifications', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Authorization' => 'Basic ' . env('ONESIGNAL_REST_API_KEY'),
                ],
                'json' => [
                    'app_id' => env('ONESIGNAL_APP_ID'),
                    'include_player_ids' => [$request->onesignal_player_id],
                    'contents' => ['en' => $request->message],
                    'data' => [
                        'type' => 'chat',
                        'from' => '',
                        'to' => '',
                        'chat_id' => '',
                        'message' => $request->message,
                        'sender_name' => '',
                    ],
                    'headings' => [
                        'en' => 'Sender name'
                    ],
                    "ios_badgeType" => "Increase",  // or "Increase"
                    "ios_badgeCount" => 1,
                    'is_android' => true,
                    'is_ios' => true,
                    'small_icon' => 'ic_notification', // Android small icon resource name
                    'large_icon' => 'https://schola.one/ic_notification.png', // Large icon URL
                ],
            ]);

            $responseBody = json_decode($response->getBody(), true);
            if (isset($responseBody['errors'])) {                
                return response()->json([
                    'error' => false,
                    'message' => 'Notification sent successfully',
                    'onesignal_response' => $responseBody['errors'],
                ], 200);
            } else {   
                return response()->json([
                    'error' => false,
                    'message' => 'Notification sent successfully',
                    'onesignal_response' => $responseBody,
                ], 200);
            }
        } catch (\Throwable $e) {
            return response()->json([
                'error' => true,
                'message' => 'Failed to send notification',
                'exception' => $e->getMessage(),
            ], 500);
        }
    }

    public function sendMessage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'to' => 'required',
            //'message' => 'required'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }

        $requestMessage = $request->message ?? '';
        try {
            DB::beginTransaction();
            $chat = $this->chat->builder()
                ->where(function($q) use($request){
                    $q->where('sender_id', Auth::user()->id)->where('receiver_id', $request->to);
                })
                ->orWhere(function($q) use($request){
                    $q->where('sender_id', $request->to)->where('receiver_id',Auth::user()->id);
                })
                ->first();

            if (!$chat) {
                $chat = $this->chat->create(['sender_id' => Auth::user()->id, 'receiver_id' => $request->to]);
            }

            $chat = $chat->load(['receiver']);

            $message = $this->message->create(['chat_id' => $chat->id, 'sender_id' => Auth::user()->id, 'message' => $requestMessage]);

            $data = [];
            if ($request->hasFile('files')) {
                foreach ($request->file('files') as $key => $file) {
                    $file_path = $file->store('chat_file', 'public');
                    $data[] = [
                        'id' => 0,
                        'message_id' => $message->id,
                        'file' => $file_path,
                        'file_type' => $file->getClientOriginalExtension()
                    ];
                }
                $this->attachment->createBulk($data);

                if($requestMessage == ''){
                    $requestMessage = 'Attachment';
                }
            }


            // Prepare the message data in a structured format
            $messageData = [
                'id' => $message->id,
                'chat_id' => $chat->id,
                'sender_id' => Auth::user()->id,
                'message' => $requestMessage,
                'timestamp' => $message->created_at->toIso8601String(),
                'attachment' => [],
                'read_at' => '',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
                // Add any other relevant message fields
            ];

            // If there are files, add their information
            if ($request->hasFile('files')) {
                $messageData['attachment'] = collect($data)->map(function($attachment) {
                    return [
                        'id' => 0,
                        'message_id' => 0,
                        'file' => 'https://schola.one/storage/'.$attachment['file'],
                        'file_type' => $attachment['file_type'],
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now(),
                    ];
                })->toArray();
            }

            $onesignalPlayerId = Auth::user()->id == $chat->sender->id ? $chat->receiver->onesignal_player_id : $chat->sender->onesignal_player_id;
            $onesignalRestAppId = '';
            $onesignalRestAPIKey = '';
            $schoolId = Auth::user()->school_id;
            $staffs = DB::select('SELECT * FROM staffs WHERE user_id = ?', [Auth::user()->id]);
            $appType = COUNT($staffs) ? 'parent' : 'teacher';
            $oneSignalSettings = DB::select("SELECT * FROM onesignal WHERE type = ? AND organization = COALESCE((SELECT organization FROM onesignal WHERE organization = ? LIMIT 1), 'schola') LIMIT 1", [$appType, $schoolId]);
            if(COUNT($oneSignalSettings)){
                $onesignalRestAPIKey = $oneSignalSettings[0]->authentication_key;
                $onesignalRestAppId = $oneSignalSettings[0]->app_id;
            }

            // Prepare OneSignal notification
            $notificationData = [
                'app_id' => $onesignalRestAppId,
                'include_player_ids' => [$onesignalPlayerId],
                'contents' => [
                    'en' => strip_tags($requestMessage) // Ensure message is plain text
                ],
                'headings' => [
                    'en' => Auth::user()->first_name . ' ' . Auth::user()->last_name
                ],
                'ios_badgeType' => 'Increase', // Options: 'None', 'SetTo', 'Increase'
                'ios_badgeCount' => 1, // Number to set or increase the badge count by
                'data' => [
                    'type' => 'chat',
                    'from' => Auth::user()->id,
                    'to' => $request->to,
                    'chat_id' => $chat->id,
                    'message' => $messageData,
                    'sender_name' => Auth::user()->first_name . ' ' . Auth::user()->last_name,
                    'sender_image' => Auth::user()->image != null ? Auth::user()->image : '',
                ],
                //'android_channel_id' => 'chat_messages', // Create a specific channel for chat
                //'android_group' => 'chat_' . $chat->id, // Group messages by chat
                //'ios_relevance_score' => 10,
                //'priority' => 10,
                //'content_available' => true,
                //'mutable_content' => true,
                // 'android_properties' => [
                //     'small_icon' => 'ic_notification',
                //     'large_icon' => 'https://schola.one/ic_notification.png',
                //     'android_sound' => 'notification',
                //     'android_visibility' => 1
                // ],
                'is_android' => true,
                'is_ios' => true,
                'small_icon' => 'ic_notification', // Android small icon resource name
                'large_icon' => 'https://schola.one/ic_notification.png', // Large icon URL
            ];
             
            $responseBody = '';
            try {
                $client = new HttpClient();
                $response = $client->post('https://onesignal.com/api/v1/notifications', [
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'Authorization' => 'Basic ' . $onesignalRestAPIKey,
                    ],
                    'json' => $notificationData
                ]);
                
                $responseBody = json_decode($response->getBody(), true);
            } catch (\Throwable $e) {
                \Log::error('OneSignal exception', [
                    'message' => $e->getMessage(),
                    'notification' => $notificationData
                ]);
                // Continue execution - don't let push notification failure break the chat
            }

            $messageData['push_body'] = json_encode($responseBody);
            $messageData['onesignal_rest_app_id'] = $onesignalRestAppId;
            $messageData['onesignal_rest_api_key'] = $onesignalRestAPIKey;
            $messageData['onesignal_player_id'] = $onesignalPlayerId;

            DB::commit();
            ResponseService::successResponse("Message sent successfully.", $messageData);
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getMessage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'receiver_id' => 'required'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }

        try {
            $data = [];
            $data = $this->message->builder()->whereHas('chat',function($q) use($request) {
                $q->where(function($q) use($request){
                    $q->where('sender_id', Auth::user()->id)->where('receiver_id',$request->receiver_id);
                })
                ->orWhere(function($q) use($request){
                    $q->where('sender_id', $request->receiver_id)->where('receiver_id',Auth::user()->id);
                });
            })
            ->with('attachment')->orderBy('id','DESC')
            ->paginate(10);
            
            ResponseService::successResponse("data_fetch_successfully",$data);

        } catch (\Throwable $th) {
            ResponseService::logErrorResponse($th);
            ResponseService::errorResponse();
        }
    }

    public function deleteMessage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required'
        ]);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $this->message->builder()->whereIn('id',$request->id)->delete();
            ResponseService::successResponse("Data Deleted Successfully");

        } catch (\Throwable $th) {
            ResponseService::logErrorResponse($th);
            ResponseService::errorResponse();
        }
    }

    public function readMessage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'message_id' => 'required'
        ]);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $read_at = Carbon::now();
            $this->message->builder()->whereIn('id',$request->message_id)->update(['read_at' => $read_at]);
            ResponseService::successResponse("Data Updated Successfully");

        } catch (\Throwable $th) {
            ResponseService::logErrorResponse($th);
            ResponseService::errorResponse();
        }
    }

    public function getUsers(Request $request)
    {
        if (Auth::user()->hasRole('Teacher')) {
            $validator = Validator::make($request->all(), [
                'role' => 'required|in:Guardian,Staff,Student,Teacher',
            ]);
            if ($validator->fails()) {
                ResponseService::validationError($validator->errors()->first());
            }    
        }
        
        try {
            
            $users = [];
            if (Auth::user()) {
                $searchTerm = $request->search;
                DB::enableQueryLog();
                $users = User::select('id','first_name','last_name','image','school_id')->whereNot('id',Auth::user()->id)->where('status',1);

                if (Auth::user()->hasRole('Student')) { // Student login
                    $class_subject_ids = Auth::user()->student->selectedStudentSubjects()->pluck('class_subject_id')->toArray();
                    // Get teacher list
                    $class_teachers = ClassTeacher::where('class_section_id',Auth::user()->student->class_section_id)->pluck('teacher_id')->toArray();

                    $users = $users->where(function($query) use($class_teachers, $class_subject_ids, $searchTerm) {
                    // Filter by subject teachers
                    $query->whereHas('subjectTeachers', function($q) use($class_teachers, $class_subject_ids) {
                            $q->whereIn('class_subject_id', $class_subject_ids)
                            ->whereNotIn('teacher_id', $class_teachers);
                        })
                        // OR condition to also include class teachers
                        ->orWhereHas('class_teacher', function($q) use($class_teachers) {
                            $q->whereIn('teacher_id', $class_teachers);
                        })
                        // Add search condition for first name or last name
                        ->where(function($q) use ($searchTerm) {
                            $q->where('first_name', 'like', '%' . $searchTerm . '%')
                            ->orWhere('last_name', 'like', '%' . $searchTerm . '%');
                        });
                    })
                    // Eager load subject teachers and class teachers
                    ->with([
                        'subjectTeachers' => function($q) use($class_teachers, $class_subject_ids) {
                            $q->whereIn('class_subject_id', $class_subject_ids)
                            ->with('subject:id,name')
                            ->whereNotIn('teacher_id', $class_teachers);
                        },
                        'class_teacher.class_section.class.stream',
                        'class_teacher.class_section.section',
                        'class_teacher.class_section.medium'
                    ]);
                    // ->orWhereIn('id', $class_teachers);
                } else if(Auth::user()->hasRole('Teacher')) { // Teacher login
                    // Get guardian list
                    if ($request->role == 'Guardian') {
                        $validator = Validator::make($request->all(), [
                            'class_section_id' => 'required',
                        ]);
                        if ($validator->fails()) {
                            ResponseService::validationError($validator->errors()->first());
                        } 
                        $users = $users->role(['Guardian'])->whereHas('child',function($q) use($request) {
                            $q->where('school_id',Auth::user()->school_id)
                            ->where('class_section_id', $request->class_section_id);
                        })->with('child:id,user_id,guardian_id,class_section_id','child.user:id,first_name,last_name,image');

                    } else if($request->role == 'Staff') {
                        // Get staff list
                        $users = $users->where('school_id',Auth::user()->school_id)->has('staff');
                    }
                } else if(Auth::user()->hasRole('Guardian')) { // Guardian login
                    
                    $users = $users->where('school_id', Auth::user()->school_id)->has('staff');
                    
                    if ($request->child_id) {
                        $class_sections = Students::where('id',$request->child_id)->pluck('class_section_id')->toArray();
                    } else {  
                        $child_ids = Auth::user()->load(['child' => function($q) {
                            $q->whereNotNull('class_section_id');
                        }])->child->pluck('id')->toArray();
                        $class_sections = Students::whereIn('id', $child_ids)->pluck('class_section_id')->toArray();    
                    }
                    
                    $class_sections = array_filter($class_sections);

                    $class_teachers = ClassTeacher::whereIn('class_section_id',$class_sections)->pluck('teacher_id')->toArray();
                    $class_teachers = array_unique($class_teachers);
                    $class_teachers = array_values($class_teachers);

                    $subject_teachers = SubjectTeacher::whereIn('class_section_id',$class_sections)->pluck('teacher_id')->toArray();
                    $subject_teachers = array_unique($subject_teachers);
                    $subject_teachers = array_values($subject_teachers);

                    $teacher_ids = array_merge($class_teachers, $subject_teachers);
                    $teacher_ids = array_unique($teacher_ids);
                    $teacher_ids = array_values($teacher_ids);

                    $users = $users->whereIn('id',$teacher_ids)->with(['subjectTeachers' => function($q) use($class_sections){
                        $q->whereIn('class_section_id', $class_sections)
                        ->with('subject:id,name');
                    }])
                    ->where(function($query) use ($searchTerm) {
                        $query->where('first_name', 'like', '%' . $searchTerm . '%')
                              ->orWhere('last_name', 'like', '%' . $searchTerm . '%');
                    })
                    
                    ->with(['class_teacher' => function($q) use($class_sections) {
                        $q->whereIn('class_section_id',$class_sections)
                        ->with('class_section.class.stream','class_section.section','class_section.medium');
                    }])
                    ->has('staff');


                    // =====================
                    // $users = $users->whereHas('subjectTeachers', function($q) use($class_sections) {
                    //     $q->whereIn('class_section_id', $class_sections);
                    // })
                    // ->with(['subjectTeachers' => function($q) use($class_sections){
                    //     $q->whereIn('class_section_id', $class_sections)
                    //     ->with('subject:id,name');
                    // }])
                    // ->where(function($query) use ($searchTerm) {
                    //     $query->where('first_name', 'like', '%' . $searchTerm . '%')
                    //           ->orWhere('last_name', 'like', '%' . $searchTerm . '%');
                    // })
                    // ->orWhereHas('class_teacher',function($q) use($class_sections) {
                    //     $q->whereIn('class_section_id', $class_sections);
                    // })
                    // ->with(['class_teacher' => function($q) use($class_sections) {
                    //     $q->whereIn('class_section_id',$class_sections)
                    //     ->with('class_section.class.stream','class_section.section','class_section.medium');
                    // }])
                    // ->has('staff');


                } else { // Staff login
                    $users = $users->where('school_id',Auth::user()->school_id);
                }
                // if ($request->search) {
                //     $search = $request->search;
                //     $users->where(function($query) use ($search) {
                //         $query->where('first_name', 'LIKE', "%$search%")
                //         ->orWhere('last_name', 'LIKE', "%$search%");
                //     });
                // }
                if ($request->search) {
                    $search = $request->search;
                    $users = $users->where(function($query) use ($search) {
                        $query->where('first_name', 'LIKE', "%$search%")
                            ->orWhere('last_name', 'LIKE', "%$search%")
                            ->orWhereHas('child.user', function($q) use ($search) {
                                $q->where('first_name', 'LIKE', "%$search%")
                                  ->orWhere('last_name', 'LIKE', "%$search%");
                            });
                    });
                }
                $users = $users->orderBy('first_name','ASC')->with('roles')->paginate(10);
            }

            ResponseService::successResponse("Data Fetched Successfully",$users);
        } catch (\Throwable $th) {
            ResponseService::logErrorResponse($th);
            ResponseService::errorResponse();
        }
    }

    public function usersChatHistory(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'role' => 'required|in:Guardian,Staff,Student,Teacher',
        ]);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $data = [];
            if (Auth::user()) {
                $search = $request->search;
                if ($request->role == 'Staff') {
                    // Staff
                    $data = Chat::where(function($q) {
                        $q->where('sender_id',Auth::user()->id)->orWhere('receiver_id',Auth::user()->id);
                    })
                    ->where(function($q) use($request) {
                        $q->whereHas('receiver', function($q) {
                            $q->whereHas('roles', function ($q) {
                                $q->whereNotIn('name', ['Student','Guardian']);
                            });
                        });
                    })
                    ->withCount(['message as unread_count' => function($q) {
                        $q->where('read_at',null)->whereNot('sender_id',Auth::user()->id);
                    }])
                    ->when($search, function ($q) use ($search) {
                        $q->whereHas('receiver', function($query) use($search) {
                            $query->where('first_name', 'LIKE', "%$search%")
                            ->orwhere('last_name', 'LIKE', "%$search%")
                            ->orWhereRaw("concat(first_name,' ',last_name) LIKE '%" . $search . "%'");
                        });
                    })
                    ->paginate(10);
                } else {
                    // Teacher, Guardian, Student
                    $data = Chat::where(function($q) {
                        $q->where('sender_id',Auth::user()->id)->orWhere('receiver_id',Auth::user()->id);
                    })
                    ->where(function($q) use($request) {
                        $q->whereHas('receiver', function($q) use($request) {
                            $q->role([$request->role]);
                        })
                        ->orWhereHas('sender', function($q) use($request) {
                            $q->role([$request->role]);
                        });
                    })
                    ->withCount(['message as unread_count' => function($q) {
                        $q->where('read_at',null)->whereNot('sender_id',Auth::user()->id);
                    }])
                    ->when($search, function ($q) use ($search) {
                        $q->whereHas('receiver', function($query) use($search) {
                            $query->where('first_name', 'LIKE', "%$search%")
                            ->orwhere('last_name', 'LIKE', "%$search%")
                            ->orWhereRaw("concat(first_name,' ',last_name) LIKE '%" . $search . "%'");
                        });
                    })
                    ->paginate(10);

                    // Apply filter after paginating
                    // $data->getCollection()->filter(function($chat) {
                    //     // Skip the chat row if the user attribute is null
                    //     return $chat->user !== null;
                    // });

                    // Return the filtered paginated results
                    $filteredData = $data->setCollection($data->getCollection()->filter(function($chat) {
                        return $chat->user !== null;
                    }));
                    $data->setCollection($filteredData->values());
                    // return $data;
                }
                
                
            }
            
            ResponseService::successResponse("Data Fetched Successfully",$data);
        } catch (\Throwable $th) {
            ResponseService::logErrorResponse($th);
            ResponseService::errorResponse();
        }
    }

    public function classSectionTeachers(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'class_section_id' => 'required'
        ]);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            
            $users = $this->user->builder()->role(['Teacher'])->select('id','first_name','last_name','image')
            ->whereHas('class_teacher',function($q) use($request) {
                $q->where('class_section_id', $request->class_section_id);
            })
            ->orWhereHas('subjectTeachers',function($q) use($request) {
                $q->where('class_section_id', $request->class_section_id);
            })
            // ->with(['class_teacher' => function($q) use($request) {
            //     $q->where('class_section_id', $request->class_section_id);
            // }])
            // ->with(['subjectTeachers' => function($q) use($request) {
            //     $q->where('class_section_id', $request->class_section_id);
            // }])
            ->get();
            
            ResponseService::successResponse("Data Fetched Successfully", $users);

        } catch (\Throwable $th) {
            ResponseService::logErrorResponse($th);
            ResponseService::errorResponse();
        }
    }

    public function syncPermission(Request $request){
        //Try apply to school admin first.
        $roles = DB::select('SELECT * FROM roles WHERE id != 1 AND id = 2');

        $permissions = [
            'student-deposit-list',
            'student-deposit-create',
            'student-deposit-edit',
            'student-deposit-delete',

            'deposit-type-list',
            'deposit-type-create',
            'deposit-type-delete',
            'deposit-type-edit',

            'medium-list',
            'medium-create',
            'medium-edit',
            'medium-delete',

            'section-list',
            'section-create',
            'section-edit',
            'section-delete',

            'class-list',
            'class-create',
            'class-edit',
            'class-delete',

            'class-section-list',
            'class-section-create',
            'class-section-edit',
            'class-section-delete',

            'subject-list',
            'subject-create',
            'subject-edit',
            'subject-delete',

            'teacher-list',
            'teacher-create',
            'teacher-edit',
            'teacher-delete',

            'guardian-list',
            'guardian-create',
            'guardian-edit',
            'guardian-delete',

            'session-year-list',
            'session-year-create',
            'session-year-edit',
            'session-year-delete',

            'student-list',
            'student-create',
            'student-edit',
            'student-delete',

            'timetable-list',
            'timetable-create',
            'timetable-edit',
            'timetable-delete',

            'attendance-create',
            'attendance-delete',
            'attendance-edit',
            'attendance-list',

            'holiday-list',
            'holiday-create',
            'holiday-edit',
            'holiday-delete',

            'announcement-list',
            'announcement-create',
            'announcement-edit',
            'announcement-delete',

            'slider-list',
            'slider-create',
            'slider-edit',
            'slider-delete',

            'exam-create',
            'exam-list',
            'exam-edit',
            'exam-delete',

            'exam-timetable-create',
            'exam-timetable-list',
            'exam-timetable-delete',

            'exam-result',
            'exam-result-edit',
            'exam-upload-marks',

            'assignment-create',
            'assignment-delete',
            'assignment-edit',
            'assignment-list',
            'assignment-submission',

            'student-reset-password',
            'reset-password-list',
            'student-change-password',

            'promote-student-list',
            'promote-student-create',
            'promote-student-edit',
            'promote-student-delete',

            'transfer-student-list',
            'transfer-student-create',
            'transfer-student-edit',
            'transfer-student-delete',

            // 'update-admin-profile',

            'fees-paid',
            'fees-config',

            'form-fields-list',
            'form-fields-create',
            'form-fields-edit',
            'form-fields-delete',

            'grade-create',
            'grade-list',
            'grade-edit',
            'grade-delete',

            'school-setting-manage',

            'fees-type-list',
            'fees-type-create',
            'fees-type-edit',
            'fees-type-delete',

            'fees-class-list',
            'fees-class-create',
            'fees-class-edit',
            'fees-class-delete',


            'online-exam-create',
            'online-exam-list',
            'online-exam-edit',
            'online-exam-delete',
            'online-exam-questions-create',
            'online-exam-questions-list',
            'online-exam-questions-edit',
            'online-exam-questions-delete',
            'online-exam-result-list',

            'role-list',
            'role-create',
            'role-edit',
            'role-delete',

            'staff-list',
            'staff-create',
            'staff-edit',
            'staff-delete',

            'expense-category-list',
            'expense-category-create',
            'expense-category-edit',
            'expense-category-delete',

            'expense-list',
            'expense-create',
            'expense-edit',
            'expense-delete',

            'fees-list',
            'fees-create',
            'fees-edit',
            'fees-delete',

            'semester-list',
            'semester-create',
            'semester-edit',
            'semester-delete',

            'payroll-list',
            'payroll-create',
            'payroll-edit',
            'payroll-delete',

            'stream-list',
            'stream-create',
            'stream-edit',
            'stream-delete',

            'shift-list',
            'shift-create',
            'shift-edit',
            'shift-delete',

            'approve-leave',
            'leave-list',
            'leave-create',
            'leave-edit',
            'leave-delete',
            'id-card-settings',

            'gallery-list',
            'gallery-create',
            'gallery-edit',
            'gallery-delete',

            'notification-list',
            'notification-create',
            'notification-delete',

            'certificate-list',
            'certificate-create', 
            'certificate-edit',
            'certificate-delete',

            'payroll-settings-list',
            'payroll-settings-create',
            'payroll-settings-edit',
            'payroll-settings-delete',

            'school-web-settings',

            'faqs-list',
            'faqs-create',
            'faqs-edit',
            'faqs-delete',

            'class-group-list',
            'class-group-create',
            'class-group-edit',
            'class-group-delete',
            
            'topic-create',
            'topic-delete',
            'topic-edit',
            'topic-list',

            'lesson-create',
            'lesson-delete',
            'lesson-edit',
            'lesson-list',
            'email-template',
            'class-teacher',
            'student-id-card-settings'
            // 'announcement-create',
            // 'announcement-delete',
            // 'announcement-edit',
            // 'announcement-list',
            // 'approve-leave',
            // 'assignment-create',
            // 'assignment-delete',
            // 'assignment-edit',
            // 'assignment-list',
            // 'assignment-submission',
            // 'attendance-create',
            // 'attendance-delete',
            // 'attendance-edit',
            // 'attendance-list',
            // 'class-create',
            // 'class-delete',
            // 'class-edit',
            // 'class-list',
            // 'class-section-create',
            // 'class-section-delete',
            // 'class-section-edit',
            // 'class-section-list',
            // 'class-teacher',
            // 'exam-create',
            // 'exam-delete',
            // 'exam-edit',
            // 'exam-list',
            // 'exam-result',
            // 'exam-result-edit',
            // 'exam-timetable-create',
            // 'exam-timetable-delete',
            // 'exam-timetable-edit',
            // 'exam-timetable-list',
            // 'exam-upload-marks',
            // 'expense-category-create',
            // 'expense-category-delete',
            // 'expense-category-edit',
            // 'expense-category-list',
            // 'expense-create',
            // 'expense-delete',
            // 'expense-edit',
            // 'expense-list',
            // 'fees-class-create',
            // 'fees-class-delete',
            // 'fees-class-edit',
            // 'fees-class-list',
            // 'fees-classes',
            // 'fees-config',
            // 'fees-create',
            // 'fees-delete',
            // 'fees-edit',
            // 'fees-list',
            // 'fees-paid',
            // 'fees-type-create',
            // 'fees-type-delete',
            // 'fees-type-edit',
            // 'fees-type-list',
            // 'form-fields-create',
            // 'form-fields-delete',
            // 'form-fields-edit',
            // 'form-fields-list',
            // 'gallery-create',
            // 'gallery-delete',
            // 'gallery-edit',
            // 'gallery-list',
            // 'grade-create',
            // 'grade-delete',
            // 'grade-edit',
            // 'grade-list',
            // 'guardian-create',
            // 'guardian-delete',
            // 'guardian-edit',
            // 'guardian-list',
            // 'guidance-create',
            // 'guidance-delete',
            // 'guidance-edit',
            // 'guidance-list',
            // 'holiday-create',
            // 'holiday-delete',
            // 'holiday-edit',
            // 'holiday-list',
            // 'leave-create',
            // 'leave-delete',
            // 'leave-edit',
            // 'leave-list',
            // 'lesson-create',
            // 'lesson-delete',
            // 'lesson-edit',
            // 'lesson-list',
            // 'medium-create',
            // 'medium-delete',
            // 'medium-edit',
            // 'medium-list',
            // 'notification-create',
            // 'notification-delete',
            // 'notification-edit',
            // 'notification-list',
            // 'online-exam-create',
            // 'online-exam-delete',
            // 'online-exam-edit',
            // 'online-exam-list',
            // 'online-exam-questions-create',
            // 'online-exam-questions-delete',
            // 'online-exam-questions-edit',
            // 'online-exam-questions-list',
            // 'online-exam-result-list',
            // 'payroll-create',
            // 'payroll-delete',
            // 'payroll-edit',
            // 'payroll-list',
            // 'promote-student-create',
            // 'promote-student-delete',
            // 'promote-student-edit',
            // 'promote-student-list',
            // 'role-create',
            // 'role-delete',
            // 'role-edit',
            // 'role-list',
            // 'school-setting-manage',
            // 'school-terms-condition',
            // 'section-create',
            // 'section-delete',
            // 'section-edit',
            // 'section-list',
            // 'semester-create',
            // 'semester-delete',
            // 'semester-edit',
            // 'semester-list',
            // 'session-year-create',
            // 'session-year-delete',
            // 'session-year-edit',
            // 'session-year-list',
            // 'shift-create',
            // 'shift-delete',
            // 'shift-edit',
            // 'shift-list',
            // 'slider-create',
            // 'slider-delete',
            // 'slider-edit',
            // 'slider-list',
            // 'staff-create',
            // 'staff-delete',
            // 'staff-edit',
            // 'staff-list',
            // 'stream-create',
            // 'stream-delete',
            // 'stream-edit',
            // 'stream-list',
            // 'student-change-password',
            // 'student-create',
            // 'student-delete',
            // 'student-edit',
            // 'student-id-card-settings',
            // 'student-list',
            // 'student-reset-password',
            // 'subject-create',
            // 'subject-delete',
            // 'subject-edit',
            // 'subject-list',
            // 'subscription-bill-payment',
            // 'subscription-change-bills',
            // 'subscription-settings',
            // 'subscription-view',
            // 'teacher-create',
            // 'teacher-delete',
            // 'teacher-edit',
            // 'teacher-list',
            // 'timetable-create',
            // 'timetable-delete',
            // 'timetable-edit',
            // 'timetable-list',
            // 'topic-create',
            // 'topic-delete',
            // 'topic-edit',
            // 'topic-list',
            // 'transfer-student-create',
            // 'transfer-student-delete',
            // 'transfer-student-edit',
            // 'transfer-student-list',
            // 'front-site-setting',
            // 'faqs-list',
            // 'faqs-create',
            // 'faqs-edit',
            // 'faqs-delete',
            // 'class-group-list',
            // 'class-group-create',
            // 'class-group-edit',
            // 'class-group-delete',
            // 'email-template'
        ];
        $permissionIdArray = [];
        for($i = 0; $i < COUNT($roles); $i++){
            $role = Role::findOrFail($roles[$i]->id);    
            for($j = 0; $j < COUNT($permissions); $j++){
                echo 'Loop at i:'.$i.' Roles:'.$roles[$i]->name.' Id:'.$roles[$i]->id;
                echo '';
                $permissionIds = DB::select('SELECT id FROM permissions WHERE name = ?', [$permissions[$j]]);
                if(COUNT($permissionIds)){
                    $permissionId = $permissionIds[0]->id;
                    $permissionIdArray[] = $permissionId;
                    echo 'Loop at j:'.$j.' Permission:'.$permissions[$j].' Id:'.$permissionId;
                    echo '';

                    $roleHasPermission = DB::select('SELECT * FROM role_has_permissions WHERE role_id = ? AND permission_id = ?', [$roles[$i]->id, $permissionId]);
                    if(COUNT($roleHasPermission) == 0){
                        DB::table('role_has_permissions')->insert([
                            'permission_id'=> $permissionId,
                            'role_id' => $roles[$i]->id
                        ]);

                        echo 'Permissionnot found. Insert';
                        echo '';
                    }
                }
            }
            $role->syncPermissions($permissionIdArray);
        }
    }

    public function permissionSeeder(Request $request){
        /**** Create All the Permission ****/
        //$this->createPermissions();

        $this->createSuperAdminRole();

        $this->createSchoolAdminRole();

        $this->createTeacherRole();
        
        // System Features
        $this->systemFeatures();

        Role::updateOrCreate(['name' => 'School Admin']);

        //clear cache
        Artisan::call('view:clear');
        Artisan::call('route:clear');
        Artisan::call('config:clear');
        Artisan::call('cache:clear');
    }

    public function createPermissions() {

        $permissions = [
            ...self::permission('role'),
            ...self::permission('medium'),
            ...self::permission('section'),
            ...self::permission('class'),
            ...self::permission('class-section'),
            ...self::permission('subject'),
            ...self::permission('teacher'),
            ...self::permission('guardian'),
            ...self::permission('session-year'),
            ...self::permission('student'),
            ...self::permission('timetable'),
            ...self::permission('attendance'),
            ...self::permission('holiday'),
            ...self::permission('announcement'),
            ...self::permission('slider'),
            ...self::permission('promote-student'),
            ...self::permission('language'),
            ...self::permission('lesson'),
            ...self::permission('topic'),
            ...self::permission('schools'),
            ...self::permission('form-fields'),
            ...self::permission('grade'),
            ...self::permission('package'),
            ...self::permission('addons'),
            ...self::permission('guidance'),


            ...self::permission('assignment'),
            ['name' => 'assignment-submission'],

            ...self::permission('exam'),
            ...self::permission('exam-timetable'),
            ['name' => 'exam-upload-marks'],
            ['name' => 'exam-result'],
            ['name' => 'exam-result-edit'],

            ['name' => 'system-setting-manage'],
            ['name' => 'fcm-setting-create'],
            ['name' => 'email-setting-create'],
            ['name' => 'privacy-policy'],
            ['name' => 'contact-us'],
            ['name' => 'about-us'],
            ['name' => 'terms-condition'],

            ['name' => 'class-teacher'],
            ['name' => 'student-reset-password'],
            ['name' => 'reset-password-list'],
            ['name' => 'student-change-password'],

            // ['name' => 'update-admin-profile'],

            ['name' => 'fees-classes'],
            ['name' => 'fees-paid'],
            ['name' => 'fees-config'],

            ['name' => 'school-setting-manage'],
            ['name' => 'app-settings'],
            ['name' => 'subscription-view'],

            ...self::permission('online-exam'),
            ...self::permission('online-exam-questions'),
            ['name' => 'online-exam-result-list'],
            ...self::permission('fees-type'),
            ...self::permission('fees-class'),
            ...self::permission('role'),
            ...self::permission('staff'),
            ...self::permission('expense-category'),
            ...self::permission('expense'),
            ...self::permission('semester'),
            ...self::permission('payroll'),
            ...self::permission('stream'),
            ...self::permission('shift'),
            ...self::permission('leave'),
            ['name' => 'approve-leave'],
            ...self::permission('faqs'),


            ['name' => 'fcm-setting-manage'],
            // ['name' => 'front-site-setting'],

            ...self::permission('fees'),
            ...self::permission('transfer-student'),
            ...self::permission('gallery'),
            ...self::permission('notification'),

            ['name' => 'payment-settings'],

            ['name' => 'subscription-settings'],
            ['name' => 'subscription-change-bills'],
            ['name' => 'school-terms-condition'],

            ['name' => 'id-card-settings'],

            ['name' => 'subscription-bill-payment'],
            ['name' => 'web-settings'],

            ...self::permission('certificate'),

            ...self::permission('payroll-settings'),

            ['name' => 'school-web-settings' ],
            ...self::permission('class-group'),

            ['name' => 'email-template' ],


            

        ];
        $permissions = array_map(static function ($data) {
            $data['guard_name'] = 'web';
            return $data;
        }, $permissions);
        Permission::upsert($permissions, ['name'], ['name']);
    }

    public function createSuperAdminRole() {
        $role = Role::withoutGlobalScope('school')->updateOrCreate(['name' => 'Super Admin', 'custom_role' => 0, 'editable' => 0]);
        $superAdminHasAccessTo = [
            'schools-list',
            'schools-create',
            'schools-edit',
            'schools-delete',

            'package-list',
            'package-create',
            'package-edit',
            'package-delete',

            'email-setting-create',
            'privacy-policy',
            'terms-condition',
            'contact-us',
            'about-us',
            'fcm-setting-create',
            'language-list',
            'language-create',
            'language-edit',
            'language-delete',
            'system-setting-manage',
            'app-settings',

            'role-list',
            'role-create',
            'role-edit',
            'role-delete',

            'staff-list',
            'staff-create',
            'staff-edit',
            'staff-delete',

            'addons-list',
            'addons-create',
            'addons-edit',
            'addons-delete',

            'subscription-view',

            'faqs-list',
            'faqs-create',
            'faqs-edit',
            'faqs-delete',

            'fcm-setting-manage',

            // 'front-site-setting',

            // 'update-admin-profile',
            'subscription-settings',
            'subscription-change-bills',
            'school-terms-condition',

            'guidance-list',
            'guidance-create',
            'guidance-edit',
            'guidance-delete',

            'subscription-bill-payment',
            'web-settings'

        ];
        $role->syncPermissions($superAdminHasAccessTo);
    }

    public function createSchoolAdminRole() {
        $role = Role::withoutGlobalScope('school')->updateOrCreate(['name' => 'School Admin', 'custom_role' => 0, 'editable' => 0]);
        $SchoolAdminHasAccessTo = [
            'student-deposit-list',
            'student-deposit-create',
            'student-deposit-edit',
            'student-deposit-delete',

            'deposit-type-list',
            'deposit-type-create',
            'deposit-type-delete',
            'deposit-type-edit',

            'medium-list',
            'medium-create',
            'medium-edit',
            'medium-delete',

            'section-list',
            'section-create',
            'section-edit',
            'section-delete',

            'class-list',
            'class-create',
            'class-edit',
            'class-delete',

            'class-section-list',
            'class-section-create',
            'class-section-edit',
            'class-section-delete',

            'subject-list',
            'subject-create',
            'subject-edit',
            'subject-delete',

            'teacher-list',
            'teacher-create',
            'teacher-edit',
            'teacher-delete',

            'guardian-list',
            'guardian-create',
            'guardian-edit',
            'guardian-delete',

            'session-year-list',
            'session-year-create',
            'session-year-edit',
            'session-year-delete',

            'student-list',
            'student-create',
            'student-edit',
            'student-delete',

            'timetable-list',
            'timetable-create',
            'timetable-edit',
            'timetable-delete',

            'attendance-create',
            'attendance-delete',
            'attendance-edit',
            'attendance-list',

            'holiday-list',
            'holiday-create',
            'holiday-edit',
            'holiday-delete',

            'announcement-list',
            'announcement-create',
            'announcement-edit',
            'announcement-delete',

            'slider-list',
            'slider-create',
            'slider-edit',
            'slider-delete',

            'exam-create',
            'exam-list',
            'exam-edit',
            'exam-delete',

            'exam-timetable-create',
            'exam-timetable-list',
            'exam-timetable-delete',

            'exam-result',
            'exam-result-edit',
            'exam-upload-marks',

            'assignment-create',
            'assignment-delete',
            'assignment-edit',
            'assignment-list',
            'assignment-submission',

            'student-reset-password',
            'reset-password-list',
            'student-change-password',

            'promote-student-list',
            'promote-student-create',
            'promote-student-edit',
            'promote-student-delete',

            'transfer-student-list',
            'transfer-student-create',
            'transfer-student-edit',
            'transfer-student-delete',

            // 'update-admin-profile',

            'fees-paid',
            'fees-config',

            'form-fields-list',
            'form-fields-create',
            'form-fields-edit',
            'form-fields-delete',

            'grade-create',
            'grade-list',
            'grade-edit',
            'grade-delete',

            'school-setting-manage',

            'fees-type-list',
            'fees-type-create',
            'fees-type-edit',
            'fees-type-delete',

            'fees-class-list',
            'fees-class-create',
            'fees-class-edit',
            'fees-class-delete',

            'lesson-list',
            'lesson-create',
            'lesson-edit',
            'lesson-delete',

            'topic-list',
            'topic-create',
            'topic-edit',
            'topic-delete',

            'online-exam-create',
            'online-exam-list',
            'online-exam-edit',
            'online-exam-delete',
            'online-exam-questions-create',
            'online-exam-questions-list',
            'online-exam-questions-edit',
            'online-exam-questions-delete',
            'online-exam-result-list',

            'role-list',
            'role-create',
            'role-edit',
            'role-delete',

            'staff-list',
            'staff-create',
            'staff-edit',
            'staff-delete',

            'expense-category-list',
            'expense-category-create',
            'expense-category-edit',
            'expense-category-delete',

            'expense-list',
            'expense-create',
            'expense-edit',
            'expense-delete',

            'fees-list',
            'fees-create',
            'fees-edit',
            'fees-delete',

            'semester-list',
            'semester-create',
            'semester-edit',
            'semester-delete',

            'payroll-list',
            'payroll-create',
            'payroll-edit',
            'payroll-delete',

            'stream-list',
            'stream-create',
            'stream-edit',
            'stream-delete',

            'shift-list',
            'shift-create',
            'shift-edit',
            'shift-delete',

            'approve-leave',
            'leave-list',
            'leave-create',
            'leave-edit',
            'leave-delete',
            'id-card-settings',

            'gallery-list',
            'gallery-create',
            'gallery-edit',
            'gallery-delete',

            'notification-list',
            'notification-create',
            'notification-delete',

            'certificate-list',
            'certificate-create', 
            'certificate-edit',
            'certificate-delete',

            'payroll-settings-list',
            'payroll-settings-create',
            'payroll-settings-edit',
            'payroll-settings-delete',

            'school-web-settings',

            'faqs-list',
            'faqs-create',
            'faqs-edit',
            'faqs-delete',

            'class-group-list',
            'class-group-create',
            'class-group-edit',
            'class-group-delete',

            'email-template',

        ];

        $role->syncPermissions($SchoolAdminHasAccessTo);
    }

    public function createTeacherRole() {
        //Add Teacher Role
        $teacher_role = Role::updateOrCreate(['name' => 'Teacher']);
        $TeacherHasAccessTo = [
            'class-section-list',
            'student-list',
            'timetable-list',

            'attendance-list',
            'attendance-create',
            'attendance-edit',
            'attendance-delete',

            'holiday-list',

            'gallery-list',
            'gallery-create',
            'gallery-edit',
            'gallery-delete',
            
            'announcement-list',
            'announcement-create',
            'announcement-edit',
            'announcement-delete',

            'assignment-create',
            'assignment-list',
            'assignment-edit',
            'assignment-delete',
            'assignment-submission',

            'lesson-list',
            'lesson-create',
            'lesson-edit',
            'lesson-delete',

            'topic-list',
            'topic-create',
            'topic-edit',
            'topic-delete',

            'online-exam-create',
            'online-exam-list',
            'online-exam-edit',
            'online-exam-delete',
            'online-exam-questions-create',
            'online-exam-questions-list',
            'online-exam-questions-edit',
            'online-exam-questions-delete',
            'online-exam-result-list',

            'exam-upload-marks',
            'exam-result',

            'leave-list',
            'leave-create',
            'leave-edit',
            'leave-delete',
        ];
        $teacher_role->syncPermissions($TeacherHasAccessTo);
    }
    
    /**
     * Generate List , Create , Edit , Delete Permissions
     * @param $prefix
     * @param array $customPermissions - Prefix will be set Automatically
     * @return string[]
     */
    public static function permission($prefix, array $customPermissions = []) {

        $list = [["name" => $prefix . '-list']];
        $create = [["name" => $prefix . '-create']];
        $edit = [["name" => $prefix . '-edit']];
        $delete = [["name" => $prefix . '-delete']];

        $finalArray = array_merge($list, $create, $edit, $delete);
        foreach ($customPermissions as $customPermission) {
            $finalArray[] = ["name" => $prefix . "-" . $customPermission];
        }
        return $finalArray;
    }
    
    // System Features
    public function systemFeatures() {
        $features = [
            ['name' => 'Student Management', 'is_default' => 1, 'status' => 1],
            ['name' => 'Academics Management', 'is_default' => 1, 'status' => 1],
            ['name' => 'Slider Management', 'is_default' => 0, 'status' => 1],
            ['name' => 'Teacher Management', 'is_default' => 1, 'status' => 1],
            ['name' => 'Session Year Management', 'is_default' => 1, 'status' => 1],
            ['name' => 'Holiday Management', 'is_default' => 0, 'status' => 1],
            ['name' => 'Timetable Management', 'is_default' => 0, 'status' => 1],
            ['name' => 'Attendance Management', 'is_default' => 0, 'status' => 1],
            ['name' => 'Exam Management', 'is_default' => 0, 'status' => 1],
            ['name' => 'Lesson Management', 'is_default' => 0, 'status' => 1],
            ['name' => 'Assignment Management', 'is_default' => 0, 'status' => 1],
            ['name' => 'Announcement Management', 'is_default' => 0, 'status' => 1],
            ['name' => 'Staff Management', 'is_default' => 0, 'status' => 1],
            ['name' => 'Expense Management', 'is_default' => 0, 'status' => 1],
            ['name' => 'Staff Leave Management', 'is_default' => 0, 'status' => 1],
            ['name' => 'Fees Management', 'is_default' => 0, 'status' => 1],
            ['name' => 'School Gallery Management', 'is_default' => 0, 'status' => 1],
            ['name' => 'ID Card - Certificate Generation', 'is_default' => 0, 'status' => 1],
            ['name' => 'Website Management', 'is_default' => 0, 'status' => 0],
        ];

        foreach ($features as $key => $feature) {
            Feature::updateOrCreate(['id' => ($key + 1)], $feature);
        }
    }

    public function migration(Request $request)
    {
        // Add email templates
        $systemSettings = array(
            [
            'name' => 'email_template_school_registration',
            'data' => '&lt;p&gt;Dear {school_admin_name},&lt;/p&gt; &lt;p&gt;Welcome to {system_name}!&lt;/p&gt; &lt;p&gt;We are excited to have you as part of our educational community. Below are your registration details to access the system:&lt;/p&gt; &lt;hr&gt; &lt;p&gt;&lt;strong&gt;School Name:&lt;/strong&gt; {school_name}&lt;/p&gt; &lt;p&gt;&lt;strong&gt;System URL:&lt;/strong&gt; {url}&lt;/p&gt; &lt;p&gt;&lt;strong&gt;Your Login Credentials:&lt;/strong&gt;&lt;/p&gt; &lt;ul&gt; &lt;li&gt;&lt;strong&gt;Email:&lt;/strong&gt; {email}&lt;/li&gt; &lt;li&gt;&lt;strong&gt;Password:&lt;/strong&gt; {password}&lt;/li&gt; &lt;/ul&gt; &lt;hr&gt; &lt;p&gt;&lt;strong&gt;Please follow these steps to complete your registration:&lt;/strong&gt;&lt;/p&gt; &lt;ol&gt; &lt;li&gt;Click on the system URL provided above.&lt;/li&gt; &lt;li&gt;Enter your email and password.&lt;/li&gt; &lt;li&gt;Follow the instructions to complete your profile setup.&lt;/li&gt; &lt;/ol&gt; &lt;p&gt;&lt;strong&gt;Important:&lt;/strong&gt;&lt;/p&gt; &lt;ul&gt; &lt;li&gt;For security reasons, please change your password after your first login.&lt;/li&gt; &lt;li&gt;If you encounter any issues during the registration process, please do not hesitate to contact our support team at {support_email} or call {contact}.&lt;/li&gt; &lt;/ul&gt; &lt;p&gt;Thank you for choosing {system_name}. We are committed to providing you with the best educational tools and resources.&lt;/p&gt; &lt;p&gt;Best regards,&lt;/p&gt; &lt;p&gt;{super_admin_name}&lt;br&gt;{system_name}&lt;br&gt;{support_email}&lt;br&gt;{url}&lt;/p&gt;',
            'type' => 'text'
            ],
            [
                'name' => 'system_version',
                'data' => '1.3.2',
                'type' => 'text'
            ]
        );
        
        SystemSetting::upsert($systemSettings, ["name"], ["data","type"]);
        $schools = School::get();
        $email_template_staff = [];
        $email_template_parent = [];
        foreach ($schools as $key => $school) {
            $email_template_staff[] =
                [
                    'name' => 'email-template-staff',
                    'data' => '&lt;p&gt;Dear {full_name},&lt;/p&gt; &lt;p&gt;Welcome to {school_name}!&lt;/p&gt; &lt;p&gt;We are excited to have you join our team. Below are your registration details to access the {school_name}:&lt;/p&gt; &lt;hr&gt; &lt;p&gt;&lt;strong&gt;Your Registration Details:&lt;/strong&gt;&lt;/p&gt; &lt;ul&gt; &lt;li&gt;&lt;strong&gt;Registration URL:&lt;/strong&gt; {url}&lt;/li&gt; &lt;li&gt;&lt;strong&gt;Email:&lt;/strong&gt; {email}&lt;/li&gt; &lt;li&gt;&lt;strong&gt;Password:&lt;/strong&gt; {password}&lt;/li&gt; &lt;/ul&gt; &lt;hr&gt; &lt;p&gt;&lt;strong&gt;Steps to Complete Your Registration:&lt;/strong&gt;&lt;/p&gt; &lt;ol&gt; &lt;li&gt;Click on the registration URL provided above.&lt;/li&gt; &lt;li&gt;Enter your email and password.&lt;/li&gt; &lt;li&gt;Follow the on-screen instructions to set up your profile.&lt;/li&gt; &lt;/ol&gt; &lt;p&gt;&lt;strong&gt;Important:&lt;/strong&gt;&lt;/p&gt; &lt;ul&gt; &lt;li&gt;For security reasons, please change your password upon your first login.&lt;/li&gt; &lt;li&gt;If you have any questions or need assistance during the registration process, please contact our support team at {support_email} or call {support_contact}.&lt;/li&gt; &lt;/ul&gt; &lt;p&gt;&lt;strong&gt;App Download Links:&lt;/strong&gt;&lt;/p&gt; &lt;ul&gt; &lt;li&gt;&lt;strong&gt;Android:&lt;/strong&gt; {android_app}&lt;/li&gt; &lt;li&gt;&lt;strong&gt;iOS:&lt;/strong&gt; {ios_app}&lt;/li&gt; &lt;li&gt;&lt;strong&gt;Huawei: &lt;/strong&gt;{huawei_app}&lt;/li&gt; &lt;/ul&gt; &lt;p&gt;We look forward to a successful academic year with you on our team. Thank you for your commitment to excellence in education.&lt;/p&gt; &lt;p&gt;Best regards,&lt;/p&gt; &lt;p&gt;{school_name}&lt;br&gt;{support_email}&lt;br&gt;{support_contact}&lt;br&gt;{url}&lt;/p&gt;',
                    'type' => 'text',
                    'school_id' => $school->id
                ];
            $email_template_parent[] = [
                'name' => 'email-template-parent',
                'data' => '&lt;p&gt;Dear {parent_name},&lt;/p&gt; &lt;p&gt;We are delighted to welcome {child_name} to {school_name}!&lt;/p&gt; &lt;p&gt;As part of our registration process, we have created accounts for both you and your child in our {school_name}. Below are the registration details you will need to access the system, along with links to download our mobile app for your convenience.&lt;/p&gt; &lt;hr&gt; &lt;p&gt;&lt;strong&gt;Student Credential Details:&lt;/strong&gt;&lt;/p&gt; &lt;ul&gt; &lt;li&gt;&lt;strong&gt;Name:&lt;/strong&gt; {child_name}&lt;/li&gt; &lt;li&gt;&lt;strong&gt;Admission No.: &lt;/strong&gt;{admission_no}&lt;/li&gt; &lt;li&gt;&lt;strong&gt;Student ID.:&lt;/strong&gt; {grno}&lt;/li&gt; &lt;li&gt;&lt;strong&gt;Password:&lt;/strong&gt; {child_password}&lt;/li&gt; &lt;/ul&gt; &lt;ul&gt;{others}&lt;/ul&gt; &lt;hr&gt; &lt;p&gt;&lt;strong&gt;Parent Credential Details:&lt;/strong&gt;&lt;/p&gt; &lt;ul&gt; &lt;li&gt;&lt;strong&gt;Name:&lt;/strong&gt; {parent_name}&lt;/li&gt; &lt;li&gt;&lt;strong&gt;Email:&lt;/strong&gt; {email}&lt;/li&gt; &lt;li&gt;&lt;strong&gt;Password:&lt;/strong&gt; {password}&lt;/li&gt; &lt;/ul&gt; &lt;hr&gt; &lt;p&gt;&lt;strong&gt;App Download Links:&lt;/strong&gt;&lt;/p&gt; &lt;ul&gt; &lt;li&gt;&lt;strong&gt;Android:&lt;/strong&gt; {android_app}&lt;/li&gt; &lt;li&gt;&lt;strong&gt;iOS:&lt;/strong&gt; {ios_app}&lt;/li&gt; &lt;li&gt;&lt;strong&gt;Huawei: &lt;/strong&gt;{huawei_app}&lt;/li&gt; &lt;/ul&gt; &lt;hr&gt; &lt;p&gt;&lt;strong&gt;Steps to Complete the Registration:&lt;/strong&gt;&lt;/p&gt; &lt;ol&gt; &lt;li&gt;Download the school management app using the links above for easier access on your mobile devices.&lt;/li&gt; &lt;li&gt;Enter the email and password for either the student or parent account.&lt;/li&gt; &lt;li&gt;Follow the on-screen instructions to complete the profile setup.&lt;/li&gt; &lt;/ol&gt; &lt;p&gt;&lt;strong&gt;Important:&lt;/strong&gt;&lt;/p&gt; &lt;ul&gt; &lt;li&gt;For security reasons, please ensure that both the student and parent passwords are changed upon first login.&lt;/li&gt; &lt;li&gt;If you encounter any issues during the registration process, please do not hesitate to contact our support team at {support_email} or call {support_contact}.&lt;/li&gt; &lt;/ul&gt; &lt;p&gt;We look forward to an enriching educational experience for {child_name} at {school_name}. Thank you for entrusting us with your child&#039;s education.&lt;/p&gt; &lt;p&gt;Best regards,&lt;/p&gt; &lt;p&gt;{school_name}&lt;br&gt;{support_email}&lt;/p&gt;',
                'type' => 'text',
                'school_id' => $school->id
            ];
            
        }
        $schoolSettings = array_merge($email_template_parent, $email_template_staff);
        SchoolSetting::upsert($schoolSettings, ["name","school_id"], ["data","type"]);
        Cache::flush();
    }

    public function userNotifications(Request $request){
        $userId = Auth::user()->id;

        try {
            if(isset($_GET['page'])){
                $notifications = UserNotifications::where('user_id', '=', $userId)
                ->orderByDesc('created_at')->paginate(10);
                for($i = 0; $i < COUNT($notifications); $i++){
                    $notifications[$i]->image = str_replace('https://schola.one/storage/https://schola.one/storage/','', $notifications[$i]->image ?? '');
                    $notifications[$i]->createdAt = $notifications[$i]->created_at;
                }
                ResponseService::successResponse("Notifications Fetched Successfully", $notifications);
            }
            else{
                $notifications = UserNotifications::where('user_id', '=', $userId)->orderBy('created_at', 'DESC')->get();
                for($i = 0; $i < COUNT($notifications); $i++){
                    $notifications[$i]->image = str_replace('https://schola.one/storage/https://schola.one/storage/','', $notifications[$i]->image ?? '');
                    $notifications[$i]->createdAt = $notifications[$i]->created_at;
                }
                ResponseService::successResponse("Notifications Fetched Successfully", $notifications);
            }
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function faceRecognitionHookGet(Request $request)
    {
        // Store detailed debug data
        DB::table('face_recognition_hook_data')->insert([
            'data' => json_encode([
                'raw_data' => $request->all(),
                'method' => 'GET',
                'debug_info' => [
                    'time' => now()->format('Y-m-d H:i:s'),
                    'url' => $request->fullUrl(),
                    'headers' => $request->headers->all()
                ]
            ]),
            'created_at' => now(),
            'updated_at' => now()
        ]);

        return response()->json(['message' => 'GET data stored successfully']);
    }

    public function faceRecognitionHookPost(Request $request)
    {
        try {
            // Store initial request data
            $debugData = [
                'raw_data' => $request->all(),
                'method' => 'POST',
                'debug_info' => [
                    'time' => now()->format('Y-m-d H:i:s'),
                    'url' => $request->fullUrl(),
                    'headers' => $request->headers->all()
                ]
            ];

            $hookDataId = DB::table('face_recognition_hook_data')->insertGetId([
                'data' => json_encode($debugData),
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // Process attendance
            $result = $this->attendanceFaceRecognition(json_encode($request->all()));

            // Update record with attendance result
            DB::table('face_recognition_hook_data')
                ->where('id', $hookDataId)
                ->update([
                    'data' => json_encode([
                        'initial_data' => $debugData,
                        'attendance_result' => $result,
                        'processing_status' => 'success'
                    ])
                ]);

            return response()->json(['message' => 'POST data processed successfully']);

        } catch (\Exception $e) {
            // Store error information
            if (isset($hookDataId)) {
                DB::table('face_recognition_hook_data')
                    ->where('id', $hookDataId)
                    ->update([
                        'data' => json_encode([
                            'initial_data' => $debugData,
                            'error' => [
                                'message' => $e->getMessage(),
                                'file' => $e->getFile(),
                                'line' => $e->getLine(),
                                'trace' => $e->getTraceAsString()
                            ],
                            'processing_status' => 'error'
                        ])
                    ]);
            }

            return response()->json(['error' => 'Error processing attendance']);
        }
    }

    public function attendanceFaceRecognition($postData)
    {
        try {
            $data = json_decode($postData);
            $rfidId = $data->normalNumber;

            $user = DB::table('users')->select('id', 'first_name', 'last_name','school_id')->where('rfid_id', $rfidId)->first();
            $school = DB::table('schools')->where('id', $user->school_id)->first();
            $todayDate = now();
            $student = DB::table('students')->where('user_id', $user->id)->first();
            $teacher = DB::table('staffs')->where('user_id', $user->id)->first();
            if (!$user) {
                return response()->json(['error' => 'RFID ID does not belong to a valid user.']);
            }

            DB::beginTransaction();
            

            // For students
            if ($student) {
                // Get school's face attendance type setting
                $faceAttendanceType = DB::table('school_settings')
                    ->where('school_id', $school->id)
                    ->where('name', 'face_attendance_type')
                    ->value('data');

                if ($faceAttendanceType == 0) { // All Day Mode
                    $scanAttendanceIfExists = DB::table('subject_attendances')
                        ->where('user_id', $user->id)
                        ->whereNull('subjects_id')
                        ->whereDate('date', $todayDate->format('Y-m-d'))
                        ->first();

                    if (!$scanAttendanceIfExists) {
                        // Insert into subject_attendances first
                        $data = [
                            'school_id' => $school->id,
                            'subjects_id' => null,
                            'user_id' => $user->id,
                            'in_picture' => $data->recognitionPhoto,
                            'in_temperature' => $data->temperature,
                            'status' => 1,
                            'date' => $todayDate->format('Y-m-d'),
                            'clock_in' => $todayDate,
                        ];
                        $subjectAttendanceId = DB::table('subject_attendances')->insertGetId($data);

                        // Also insert into attendances table for all-day attendance
                        if ($subjectAttendanceId) {
                            $sessionYear = DB::select('SELECT * FROM session_years WHERE `school_id` = ? AND `default` = 1 LIMIT 1', [$school->id]);
                            $attendanceData = [
                                'class_section_id' => $student->class_section_id,
                                'student_id' => $user->id,
                                'session_year_id' => $sessionYear[0]->id,
                                'type' => 1, // All day attendance type
                                'date' => $todayDate->format('Y-m-d'),
                                'subject_attendance_id' => $subjectAttendanceId,
                                'school_id' => $school->id,
                                'created_at' => now(),
                                'updated_at' => now(),
                            ];
                            DB::table('attendances')->insert($attendanceData);
                        }

                        if ($subjectAttendanceId) {
                            // Add reward points (Clock In)
                            $classId = DB::table('class_sections')
                                ->where('id', $student->class_section_id)
                                ->value('class_id');
                            $clockInPoints = DB::table('rewards_category')
                                ->where('school_id', $school->id)
                                ->where('category_name', 'Clock In')
                                ->where('is_default', 1)
                                ->first();
                            if ($clockInPoints && $clockInPoints->points_amount > 0) {
                                $lastReward = DB::table('rewards')
                                    ->where('student_id', $student->id)
                                    ->orderBy('updated_at', 'desc')
                                    ->first();
                                $scoreTotal = $lastReward?->score_total ?? 0;
                                $rewardPointTotal = $lastReward?->reward_point_total ?? 0;
                                DB::table('rewards')->insert([
                                    'school_id' => $school->id,
                                    'class_id' => $classId,
                                    'student_id' => $student->id,
                                    'score_amount' => $clockInPoints->points_amount,
                                    'score_total' => $scoreTotal + $clockInPoints->points_amount,
                                    'reward_point_amount' => $clockInPoints->mode == 1 ? $clockInPoints->points_amount : 0,
                                    'reward_point_total' => $clockInPoints->mode == 1 ? $rewardPointTotal + $clockInPoints->points_amount : $rewardPointTotal,
                                    'remark' => 'Clock In',
                                    'category_id' => $clockInPoints->id,
                                ]);
                                
                                // Add notification for reward points
                                $pointsEarned = $clockInPoints->points_amount;
                                $rewardTitle = 'Points Added!';
                                $rewardBody = "Great job! You've earned {$pointsEarned} points for Clock In. Keep up the good work!";
                                $rewardType = 'Notification';
                                send_notification([$user->id, $student->guardian_id], $rewardTitle, $rewardBody, $rewardType);
                            }
                            
                            $attendanceStatus = 'Checked In';
                            $notifyUser[] = $user->id;
                            $notifyUser[] = $student->guardian_id;
                        }
                    } else if (!$scanAttendanceIfExists->clock_out) {
                        $clockIn = Carbon::parse($scanAttendanceIfExists->clock_in);
                        $clockOut = Carbon::parse($todayDate);
                        $diffInMinutes = $clockOut->diffInMinutes($clockIn);
                        
                        if ($diffInMinutes < 15) {
                            return response()->json(['error' => 'You can only clock out after 15 minutes.']);
                        }

                        $interval = $clockOut->diff($clockIn);
                        $totalTime = $interval->format('%H:%I:%S');
                        
                        $data = [
                            'clock_out' => $clockOut,
                            'out_picture' => $data->recognitionPhoto,
                            'out_temperature' => $data->temperature,
                            'total_time' => $totalTime
                        ];
                        $result = DB::table('subject_attendances')
                            ->where('id', $scanAttendanceIfExists->id)
                            ->update($data);
                        if ($result) {
                            // Add reward points (Clock Out)
                            $classId = DB::table('class_sections')
                                ->where('id', $student->class_section_id)
                                ->value('class_id');
                            $clockOutPoints = DB::table('rewards_category')
                                ->where('school_id', $school->id)
                                ->where('category_name', 'Clock Out')
                                ->where('is_default', 1)
                                ->first();
                            if ($clockOutPoints && $clockOutPoints->points_amount > 0) {
                                $lastReward = DB::table('rewards')
                                    ->where('student_id', $student->id)
                                    ->orderBy('updated_at', 'desc')
                                    ->first();
                                $scoreTotal = $lastReward?->score_total ?? 0;
                                $rewardPointTotal = $lastReward?->reward_point_total ?? 0;
                                DB::table('rewards')->insert([
                                    'school_id' => $school->id,
                                    'class_id' => $classId,
                                    'student_id' => $student->id,
                                    'score_amount' => $clockOutPoints->points_amount,
                                    'score_total' => $scoreTotal + $clockOutPoints->points_amount,
                                    'reward_point_amount' => $clockOutPoints->mode == 1 ? $clockOutPoints->points_amount : 0,
                                    'reward_point_total' => $clockOutPoints->mode == 1 ? $rewardPointTotal + $clockOutPoints->points_amount : $rewardPointTotal,
                                    'remark' => 'Clock Out',
                                    'category_id' => $clockOutPoints->id,
                                ]);
                                
                                // Add notification for reward points
                                $pointsEarned = $clockOutPoints->points_amount;
                                $rewardTitle = 'Points Added!';
                                $rewardBody = "Great job! You've earned {$pointsEarned} points for Clock Out. Keep up the good work!";
                                $rewardType = 'Notification';
                                send_notification([$user->id, $student->guardian_id], $rewardTitle, $rewardBody, $rewardType);
                            }
                            
                            $attendanceStatus = 'Checked Out';
                            $notifyUser[] = $user->id;
                            $notifyUser[] = $student->guardian_id;
                        }
                    } else {
                        return response()->json(['error' => 'All Day Attendance has already been recorded']);
                    }
                } 

                else if ($faceAttendanceType == 1) { // Subject Mode
                    // Get school's one-time attendance setting
                    $oneTimeAttendanceSetting = DB::table('school_settings')
                        ->where('school_id', $school->id)
                        ->where('name', 'attendance_setting')
                        ->where('data', 'LIKE', '%4%')
                        ->exists();

                    // Check if attendance already exists for today (one-time rule)
                    if ($oneTimeAttendanceSetting) {
                        $existingSubjectAttendance = DB::table('subject_attendances')
                            ->where('school_id', $school->id)
                            ->where('user_id', $user->id)
                            ->where('subjects_id', $currentClass->subject_id ?? null)
                            ->whereDate('date', $todayDate->format('Y-m-d'))
                            ->exists();
                        if ($existingSubjectAttendance) {
                            return response()->json(['error' => 'Your attendance has already been marked for today.']);
                        }
                    }

                    // Get student's timetable for today (enhanced logic from RFID function)
                    $classesToday = DB::table('timetables as t')
                        ->join('class_sections as cs', 'cs.id', '=', 't.class_section_id')
                        ->where('cs.id', $student->class_section_id)
                        ->where('t.school_id', $school->id)
                        ->where('t.day', $todayDate->format('l'))
                        ->whereNotNull('t.subject_id')
                        ->select('t.subject_teacher_id', 't.subject_id', 't.start_time', 't.end_time')
                        ->orderBy('t.start_time')
                        ->get();

                    $currentTime = $todayDate->format('H:i:s');
                    $upComingClasses = $todayDate->copy()->addHours(1)->format('H:i:s');
                    $currentClass = null;

                    // Enhanced class detection (current or upcoming)
                    foreach ($classesToday as $class) {
                        if (($currentTime >= $class->start_time && $currentTime <= $class->end_time) || 
                            ($class->start_time > $currentTime && $class->start_time <= $upComingClasses)) {
                            $currentClass = $class;
                            break;
                        }
                    }

                    if (!$currentClass) {
                        return response()->json(['error' => 'No scheduled subject found for current time']);
                    }

                    // Check for existing attendance
                    $activeSubjectAttendance = DB::table('subject_attendances')
                        ->where('date', $todayDate->format('Y-m-d'))
                        ->where('user_id', $user->id)
                        ->where('subjects_id', $currentClass->subject_id)
                        ->whereNotNull('clock_in')
                        ->whereNull('clock_out')
                        ->first();

                    if (!$activeSubjectAttendance) { // Clock In
                        // Insert attendance record
                        $subject = DB::table('subjects')->where('id', $currentClass->subject_id)->first();
                        $teacher = DB::table('subject_teachers')->where('id', $currentClass->subject_teacher_id)->first();

                        //etrieve commission type and amount
                        $commissionType = null;
                        $commissionAmount = null;
                        if ($teacher) {
                            $userGroupDetail = DB::table('user_group_details')->where('subject_id', $currentClass->subject_id)->where('teacher_id', $teacher->teacher_id)->first();
                            if ($userGroupDetail) {
                                $groupId = $userGroupDetail->group_id;
                                $userGroup = DB::table('user_groups')->where('id', $groupId)->first();
                                if ($userGroup) {
                                    $commissionType = $userGroup->commission_type;
                                    $commissionAmount = $userGroup->commission_amount;
                                }
                            }
                        }

                        $data = [
                            'school_id' => $school->id,
                            'teacher_id' => $teacher->teacher_id ?? null,
                            'subjects_id' => $currentClass->subject_id,
                            'user_id' => $user->id,
                            'in_picture' => $data->recognitionPhoto,
                            'in_temperature' => $data->temperature,
                            'status' => 1,
                            'date' => $todayDate->format('Y-m-d'),
                            'clock_in' => $todayDate,
                            'fees_per_section' => $subject->commission ?? null,
                            'fees_per_month' => $subject->commission_month ?? null,
                            'commission_typee' => $commissionType ?? null,
                            'commission_amountt' => $commissionAmount ?? 0, 
                        ];
                        $result = DB::table('subject_attendances')->insert($data);

                        $deductionMethod = DB::table('school_settings')
                            ->where('school_id', $school->id)
                            ->where('name', 'deduction_method')
                            ->value('data');
                        if ($deductionMethod) {
                            $creditStatus = DB::table('students')
                                ->where('user_id', $user->id)
                                ->value('credit_status');
                            if ($creditStatus != 1) {
                                    // Direct string comparison
                                    if ($deductionMethod === "1") {
                                        // if ($subject_attendance_id) {
                                            $query = DB::table('subject_attendances as sa')
                                                ->join('users as u', 'sa.user_id', '=', 'u.id')
                                                ->join('credit_system as cs', 'sa.user_id', '=', 'cs.user_id')
                                                ->join('students as s', 's.user_id', '=', 'sa.user_id')
                                                ->join('class_sections as cls','cls.id','=','s.class_section_id')
                                                ->join('classes as c','c.id','=','cls.class_id')
                                                ->join('subjects as sub', 'sa.subjects_id', '=', 'sub.id',)
                                                ->select('cs.user_id as credit_user', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"), 'c.id as class_id', 'cs.balance', 'cs.credit_amount', 'sub.commission')
                                                ->where('sa.date', $todayDate->format('Y-m-d'))
                                                ->where('sa.subjects_id', $subject->id)
                                                ->where('sa.status', 1)
                                                ->where('cs.school_id', $school->id)
                                                ->where('cs.user_id', $user->id)
                                                ->first();
                                            if($query){
                                                $subjectPrice = -$query->commission;
                                                $totalBalance = DB::table('credit_system')
                                                ->where('user_id', $query->credit_user)
                                                ->sum('credit_amount');
                                                // $className=DB::table('classes')->where('id',$class_id).whereNull('deleted_at')->value('name');
                                                
                                                $balance = $totalBalance + $subjectPrice;
                
                                                DB::table('credit_system')->insert([
                                                    'school_id' => $school->id,
                                                    'class_id' => $query->class_id,
                                                    'user_id' => $query->credit_user,
                                                    'credit_amount' => $subjectPrice,
                                                    'balance' => $balance,
                                                    'detail' => empty($subjectId)?'Attended 1 Class':'Attended 1 Class' .'('.$subject->name. ')',
                                                    'created_at' => Carbon::now(),
                                                    'updated_at' => Carbon::now()
                                                ]);
                
                                                if ($balance <= 0) {
                                                    $studentsInsufficientFunds[] = $query->full_name;
                                                }
                                            } else {
                                                $subjectPrice = -$subject->commission;
                                                $value = [
                                                    'school_id' => $school->id,
                                                    'class_id'  => $studentDetail->class_id,
                                                    'user_id'   => $user->id,
                                                    'credit_amount' => $subjectPrice,
                                                    'balance'       => $subjectPrice,
                                                    'detail' => empty($subjectId)?'Attended 1 Class':'Attended 1 Class' .'('.$subject->name. ')',
                                                    'created_at' => Carbon::now(),
                                                    'updated_at' => Carbon::now()
                                                ];
                                                DB::table('credit_system')->insert($value);
                                                $studentsInsufficientFunds[] = $userFullName;
                                            }
                                            $notifyDeduct=$subjectPrice;
                                        // }
                                    } else if ($deductionMethod === "2") {
                                        $userId = DB::table('students as s')
                                            ->join('users as u', 'u.id', '=', 's.user_id')
                                            ->select('s.id', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"))
                                            ->where('u.id', $user->id)
                                            ->first();
            
                                        $package = DB::table('purchase_package as pp')
                                            ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                                            ->select('pp.id as purchase_id')
                                            ->where('pp.student_id', $userId->id)
                                            ->whereIn('pp.status', [0, 2])
                                            ->whereRaw("FIND_IN_SET(?, sp.subject_id)", [$subject->id])
                                            ->first();
                                        if ($package) {
                                            $packageUsage = DB::table('package_usage as pu')
                                                ->where('pu.student_id', $userId->id)
                                                ->where('pu.purchase_package_id', $package->purchase_id)
                                                ->orderBy('created_at', 'DESC')
                                                ->first();
                                            if ($packageUsage && $packageUsage->remaining_session > 0) {
                                                if($packageUsage->remaining_session != 0){
                                                    $remain = $packageUsage->remaining_session - 1;
                                                }
            
                                                DB::table('package_usage')->insert([
                                                    'school_id' => $school->id,
                                                    'purchase_package_id' => $package->purchase_id,
                                                    'student_id' => $userId->id,
                                                    'deduct' => -1,
                                                    'remaining_session' => $remain ?? 0,
                                                    'created_at' => Carbon::now(),
                                                    'updated_at' => Carbon::now(),
                                                ]);
                                                $notifysessionDeduct=1;
            
            
                                                if (in_array($remain, [1,2,3])) {
                                                    $lowSessionCount[] = ['student_id' => $packageUsage->student_id, 'remaining_session' => $remain];
                                                }else if ($remain == 0) {
                                                    $noSessionCount[] =['student_id' => $packageUsage->student_id, 'remaining_session' => $remain];
                                                }
                                                if ($packageUsage && $remain == 0) {
                                                    DB::table('purchase_package as pp')
                                                        ->where('pp.id', $package->purchase_id)
                                                        ->update([
                                                            'status' => 1
                                                        ]);
                                                }
                                            } else {
                                                $studentsInsufficientFunds[] = $userId->full_name;
                                            }
                                        } else {
                                            $studentsInsufficientFunds[] = $userFullName;
                                        }
                                     
                                    } else if ($deductionMethod === "3") {
                                        $packageUsed = false;
                                        $userId = DB::table('students as s')
                                            ->join('users as u', 'u.id', '=', 's.user_id')
                                            ->select('s.id')
                                            ->where('u.id', $user->id)
                                            ->first();
            
                                        $package = DB::table('purchase_package as pp')
                                            ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                                            ->select('pp.id as purchase_id', 'pp.package_id', 'pp.student_id')
                                            ->where('pp.student_id', $userId->id)
                                            ->whereIn('pp.status', [0, 2])
                                            ->whereRaw("FIND_IN_SET(?, sp.subject_id)", [$subject->id])
                                            ->first();
            
                                        if ($package) {
                                            $packageUsage = DB::table('package_usage as pu')
                                            ->where('pu.student_id', $userId->id)
                                            ->where('pu.purchase_package_id', $package->purchase_id)
                                            ->orderBy('created_at', 'DESC')
                                            ->first();
                                            if ($packageUsage && $packageUsage->remaining_session > 0) {
                                                if($packageUsage->remaining_session != 0){
                                                    $remain = $packageUsage->remaining_session - 1;
                                                    $packageUsed = true;
                                                }
            
                                                DB::table('package_usage')->insert([
                                                    'school_id' => $school->id,
                                                    'purchase_package_id' => $package->purchase_id,
                                                    'student_id' => $userId->id,
                                                    'deduct' => -1,
                                                    'remaining_session' => $remain ?? 0,
                                                    'created_at' => Carbon::now(),
                                                    'updated_at' => Carbon::now(),
                                                ]);
                      
                                                $notifysessionDeduct=1;

                                                if (in_array($remain, [1,2,3])) {
                                                    $lowSessionCount[] = ['student_id' => $packageUsage->student_id, 'remaining_session' => $remain];
                                                }else if ($remain == 0) {
                                                    $noSessionCount[] =['student_id' => $packageUsage->student_id, 'remaining_session' => $remain];
                                                }
                                                if ($packageUsage && $remain == 0) {
                                                    DB::table('purchase_package as pp')
                                                        ->where('pp.id', $package->purchase_id)
                                                        ->update([
                                                            'status' => 1
                                                        ]);
                                                }
                                            } 
                                        } 
                                        
                                        $query = DB::table('subject_attendances as sa')
                                            ->join('users as u', 'sa.user_id', '=', 'u.id')
                                            ->join('credit_system as cs', 'sa.user_id', '=', 'cs.user_id')
                                            ->join('subjects as sub', 'sa.subjects_id', '=', 'sub.id')
                                            ->select('cs.user_id as credit_user', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"), 'cs.class_id', 'cs.balance', 'cs.credit_amount', 'sub.commission')
                                            ->where('sa.date', $todayDate->format('Y-m-d'))
                                            ->where('sa.subjects_id', $subject->id)
                                            ->where('sa.status', 1)
                                            ->where('cs.school_id', $school->id)
                                            ->where('cs.user_id', $user->id)
                                            ->first();
                                        if(!$packageUsed){
                                            if($query){
                                                $subjectPrice = -$query->commission;
                                                $totalBalance = DB::table('credit_system')
                                                ->where('user_id', $query->credit_user)
                                                ->sum('credit_amount');
                                               
                                                
                                                $balance = $totalBalance + $subjectPrice;
                
                                                DB::table('credit_system')->insert([
                                                    'school_id' => $school->id,
                                                    'class_id' => $query->class_id,
                                                    'user_id' => $query->credit_user,
                                                    'credit_amount' => $subjectPrice,
                                                    'balance' => $balance,
                                                    'detail' => 'Attended 1 Class' . '('.$subject->name.')',
                                                    'created_at' => Carbon::now(),
                                                    'updated_at' => Carbon::now()
                                                ]);
                
                                                if ($balance <= 0) {
                                                    $studentsInsufficientFunds[] = $query->full_name;
                                                }
                                                $notifyDeduct=$subjectPrice;
                                            } else {
                                                $subjectPrice = -$subject->commission;
                                                $value = [
                                                    'school_id' => $school->id,
                                                    'class_id'  => $studentDetail->class_id,
                                                    'user_id'   => $user->id,
                                                    'credit_amount' => $subjectPrice,
                                                    'balance'       => $subjectPrice,
                                                    'detail' => 'Attended 1 Class' . '('.$subject->name.')',
                                                    'created_at' => Carbon::now(),
                                                    'updated_at' => Carbon::now()
                                                ];
                                                DB::table('credit_system')->insert($value);
                                                $studentsInsufficientFunds[] = $userFullName;
                                                $notifyDeduct=$subjectPrice;
                                            }
                                        }
                                    }
                                }
                        }

                        $attendanceStatus = 'Checked In';
                        $notifyUser = [$user->id, $student->guardian_id];
                    } else { // Clock Out
                        $clockIn = Carbon::parse($activeSubjectAttendance->clock_in);
                        $clockOut = Carbon::parse($todayDate);
                        if ($clockOut->diffInMinutes($clockIn) < 15) {
                            return response()->json(['error' => 'You can only clock out after 15 minutes.']);
                        }

                        // Update attendance record
                        $interval = $clockOut->diff($clockIn);
                        $totalTime = $interval->format('%H:%I:%S');
                        $data = [
                            'clock_out' => $clockOut,
                            'out_picture' => $data->recognitionPhoto,
                            'out_temperature' => $data->temperature,
                            'total_time' => $totalTime,
                        ];
                        DB::table('subject_attendances')
                            ->where('id', $activeSubjectAttendance->id)
                            ->update($data);

                        $attendanceStatus = 'Checked Out';
                        $notifyUser = [$user->id, $student->guardian_id];
                    }
                }
            }
            // For teachers
            else if ($teacher) {
                    // all-day attendance
                    $allTaken = DB::table('teacher_attendance')
                        ->where('user_id', $user->id)
                        ->whereNull('subject_id')
                        ->whereNotNull('clock_in')
                        ->whereNotNull('clock_out')
                        ->where('status', '!=', 0)
                        ->where('date', $todayDate->format('Y-m-d'))
                        ->first();

                    if ($allTaken) {
                        return response()->json(['error' => 'All-day attendance has already been taken']);
                    }

                    $existingAttendance = DB::table('teacher_attendance')
                        ->where('user_id', $user->id)
                        ->whereNull('subject_id')
                        ->whereNotNull('clock_in')
                        ->whereNull('clock_out')
                        ->where('status', '!=', 0)
                        ->where('date', $todayDate->format('Y-m-d'))
                        ->first();

                    if ($existingAttendance) {
                        // Handle Clock Out for all-day
                        $clockIn = Carbon::parse($existingAttendance->clock_in);
                        $clockOut = Carbon::parse($todayDate);
                        $diffInMinutes = $clockOut->diffInMinutes($clockIn);
                        
                        if ($diffInMinutes < 15) {
                            return response()->json(['error' => 'You can only clock out after 15 minutes.']);
                        }

                        $interval = $clockOut->diff($clockIn);
                        $totalTime = $interval->format('%H:%I:%S');
                        
                        $data = [
                            'clock_out' => $clockOut,
                            'out_picture' => $data->recognitionPhoto,
                            'out_temperature' => $data->temperature,
                            'total_time' => $totalTime,
                        ];
                        
                        $result = DB::table('teacher_attendance')
                            ->where('id', $existingAttendance->id)
                            ->update($data);
                            
                        if ($result) {
                            $attendanceStatus = 'Checked Out';
                            $notifyUser[] = $user->id;
                        }
                    } else {
                        // Handle Clock In for all-day
                        $data = [
                            'user_id' => $user->id,
                            'school_id' => $school->id,
                            'clock_in' => $todayDate,
                            'in_picture' => $data->recognitionPhoto,
                            'in_temperature' => $data->temperature,
                            'status' => 1,
                            'date' => $todayDate->format('Y-m-d'),
                            'created_at' => $todayDate,
                            'updated_at' => $todayDate
                        ];
                        
                        $result = DB::table('teacher_attendance')->insert($data);
                        
                        if ($result) {
                            $attendanceStatus = 'Checked In';
                            $notifyUser[] = $user->id;
                        }
                    }
            }
            else {
                return response()->json(['error' => 'User not found']);
            }
            DB::commit();

            if (isset($attendanceStatus) && isset($notifyUser)) {
                $userName = $user->first_name . " " . $user->last_name;
                $notifyTime = $todayDate->format('h:iA');
                $notifyDate = $todayDate->format('d/m/Y');
                $subjectName = $subject->name ?? '';
                
                // Initial attendance notification
                send_notification($notifyUser, $attendanceStatus, 
                    "{$school->name}\n{$userName} {$attendanceStatus} {$subjectName} at {$notifyTime}, {$notifyDate}.", 
                    'Attendance'
                );

                // Credit deduction notification
                if(isset($notifyDeduct)) {
                    $creditStatus = DB::table('students')
                        ->where('user_id', $user->id)
                        ->value('credit_status');

                    if ($creditStatus != 1) {
                        $deductMark = abs($notifyDeduct);
                        send_notification($notifyUser, 'Credit Deduction',
                            "{$deductMark} credits deducted for clocking in to {$subjectName} on {$notifyDate},{$notifyTime} .",
                            'Credit Deduction Notification'
                        );
                    }
                }

                // Session update notification
                if(isset($notifysessionDeduct)) {
                    try {
                        $packageInfo = DB::table('purchase_package as pp')
                            ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                            ->join('students as s', 'pp.student_id', '=', 's.id')
                            ->where('s.user_id', $user->id)
                            ->whereRaw("FIND_IN_SET(?, sp.subject_id)", [$subject->id])
                            ->select('pp.date as purchase_date', 'sp.expiry_days', 'pp.id as pp_id', 'pp.status')
                            ->orderBy('pp.id', 'DESC')
                            ->first();

                        $body = "Clock-in successful!";
                        
                        if ($packageInfo) {
                            $remainingSessions = DB::table('package_usage')
                                ->where('purchase_package_id', $packageInfo->pp_id)
                                ->orderBy('created_at', 'DESC')
                                ->value('remaining_session') ?? 0;

                            if ($remainingSessions > 0) {
                                $expirationDate = Carbon::parse($packageInfo->purchase_date)
                                    ->addDays($packageInfo->expiry_days)
                                    ->format('d-m-Y');
                                $body = "Clock-in successful! You have {$remainingSessions} sessions left (expires {$expirationDate})";
                            } else {
                                $body = "Clock-in successful! Your sessions have finished.";
                            }
                        }

                        send_notification($notifyUser, 'Session Updated', $body, 'session_update');

                    } catch (\Exception $e) {
                        \Log::error('Error in session deduction process:', [
                            'error' => $e->getMessage(),
                            'file' => $e->getFile(),
                            'line' => $e->getLine(),
                            'user_id' => $user->id
                        ]);
                    }
                }

                // Final response
                $creditStatus = DB::table('students')
                    ->where('user_id', $user->id)
                    ->value('credit_status');

                $alertMessage = "{$userName} {$attendanceStatus} at " . $notifyTime;
                
                if (!empty($studentsInsufficientFunds) && $creditStatus != 1) {
                    return response()->json(['warning' => implode(', ', $studentsInsufficientFunds)]);
                }
                
                return response()->json(['success' => $alertMessage]);
            }

        } catch (\Throwable $e) {
            DB::rollback();
            return response()->json([
                'error' => 'An error occurred',
                'message' => $e->getMessage()
            ]);
        }
    }


    public function einvoiceReject($id,Request $request){
        $studentFeeEinvoice = DB::table('student_fees_einvoice')->where('student_fees_id',$id)->where('status',1)->first();
        $studentFees = $this->studentFees->builder()->where('id',$id)->first();
        $student = $this->student->builder()->where('id',$studentFees->student_id)->first();
        $schoolId = $student->school_id;
        $e_invoice_guardian = DB::table('e_invoice_guardian')->where('guardian_id',$student->guardian_id)->where('school_id',$student->school_id)->first();
        $schoolSettings = $this->cache->getSchoolSettings(['*'], $schoolId);
        if($schoolSettings) {
            $accessToken = '';
            $clientId = '';
            $client_secret = array();
            if($schoolSettings['client_id']) {
                $clientId = $schoolSettings['client_id'];
            }
            if($schoolSettings['client_secret_1']){
                $client_secret[] = $schoolSettings['client_secret_1'];
            }
            if($schoolSettings['client_secret_2']){
                $client_secret[] = $schoolSettings['client_secret_2'];
            }
            if(!empty($clientId) && count($client_secret) > 0) {
                $accessTokenExist = DB::table('access_token_einvoice')->where('guardian_id',$e_invoice_guardian->guardian_id)->orderBy('id','desc')->first();
                if($accessTokenExist && $accessTokenExist->time_expired > now()){
                    $accessToken = $accessTokenExist->access_token; 
                } else {
                    foreach ($client_secret as $secret){
                        $url = 'https://api.myinvois.hasil.gov.my/connect/token';
                        $headers = ['onbehalfof:'.$e_invoice_guardian->tax_identification_number];
                        $fields = [
                            'client_id' => $clientId,
                            'client_secret' => $secret,
                            'grant_type'    => 'client_credentials',
                            'scope'         => 'InvoicingAPI'
                        ];
                        $encodedFields = http_build_query($fields);
                        $ch = curl_init();
                        curl_setopt($ch, CURLOPT_URL, $url);
                        curl_setopt($ch, CURLOPT_POST, true);
                        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
                        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
                        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                        curl_setopt($ch,CURLOPT_POSTFIELDS, $encodedFields);
                        $result = curl_exec($ch);
                        if($result == false){
                            ResponseService::errorResponse("Unable to connect to submit e-invoice");
                            die('Curl failed: ' . curl_error($ch));
                        }else {
                            $data = json_decode($result);
                            if(!isset($data->error)){
                                if(!empty($data->access_token)){
                                    $accessToken = $data->access_token;
                                    DB::table('access_token_einvoice')->insert([
                                        'access_token' => $accessToken,
                                        'guardian_id' => $e_invoice_guardian->guardian_id,
                                    ]);
                                    break;
                                }
                            }
                        }
                        curl_close($ch);
                    }
                }
                if($accessToken){
                    $url = 'https://api.myinvois.hasil.gov.my/api/v1.0/documents/state/'.$studentFeeEinvoice->uuid.'/state';
                    $headers = [
                        'authorization: Bearer '.$accessToken,
                        'Content-Type:application/json',
                    ];
                    $data = [
                        'status' => 'rejected',
                        'reason' => $request->reason
                    ];
                    $encodedData = json_encode($data);
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $url);
                    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT");
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
                    curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                    curl_setopt($ch,CURLOPT_POSTFIELDS, $encodedData);
                    $result = curl_exec($ch);
                    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    curl_close($ch);
                    $resultData = json_decode($result);
                    if($httpCode == 200){
                        ResponseService::successResponse(json_encode($resultData));
                        DB::table('student_fees_einvoice')->where('id',$studentFeeEinvoice->id)->update(['status'=> 4,'reject_reason'=>$request->reason]);
                        // ResponseService::successResponse("E-Invoice rejected successfully");
                    } else {
                        if(isset($resultData->message)){
                            ResponseService::errorResponse($resultData->message);
                        }
                        // ResponseService::errorResponse($resultData);
                    }
                }
            }
        }

    }

    public function verifyFiuuPayment() {
       try {
            $paymentConfigs = DB::table('payment_configurations')
                    ->select('secret_key', 'school_id', 'merchant_id')
                    ->where('payment_method', 'Fiuu')
                    ->whereRaw("merchant_id IS NOT NULL AND merchant_id != ''")
                    ->whereRaw("secret_key IS NOT NULL AND secret_key != ''")
                    ->whereRaw("school_id IS NOT NULL AND school_id != ''")
                    ->get();
        
            date_default_timezone_set('Asia/Kuala_Lumpur');
            $currentDatetime = new DateTime();
            $pastDatetime = new DateTime();
            $pastDatetime->modify('-5 minutes');
            echo $currentDatetime->format('Y-m-d H:i:s') . " " . $pastDatetime->format('Y-m-d H:i:s');
            $date = $pastDatetime->format('Y-m-d');
            $date = '2025-04-18';
            $allRecords = [];
            foreach($paymentConfigs as $item) {
                $count=0;
                $url = 'https://api.fiuu.com/RMS/API/PSQ/psq-daily.php';
                $fields = [
                    'merchantID' => $item->merchant_id,
                    'skey'       => md5($date.$item->merchant_id.$item->secret_key),
                    'rdate'      => $date,
                    'version'    => 4.0,
                    'additional_fields' => 'all'
                ];

                $encodedFields = http_build_query($fields);
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
                curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $encodedFields);
                
                $result = curl_exec($ch);
                if ($result === false) {
                    die('Curl failed: ' . curl_error($ch));
                } else {
                    $lines = explode("\n", trim($result));
                    $header = explode("\t", array_shift($lines));
                    if (end($header) === "") {
                        array_pop($header);  
                    }
                    $header[]='school_id';
                    foreach($header as $key=>$head) {
                        if($header[$key]==='Channel'){
                            $count++;
                            if($count>1) {      
                                $header[$key] = 'Channel' . $count;
                            } else {
                                $header[$key] = 'Channel1';
                            }
                        }
                    }
                    //echo md5($date.$item->merchant_id.$item->secret_key);
                    foreach ($lines as $line) {
                        if (!empty($line)) {
                            $values = explode("\t", $line);
                            $values[]=$item->school_id;          
                            $record = array_combine($header, $values);
                            $fieldsToRemove = [
                                'StatCode', 'StatName', 'BIN', 'BillingInfo', 'Channel2', 'GST', 'BankName', 
                                'ExpiryDate', 'SettlementDate', 'TerminalID', 'PayTransactionID', 'ResponseCode',
                                'ApprovalCode', 'Bin4', 'ECIValue', 'CAVV', 'XID', 'AcquirerName', 'BankMID',
                                'CardScheme', 'CardType', 'CardCountry', 'CaptureRefID', 'RefundRefID', 'school_id'
                            ];

                            $filteredRecord = array_diff_key($record, array_flip($fieldsToRemove));

                            $recordString = implode("\n", array_map(function($key, $value) {
                                return "$key: $value";
                            }, array_keys($filteredRecord), $filteredRecord));
                            if ($record) {
                                $exists = DB::table('fiuu_daily_transaction')
                                    ->where('TranId', $values[2])
                                    ->exists();
                                if ($exists) {
                                    echo "TranId: $values[2] exists.";
                                    //Record exist so we can skip it
                                } else {
                                    echo "TranId $values[2] not found";
                                    //Record does not exit, insert and verify the data
                                    DB::table('fiuu_daily_transaction')->insert($record);
                                    $orderId = (int)str_replace("INV", "", $values[1]);
                                    echo 'StatCode:'.$values[5].' ResponseCode:'.$values[30];
                                    if($values[5] == '0' && ($values[30] == '00' || $values[30] == 'SUCCESS')){
                                        echo "Match";
                                        //Process the following record
                                        $studentFeesResults = DB::select("
                                            SELECT * FROM student_fees
                                            WHERE uid = ?
                                            AND school_id = ?
                                            AND status = 'published'
                                        ", [$orderId, $item->school_id]);
                                        if (COUNT($studentFeesResults) == 0) {
                                            echo 'UID/ID: '.$orderId.' not found';
                                            //If student fee not found, something is not right
                                        } else if (COUNT($studentFeesResults) == 1) {
                                            echo "Record found: " . json_encode($studentFeesResults[0]);
                                            //Student fee exist, check their payment status before process
                                            //Calculate the student fees
                                            $res = DB::select("
                                                SELECT      sf.id AS student_fees_id,
                                                            s.id AS student_id,
                                                            s.school_id,
                                                            CONCAT(u.first_name , u.last_name) AS full_name,
                                                            s.guardian_id,
                                                            (
                                                                SELECT  SUM( (fees_type_amount - (fees_type_amount * COALESCE(discount,0)/100) + (fees_type_amount * COALESCE(tax,0)/100) ) * quantity ) 
                                                                FROM    student_fees_details
                                                                WHERE   student_fees_id = sf.id
                                                                AND     fees_type_name NOT IN ('Early Discount', 'Overdue Fees')
                                                            )    AS total_compulsory_fees,
                                                            (
                                                                SELECT  SUM(fees_type_amount * discount / 100) 
                                                                FROM    student_fees_details
                                                                WHERE   student_fees_id = sf.id
                                                                AND     fees_type_name NOT IN ('Early Discount', 'Overdue Fees')
                                                            )   AS sumDiscount,
                                                            (
                                                                SELECT  SUM(fees_type_amount * tax / 100) 
                                                                FROM    student_fees_details
                                                                WHERE   student_fees_id = sf.id
                                                                AND     fees_type_name NOT IN ('Early Discount', 'Overdue Fees')
                                                            )   AS sumTax,
                                                            c.name AS class_name,
                                                            sfp.is_fully_paid,
                                                            sfp.mode,
                                                            sfp.cheque_no,
                                                            sfp.date
                                                FROM        users u
                                                JOIN        students s
                                                ON          u.id = s.user_id
                                                JOIN        class_sections cs
                                                ON          cs.id = s.class_section_id
                                                JOIN        classes c
                                                ON          c.id = cs.class_id
                                                JOIN        student_fees sf
                                                ON          sf.student_id = s.id
                                                AND         sf.status = 'published'
                                                LEFT JOIN   student_fees_paids sfp
                                                ON          sfp.student_fees_id = sf.id
                                                WHERE       s.id = ?
                                                AND         sf.id = ?
                                                AND         sf.deleted_at IS NULL
                                                ORDER BY    sfp.is_fully_paid, sf.created_at desc
                                            ", [$studentFeesResults[0]->student_id, $studentFeesResults[0]->id]);
                                            $student = $res[0];
                                            if ($student) {
                                                $student->due_charges = 0;
                                                $currentDate = date('Y-m-d');
                                                $due_date = DB::select('SELECT due_date FROM student_fees WHERE id= ?', [$student->student_fees_id]);
                                                $due_date = $due_date[0]->due_date;
                                                $due_date = date("Y-m-d", strtotime($due_date));
                                                $early_date = DB::select('SELECT early_date FROM student_fees WHERE id= ?', [$student->student_fees_id]);
                                                $early_date = $early_date[0]->early_date;
                                                $early_date = date("Y-m-d", strtotime($early_date));
                                                $total_compulsory_fees = floatval(str_replace(',', '', $student->total_compulsory_fees));
                                                $earlyFee = 0;
                                                $dueFee = 0;
                                                $studentFeesCharges = DB::table('student_fees')->where('id',$student->student_fees_id)->first();
                                                if ($student->is_fully_paid == 0 && $currentDate > $due_date) {
                                                    $student_overdue_fees = DB::table("student_fees_details")
                                                    ->select("fees_type_amount")
                                                    ->where("fees_type_name", "Overdue Fees")
                                                    ->where("student_fees_id", $student->student_fees_id) 
                                                    ->get();
                                                    if ($student_overdue_fees->isNotEmpty()) {
                                                        $total_compulsory_fees += $student_overdue_fees->first()->fees_type_amount;
                                                        $student->due_charges = number_format($student_overdue_fees->first()->fees_type_amount, 2);
                                                        $student->total_compulsory_fees = $total_compulsory_fees;   
                                                    } else {
                                                        if ($currentDate > $due_date) {
                                                            if (!$studentFeesCharges->due_charges) {
                                                                $dueFee = $studentFeesCharges->due_charges_amount;
                                                                $total_compulsory_fees += $dueFee;
                                                            } else {
                                                                $dueFee =($total_compulsory_fees * ($studentFeesCharges->due_charges / 100));
                                                                $total_compulsory_fees += $dueFee;
                                                            }
                                                            $student->due_charges = number_format($dueFee);
                                                            $student->total_compulsory_fees = $total_compulsory_fees;   
                                                        } 
                                                    }
                                                }
                                                // Only apply the early discount if there are no overdue fees
                                                if ($student->is_fully_paid == 0 && $currentDate <= $early_date && $currentDate <= $due_date) {
                                                    $student_early_discount = DB::table("student_fees_details")
                                                        ->select("fees_type_amount")
                                                        ->where("fees_type_name", "Early Discount")
                                                        ->where("student_fees_id", $student->student_fees_id) 
                                                        ->get();
                                                    if ($student_early_discount->isNotEmpty()) {
                                                        $total_compulsory_fees += $student_early_discount->first()->fees_type_amount;
                                                        $student->early_offer = number_format($student_early_discount->first()->fees_type_amount, 2);
                                                        $student->total_compulsory_fees = $total_compulsory_fees;   
                                                    } else {
                                                        if ($currentDate <= $early_date) {
                                                            if (!$studentFeesCharges->early_offer) {
                                                                $earlyFee = $studentFeesCharges->early_offer_amount;
                                                                $total_compulsory_fees -= $earlyFee;
                                                            } else {
                                                                $earlyFee = ($total_compulsory_fees * ($studentFeesCharges->early_offer / 100));
                                                                $total_compulsory_fees -= $earlyFee;
                                                            }
                                                            $student->early_offer = number_format($earlyFee);
                                                            $student->total_compulsory_fees = $total_compulsory_fees;
                                                        } 
                                                    }
                                                }
                                            }
                                            $sumDiscount= $student->sumDiscount;
                                            $sumTax = $student->sumTax;

                                            $studentFeesPaidResults = DB::select("
                                                SELECT * FROM student_fees_paids
                                                WHERE student_fees_id = ?
                                                AND school_id = ?
                                            ", [$studentFeesResults[0]->id, $item->school_id]);
                                            if (COUNT($studentFeesPaidResults) == 0) {
                                                echo 'student_fees_id:'.$studentFeesResults[0]->id.' not found';
                                                //Calculation the fees and due charges
                                                //Fee payment not found, insert new payment record
                                                
                                                //Insert a new payment record
                                                $data = [
                                                    [
                                                        'student_fees_id' => $studentFeesResults[0]->id,
                                                        'school_id'       => $item->school_id,
                                                        'mode'            => 3,
                                                        'is_fully_paid'   => 1,
                                                        'amount'          => $student->total_compulsory_fees,
                                                        'due_charges'     => $student->due_charges,
                                                        'status'          => 1,
                                                        'date'            => Carbon::now()->toDateString(),
                                                        'created_at'      => Carbon::now(),
                                                        'updated_at'      => Carbon::now(),
                                                        'payment_detail'  => $recordString,
                                                    ],
                                                ];
                                                DB::table('student_fees_paids')->insert($data);
                                                
                                                $userId = DB::table('users')
                                                    ->join('students', 'students.user_id', '=', 'users.id')
                                                    ->join('student_fees', 'student_fees.student_id', '=', 'students.id')
                                                    ->where('students.id', $student->student_id)
                                                    ->where('students.school_id', $student->school_id)
                                                    ->whereNull('students.deleted_at') // Ensure soft-deleted students are excluded
                                                    ->select('users.id') 
                                                    ->first();

                                                $data = [
                                                    'user_id'=> $userId->id,
                                                    'school_id' => $student->school_id,
                                                    'type'      => 4,
                                                    'date' => now(),
                                                    'status'    => 0,
                                                ];
                                                DB::table('admission_notification')->insert($data);
                                            } else if (COUNT($studentFeesPaidResults) == 1) {
                                                //Fee payment found, update only if status is pending or mode is not online
                                                //echo "student_fees_id: ".$studentFeesResults[0]->id.' '.json_encode($studentFeesPaidResults[0]);
                                                echo "student_fees_id: ".$studentFeesResults[0]->id.' found.';
                                                if($studentFeesPaidResults[0]->status == 2 || $studentFeesPaidResults[0]->mode != 3){
                                                    echo 'Update fees status';
                                                    //Update the payment record
                                                    $data = [
                                                        'status'          => 1,
                                                        'mode'            => 3,
                                                        'updated_at'      => Carbon::now(),
                                                        'payment_detail'  => $recordString,
                                                    ];
                                                    DB::table('student_fees_paids')->where('id', $studentFeesPaidResults[0]->id)->update($data);

                                                    $userId = DB::table('users')
                                                    ->join('students', 'students.user_id', '=', 'users.id')
                                                    ->join('student_fees', 'student_fees.student_id', '=', 'students.id')
                                                    ->where('students.id', $student->student_id)
                                                    ->where('students.school_id', $student->school_id)
                                                    ->whereNull('students.deleted_at') // Ensure soft-deleted students are excluded
                                                    ->select('users.id') 
                                                    ->first();

                                                    $data = [
                                                        'user_id'=> $userId->id,
                                                        'school_id' => $student->school_id,
                                                        'type'      => 4,
                                                        'date' => now(),
                                                        'status'    => 0,
                                                    ];
                                                    DB::table('admission_notification')->insert($data);
                                                }
                                            } else {
                                                echo 'student_fees_id:'.$studentFeesResults[0]->id.' found '.COUNT($studentFeesPaidResults).' records';
                                                //Found more than 1 records. Delete all records and insert new one
                                                echo "Found more than 1 records. Delete all records and insert new one";
                                                DB::table('student_fees_paids')->where('student_fees_id', $studentFeesResults[0]->id)->delete();

                                                $data = [
                                                    [
                                                        'student_fees_id' => $studentFeesResults[0]->id,
                                                        'school_id'       => $item->school_id,
                                                        'mode'            => 3,
                                                        'is_fully_paid'   => 1,
                                                        'amount'          => $student->total_compulsory_fees,
                                                        'due_charges'     => $student->due_charges,
                                                        'status'          => 1,
                                                        'date'            => Carbon::now()->toDateString(),
                                                        'created_at'      => Carbon::now(),
                                                        'updated_at'      => Carbon::now(),
                                                        'payment_detail'  => $recordString,
                                                    ],
                                                ];
                                                DB::table('student_fees_paids')->insert($data);
                                            }
                                        } else {
                                            //echo 'UID/ID: '.$orderId.' found '.COUNT($studentFeesResults).' records';
                                            //More than 1 types of fee found. Ignore it.
                                        }
                                    }
                                    else{
                                        echo 'Payment failure. Need to remove the paid record only if there is no payment success record and the payment record exist from fiuu daily transaction but with past date';

                                        //Payment failure. Need to remove the paid record only if there is no payment success record
                                        $fiuuTransactionResults = DB::select("
                                            SELECT * FROM fiuu_daily_transaction
                                            WHERE OrderId = ?
                                            AND school_id = ?
                                            AND StatCode = '0'
                                            AND (ResponseCode = '00' OR ResponseCode = 'SUCCESS')
                                        ", [$values[1], $item->school_id]);
                                        if (COUNT($fiuuTransactionResults) == 0) {
                                            //Check if the fee exist
                                            $studentFeesResults = DB::select("
                                                SELECT * FROM student_fees
                                                WHERE uid = ?
                                                AND school_id = ?
                                                AND status = 'published'
                                            ", [$orderId, $item->school_id]);
                                            if (COUNT($studentFeesResults) == 1) {
                                                //echo "Record found: " . json_encode($studentFeesResults[0]);
                                                echo 'Student fee exist, check their payment status before process';
                                                $studentFeesPaidResults = DB::select("
                                                    SELECT * FROM student_fees_paids
                                                    WHERE student_fees_id = ?
                                                    AND school_id = ?
                                                ", [$studentFeesResults[0]->id, $item->school_id]);
                                                if (COUNT($studentFeesPaidResults) > 0) {
                                                    echo 'student_fees_id:'.$studentFeesResults[0]->id.' found '.COUNT($studentFeesPaidResults).' records';
                                                    //Found more than 1 records. Delete all records and insert new one
                                                    DB::table('student_fees_paids')->where('student_fees_id', $studentFeesResults[0]->id)->delete();
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                curl_close($ch);
            }
            echo 'Process successful';

        } catch (\Throwable $th) {
            Log::error('Error in verify fiuu payment cron job: ' . $th->getMessage(), [
                'exception' => $th,
                'stack_trace' => $th->getTraceAsString(),
            ]);
        }
    }

    public function recurringPayment()
    {
        //New Iogic
        try {
            //If auto_publish_date and invoice_date is not null
            // $studentFees = StudentFee::whereNotNull('invoice_date')
            //     ->whereNull('deleted_at')
            //     ->whereRaw('current_cycle < total_cycles OR total_cycles IS NULL')
            //     ->get();
            $studentFees = StudentFee::whereNotNull('invoice_date')->whereNull('deleted_at')->where('disable_recurring', 0)->where('school_id', 64)->get();
            foreach ($studentFees as $fee) {
                // Check for termination condition
                if (is_numeric($fee->total_cycles) && is_numeric($fee->current_cycle) && $fee->current_cycle >= $fee->total_cycles) {
                    echo 'Recurring invoice terminated for Fee Id: '.$fee->id.' Uid:'.$fee->uid.' due to reaching total cycles.('.$fee->current_cycle .'/'. $fee->total_cycles.')';
                    echo "<br/>";
                    $fee->disable_recurring = 1;
                    $fee->save();
                    continue;
                }
                //To query for draft fee with a predefined publish date
                if($fee->status == 'draft' && $fee->auto_publish_date != null) {
                    echo 'Draft Fee Id: '.$fee->id.' Uid:'.$fee->uid;
                    echo "<br/>";
                    //Auto publish draft invoice
                    if (Carbon::now()->gte(Carbon::parse($fee->auto_publish_date))) {
                        $fee->status = 'published';
                        $fee->updated_at = Carbon::now();
                        $fee->uid = $fee->uid == null ? $this->generateNextUid($fee->school_id) : $fee->uid;
                        $fee->save();

                        // $title = 'New Invoice Available';
                        // $body = 'A new Invoice has been generated';
                        // $type = 'Notification';
                        // $parent = DB::select("SELECT guardian_id FROM students WHERE id = (SELECT student_id FROM student_fees WHERE id = ".$fee->id.")");
                        // if(COUNT($parent)){
                        //     send_notification([$parent[0]->guardian_id], $title, $body, $type);
                        // }

                        echo 'Update draft fee to publish and generate the fee invoice id: '.$fee->uid;
                        echo "<br/>";
                    }
                }
                else if($fee->recurring_invoice != null){
                    echo 'Recurring Fee Id: '.$fee->id.' Uid:'.$fee->uid;
                    echo "<br/>";
                    //Handle recurring invoice
                    $autoPublishDate = $fee->auto_publish_date;
                    if($autoPublishDate == null){
                        $autoPublishDate = $fee->invoice_date;
                    }
                    $nextInvoiceDate = $this->nextRecurringDate($fee->invoice_date, $fee->current_cycle, $fee->recurring_invoice);
                    $nextPublishDate = $this->nextRecurringDate($autoPublishDate, $fee->current_cycle, $fee->recurring_invoice);
                    echo 'Process published invoice auto_publish_date:'.$autoPublishDate.' invoice_date:'.$fee->invoice_date;
                    echo "<br/>";
                    echo 'nextInvoiceDate:'.$nextInvoiceDate.' nextPublishDate:'.$nextPublishDate;
                    echo "<br/>";
                    
                    //Make sure the logic only execute if the nextPublishDate for this fee is today or after today
                    if (Carbon::now()->gte(Carbon::parse($nextPublishDate))) {
                        echo 'nextInvoiceDate:'.$nextInvoiceDate.' Carbon::now():'.Carbon::now();
                        echo '<br/>';
                         // Check if there is existing student fee with the same next invoice date and recurring reference
                        $existingFee = StudentFee::where('invoice_date', $nextInvoiceDate)
                            ->where('recurring_reference', '=', $fee->id)
                            ->exists();
                        if ($existingFee) {
                            echo 'Invoice date already exist with date:'.$nextInvoiceDate;
                            echo '<br/>';
                            continue;
                        }


                        //YYYY-MM-DD
                        $sessionYear = DB::select("SELECT end_date FROM session_years WHERE school_id = ? AND `default` = 1", [$fee->school_id]);
                        if (COUNT($sessionYear) == 0) {
                            continue;
                        } else {
                            $sessionEndDate = $sessionYear[0]->end_date;
                            $sessionEndDate = Carbon::parse($sessionEndDate)->format('Y-m-d');
                            if (Carbon::parse($nextInvoiceDate)->gt(Carbon::parse($sessionEndDate))) {
                                continue;
                            }
                        }

                        $earlyDate = $this->calculateEarlyDate($fee->early_date, $fee->current_cycle, $fee->recurring_invoice);
                        $dueDate = $this->calculateDueDate($fee->due_date, $fee->current_cycle, $fee->recurring_invoice);
                        if (Carbon::parse($nextPublishDate)->lte(Carbon::now())) {
                            $fee->current_cycle += 1;
                            $fee->save();
                            $newFee = StudentFee::create([
                                'name'               => 'Auto-generated',
                                'due_date'           => $dueDate,
                                'due_charges'        => $fee->due_charges ?? 0,
                                'due_charges_amount' => $fee->due_charges_amount ?? 0,
                                'early_date'         => $earlyDate ? Carbon::parse($earlyDate)->format('Y-m-d') : null,
                                'early_offer'        => $fee->early_offer ?? 0,
                                'early_offer_amount' => $fee->early_offer_amount ?? 0,
                                'class_id'           => $fee->class_id,
                                'school_id'          => $fee->school_id,
                                'session_year_id'    => $fee->session_year_id,
                                'student_id'         => $fee->student_id,
                                'status'             => 'published',
                                'uid'                => $this->generateNextUid($fee->school_id),
                                'current_cycle'      => $fee->current_cycle += 1,
                                'total_cycles'        => $fee->total_cycles,
                                'recurring_reference' => $fee->id,
                                'created_at'         => now(),
                                'updated_at'         => now(),
                                'invoice_date'      => $nextInvoiceDate,
                            ]);
            
                            $feeTypes = DB::table('student_fees_details')->where('student_fees_id',$fee->id)->whereNotIn('fees_type_name', ['Overdue Fees', 'Early Discount'])->get();
                            // $this->studentFeeType->builder()->where("student_id", $fee->student_id)->get();

                            $feeDetails = [];
                            foreach ($feeTypes as $feeType) {
                                $feeDetails[] = [
                                    "student_fees_id"   => $newFee->id,
                                    "fees_type_name"    => $feeType->fees_type_name,
                                    "fees_type_amount"  => $feeType->fees_type_amount,
                                    "classification_code" => $feeType->classification_code,
                                    "quantity"          => $feeType->quantity ?? 1,
                                    "unit"              => $feeType->unit,
                                    "discount"          => $feeType->discount ?? null,
                                    "tax"               => $feeType->tax ?? null,
                                    "optional"          => 0,
                                    "school_id"         => $fee->school_id,
                                    "created_at"        => now(),   
                                    "updated_at"        => now(),
                                ];
                            }
                            if (count($feeDetails) > 0) {
                                // Log::info('Fee Details: ' . json_encode($feeDetails));
                                StudentFeesDetail::insert($feeDetails);
                                // $this->studentFeesDetail->upsert($feeDetails, ['student_fees_id','school_id'], ['fees_type_name', 'fees_type_amount', 'classification_code', 'unit', 'quantity', 'optional']);
                            }
                            
                            // $title = 'New Invoice Available';
                            // $body = 'A new Invoice has been generated';
                            // $type = 'Notification';
                            // $parent = DB::select("SELECT guardian_id FROM students WHERE id = (SELECT student_id FROM student_fees WHERE id = ".$newFee->id.")");
                            // if(COUNT($parent)){
                            //     send_notification([$parent[0]->guardian_id], $title, $body, $type);
                            // }
                        }
                    }
                    else{
                        echo 'Publish date is not today or after today';
                        echo "<br/>";
                    }
                }
            }

            
            //If auto_publish_date, invoice_date is null and recurring_invoice is not null, then we only query those published invoice for recurring processing (Past invoice record)
            $studentFees = StudentFee::whereNull('auto_publish_date')->whereNull('invoice_date')->whereNull('deleted_at')->where('status', 'published')->whereNotNull('recurring_invoice')->where('disable_recurring', 0)->where('school_id', 64)->get();
            // $studentFees = StudentFee::whereNull('auto_publish_date')
            //     ->whereNull('invoice_date')
            //     ->whereNull('deleted_at')
            //     ->where('status', 'published')
            //     ->whereNotNull('recurring_invoice')
            //     ->whereRaw('current_cycle < total_cycles OR total_cycles IS NULL')
            //     ->get();
            foreach ($studentFees as $fee) {
                // Check for termination condition
                if (is_numeric($fee->total_cycles) && is_numeric($fee->current_cycle) && $fee->current_cycle >= $fee->total_cycles) {
                    echo 'Recurring invoice terminated for Fee Id: '.$fee->id.' Uid:'.$fee->uid.' due to reaching total cycles.('.$fee->current_cycle .'/'. $fee->total_cycles.')';
                    echo "<br/>";
                    $fee->disable_recurring = 1;
                    $fee->save();
                    continue;
                }
                echo 'Past Recurring Fee Id: '.$fee->id.' Uid:'.$fee->uid;
                echo "<br/>";
                //Handle recurring invoice
                $nextInvoiceDate = $this->nextRecurringDate($fee->created_at, $fee->current_cycle, $fee->recurring_invoice);
                $nextPublishDate = $this->nextRecurringDate($fee->created_at, $fee->current_cycle, $fee->recurring_invoice);
                echo 'Process published invoice auto_publish_date:'.$fee->created_at.' invoice_date:'.$fee->created_at;
                echo "<br/>";
                echo 'nextInvoiceDate:'.$nextInvoiceDate.' nextPublishDate:'.$nextPublishDate;
                echo "<br/>";
                    
                //Make sure the logic only execute if the nextPublishDate for this fee is today or after today
                if (Carbon::now()->gte(Carbon::parse($nextPublishDate))) {
                    echo 'nextInvoiceDate:'.$nextInvoiceDate.' Carbon::now():'.Carbon::now();
                    echo '<br/>';
                     // Check if there is existing student fee with the same next invoice date and recurring reference
                    $existingFee = StudentFee::where('invoice_date', $nextInvoiceDate)
                        ->where('recurring_reference', '=', $fee->id)
                        ->exists();
                    if ($existingFee) {
                        echo 'Invoice date already exist with date:'.$nextInvoiceDate;
                        echo '<br/>';
                        continue;
                    }

                    //YYYY-MM-DD
                    $sessionYear = DB::select("SELECT end_date FROM session_years WHERE school_id = ? AND `default` = 1", [$fee->school_id]);
                    if (COUNT($sessionYear) == 0) {
                        continue;
                    } else {
                        $sessionEndDate = $sessionYear[0]->end_date;
                        $sessionEndDate = Carbon::parse($sessionEndDate)->format('Y-m-d');
                        if (Carbon::parse($nextInvoiceDate)->gt(Carbon::parse($sessionEndDate))) {
                            continue;
                        }
                    }
                    $earlyDate = $this->calculateEarlyDate($fee->early_date, $fee->current_cycle, $fee->recurring_invoice);
                    $dueDate = $this->calculateDueDate($fee->due_date, $fee->current_cycle, $fee->recurring_invoice);
                    if (Carbon::parse($nextPublishDate)->lte(Carbon::now())) {
                        $fee->current_cycle += 1;
                        $fee->save();
                        $newFee = StudentFee::create([
                            'name'               => 'Auto-generated',
                            'due_date'           => $dueDate,
                            'due_charges'        => $fee->due_charges ?? 0,
                            'due_charges_amount' => $fee->due_charges_amount ?? 0,
                            'early_date'         => $earlyDate ? Carbon::parse($earlyDate)->format('Y-m-d') : null,
                            'early_offer'        => $fee->early_offer ?? 0,
                            'early_offer_amount' => $fee->early_offer_amount ?? 0,
                            'class_id'           => $fee->class_id,
                            'school_id'          => $fee->school_id,
                            'session_year_id'    => $fee->session_year_id,
                            'student_id'         => $fee->student_id,
                            'status'             => 'published',
                            'uid'                => $this->generateNextUid($fee->school_id),
                            'current_cycle'      => $fee->current_cycle += 1,
                            'total_cycles'        => $fee->total_cycles,
                            'recurring_reference' => $fee->id,
                            'created_at'         => now(),
                            'updated_at'         => now(),
                            'invoice_date'      => $nextInvoiceDate,
                        ]);
        
                        $feeTypes = DB::table('student_fees_details')->where('student_fees_id',$fee->id)->whereNotIn('fees_type_name', ['Overdue Fees', 'Early Discount'])->get();
                        // $this->studentFeeType->builder()->where("student_id", $fee->student_id)->get();
                        $feeDetails = [];
                        foreach ($feeTypes as $feeType) {
                            $feeDetails[] = [
                                "student_fees_id"   => $newFee->id,
                                "fees_type_name"    => $feeType->fees_type_name,
                                "fees_type_amount"  => $feeType->fees_type_amount,
                                "classification_code" => $feeType->classification_code,
                                "quantity"          => $feeType->quantity ?? 1,
                                "unit"              => $feeType->unit,
                                "discount"          => $feeType->discount ?? null,
                                "tax"               => $feeType->tax ?? null,
                                "optional"          => 0,
                                "school_id"         => $fee->school_id,
                                "created_at"        => now(),   
                                "updated_at"        => now(),
                            ];
                        }
                        if (count($feeDetails) > 0) {
                            // Log::info('Fee Details: ' . json_encode($feeDetails));
                            StudentFeesDetail::insert($feeDetails);
                            // $this->studentFeesDetail->upsert($feeDetails, ['student_fees_id','school_id'], ['fees_type_name', 'fees_type_amount', 'classification_code', 'unit', 'quantity', 'optional']);
                        }
                        
                        // $title = 'New Invoice Available';
                        // $body = 'A new Invoice has been generated';
                        // $type = 'Notification';
                        // $parent = DB::select("SELECT guardian_id FROM students WHERE id = (SELECT student_id FROM student_fees WHERE id = ".$newFee->id.")");
                        // if(COUNT($parent)){
                        //     send_notification([$parent[0]->guardian_id], $title, $body, $type);
                        // }
                    }
                }
                else{
                    echo 'Publish date is not today or after today';
                    echo "<br/>";
                }
            }
            echo 'Recurring cron job executed at: '.now();
        } catch (\Exception $e) {
            echo $e->getMessage();
            Log::error('Error in recurring cron job: ' . $e->getMessage(), [
                'exception' => $e,
                'stack_trace' => $e->getTraceAsString(),
            ]);
        }
    }

    public function addMonth($date, $count) {
        $start = new DateTime($date);
        $checkDate = new DateTime($date);
        $checkDate->modify('last day of +'.$count.' month');
        if ($start->format('Y-m-d') === $start->format('Y-m-t')) {
            $start->modify('last day of +'.$count.' month');
        } else {
            if (($start->format('d') > $checkDate->format('d'))) {
                $start = $checkDate;
            } else {
                $start->modify('+'.$count.' month');
            }   
        }
        return $start;
    }

    private function nextRecurringDate($targetDate, $currentCycle, $recurringMonths)
    {
        $createdAt = Carbon::parse($targetDate);

        $nextRecurringDate = $this->addMonth($createdAt,($currentCycle + 1) * $recurringMonths);
        return $nextRecurringDate->format('Y-m-d');
    }

    private function calculateDueDate($due_date, $current_cycle, $recurring_invoice)
    {
        $createdAt = Carbon::parse($due_date);
        $nextRecurringDate = $this->addMonth($createdAt,($current_cycle + 1) * $recurring_invoice);

        return $nextRecurringDate->format('Y-m-d');
    }

    private function calculateEarlyDate($early_date, $current_cycle, $recurring_invoice)
    {
        if (!$early_date) {
            return null;
        }
        $createdAt = Carbon::parse($early_date);

        $nextRecurringDate = $this->addMonth($createdAt,($current_cycle + 1) * $recurring_invoice);

        return $nextRecurringDate->format('Y-m-d');
    }

    public function generateNextUid($schoolId)
    {
        $latestUID = DB::table('student_fees')->where('school_id', $schoolId)->where('status', 'published')->whereNull('deleted_at')->select('uid')->orderBy('uid', 'desc')->value('uid');
        $uid = $latestUID ? $latestUID + 1 : 1;

        while (DB::table('student_fees')
            ->where('uid', $uid)
            ->where('school_id', $schoolId)
            ->where('status', 'published')
            ->exists()
        ) {
            $uid++;
        } 

        return $uid;
    }

    public function recalculateRewardPreview()
    {
        // Get all rewards for school_id = 42, ordered by student_id and updated_at
        $rewards = DB::table('rewards')
            ->where('school_id', 42)
            ->where('student_id', 1354)
            ->orWhere('student_id', 1352)
            ->orderBy('student_id')
            ->orderBy('updated_at')
            ->select('id', 'student_id', 'points_amount', 'balance', 'score_amount', 
                    'score_total', 'reward_point_amount', 'reward_point_total', 'updated_at')
            ->get();
        
        // Group rewards by student_id
        $studentGroups = $rewards->groupBy('student_id');
        
        // Array to store results
        $results = [];
        
        foreach ($studentGroups as $studentId => $studentRewards) {
            $calculatedBalance = 0;
            $calculatedScoreTotal = 0;
            $calculatedRewardPointTotal = 0;
            
            // Process each reward for this student
            foreach ($studentRewards as $index => $reward) {
                if ($index === 0) {
                    // For the first record, use the original values as starting point
                    $calculatedBalance = $reward->balance;
                    $calculatedScoreTotal = $reward->score_total;
                    $calculatedRewardPointTotal = $reward->reward_point_total;
                } else {
                    // For subsequent records, add the amount to the previous calculated total
                    $calculatedBalance += $reward->points_amount;
                    $calculatedScoreTotal += $reward->score_amount;
                    $calculatedRewardPointTotal += $reward->reward_point_amount;
                }
                
                // Add to results array with both current and calculated values
                $results[] = [
                    'id' => $reward->id,
                    'student_id' => $reward->student_id,
                    'updated_at' => $reward->updated_at,
                    'points_amount' => $reward->points_amount,
                    'current_balance' => $reward->balance,
                    'calculated_balance' => $calculatedBalance,
                    'balance_difference' => $calculatedBalance - $reward->balance,
                    
                    'score_amount' => $reward->score_amount,
                    'current_score_total' => $reward->score_total,
                    'calculated_score_total' => $calculatedScoreTotal,
                    'score_total_difference' => $calculatedScoreTotal - $reward->score_total,
                    
                    'reward_point_amount' => $reward->reward_point_amount,
                    'current_reward_point_total' => $reward->reward_point_total,
                    'calculated_reward_point_total' => $calculatedRewardPointTotal,
                    'reward_point_total_difference' => $calculatedRewardPointTotal - $reward->reward_point_total,
                ];
            }
        }
        
        // Return the results
        return $results;
    }

    public function recalculateRewardUpdate()
    {
        // Begin transaction
        DB::beginTransaction();
        
        try {
            // Get all rewards for school_id = 42, ordered by student_id and updated_at
            $rewards = DB::table('rewards')
                ->where('school_id', 42)
                ->orderBy('student_id')
                ->orderBy('updated_at')
                ->select('id', 'student_id', 'points_amount', 'balance', 'score_amount', 
                        'score_total', 'reward_point_amount', 'reward_point_total', 'updated_at')
                ->get();
            
            // Group rewards by student_id
            $studentGroups = $rewards->groupBy('student_id');
            
            // Track updated records
            $updatedCount = 0;
            
            foreach ($studentGroups as $studentId => $studentRewards) {
                $calculatedBalance = 0;
                $calculatedScoreTotal = 0;
                $calculatedRewardPointTotal = 0;
                
                // Process each reward for this student
                foreach ($studentRewards as $index => $reward) {
                    if ($index === 0) {
                        // For the first record, use the original values as starting point
                        $calculatedBalance = $reward->balance;
                        $calculatedScoreTotal = $reward->score_total;
                        $calculatedRewardPointTotal = $reward->reward_point_total;
                    } else {
                        // For subsequent records, add the amount to the previous calculated total
                        $calculatedBalance += $reward->points_amount;
                        $calculatedScoreTotal += $reward->score_amount;
                        $calculatedRewardPointTotal += $reward->reward_point_amount;
                        
                        // Update only if the calculated values differ from current values
                        if ($calculatedBalance != $reward->balance || 
                            $calculatedScoreTotal != $reward->score_total || 
                            $calculatedRewardPointTotal != $reward->reward_point_total) {
                            
                            DB::table('rewards')
                                ->where('id', $reward->id)
                                ->update([
                                    'balance' => $calculatedBalance,
                                    'score_total' => $calculatedScoreTotal,
                                    'reward_point_total' => $calculatedRewardPointTotal
                                ]);
                                
                            $updatedCount++;
                        }
                    }
                }
            }
            
            // Commit transaction
            DB::commit();
            
            return [
                'success' => true,
                'message' => "Successfully updated {$updatedCount} records.",
            ];
            
        } catch (\Exception $e) {
            // Rollback transaction on error
            DB::rollBack();
            
            return [
                'success' => false,
                'message' => "Error updating records: " . $e->getMessage(),
            ];
        }
    }

    public function myinvois(Request $request) {
        $action = $request->input('action', '');
        
        if (empty($action)) {
            return response()->json([
                'success' => false,
                'message' => 'Action parameter is required',
                'errors' => ['action' => 'Action parameter is required']
            ], 400);
        }
        
        $schoolId = 1;
        // Handle get document types
        if ($action == 'get_document_types') {
            return EInvoiceHelper::getAllDocumentTypes($schoolId);
        }
        
        // Handle search tax payer TIN
        if ($action == 'search_tax_payer_tin') {
            $taxPayerName = (string)$request->input('tax_payer_name', '');
            $idType = $request->input('id_type', ''); 
            $idValue = $request->input('id_value', '');
            
            if (empty($idType) || empty($idValue)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Required fields are missing',
                    'errors' => [
                        'id_type' => empty($idType) ? 'ID type is required' : null,
                        'id_value' => empty($idValue) ? 'ID value is required' : null
                    ]
                ], 400);
            }
            
            return EInvoiceHelper::searchTaxPayerTin($schoolId, $taxPayerName, $idType, $idValue);
        }
        
        
        // Handle validate tax payer TIN
        if ($action == 'validate_tax_payer_tin') {
            $tin = $request->input('tin', '');
            $idType = $request->input('id_type', '');
            $idValue = $request->input('id_value', '');
            
            if (empty($tin) || empty($idType) || empty($idValue)) {
                return response()->json([
                    'success' => false,
                    'message' => 'All fields are required',
                    'errors' => [
                        'tin' => empty($tin) ? 'TIN is required' : null,
                        'id_type' => empty($idType) ? 'ID type is required' : null,
                        'id_value' => empty($idValue) ? 'ID value is required' : null,
                    ]
                ], 400);
            }
            
            return EInvoiceHelper::validateTaxPayerTin($schoolId, $tin, $idType, $idValue);
        }
        
        // Handle get notifications
        if ($action == 'get_notifications') {
            return EInvoiceHelper::getNotifications(
                $schoolId,
                $request->input('date_from'),
                $request->input('date_to'),
                $request->input('type'),
                $request->input('language'),
                $request->input('status'),
                $request->input('page_no', 1),
                $request->input('page_size', 100));
        }
        
        // Handle get tax payer from QR code
        if ($action == 'get_tax_payer_from_qrcode') {
            $qrCodeText = $request->input('qr_code', '');
            
            if (empty($qrCodeText)) {
                return response()->json([
                    'success' => false,
                    'message' => 'QR code text is required',
                    'errors' => [
                        'qrCodeText' => empty($qrCodeText) ? 'QR code text is required' : null
                    ]
                ], 400);
            }
            
            return EInvoiceHelper::getTaxPayerFromQrcode($schoolId, $qrCodeText);
        }
        
        // Handle cancel document
        if ($action == 'cancel_document') {
            $id = $request->input('id', '');
            $reason = $request->input('reason', '');
            
            if (empty($id) || empty($reason)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Document ID and reason are required',
                    'errors' => [
                        'id' => empty($id) ? 'Document ID is required' : null,
                        'reason' => empty($reason) ? 'Reason is required' : null
                    ]
                ], 400);
            }
            
            return EInvoiceHelper::cancelDocument($schoolId, $id, $reason);
        }

        // Handle reject document
        if ($action == 'reject_document') {
            $id = $request->input('id', '');
            $reason = $request->input('reason', '');
            
            if (empty($id) || empty($reason)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Document ID and reason are required',
                    'errors' => [
                        'id' => empty($id) ? 'Document ID is required' : null,
                        'reason' => empty($reason) ? 'Reason is required' : null
                    ]
                ], 400);
            }
            
            return EInvoiceHelper::rejectDocument(
                $schoolId,
                $id,
                $reason);
        }

        // Handle get document QR code URL
        if ($action == 'get_document_qr_code_url') {
            $id = $request->input('id', '');
            $longId = $request->input('longId', '');
            
            if (empty($id) || empty($longId)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Document ID and Long ID are required',
                    'errors' => [
                        'id' => empty($id) ? 'Document ID is required' : null,
                        'longId' => empty($longId) ? 'Long ID is required' : null
                    ]
                ], 400);
            }
            
            return EInvoiceHelper::generateDocumentQrCodeUrl($schoolId, $id, $longId);
        }

        // Handle get submission
        if ($action == 'get_submission') {
            $id = $request->input('id', '');
            
            if (empty($id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Submission ID is required',
                    'errors' => [
                        'id' => empty($id) ? 'Submission ID is required' : null
                    ]
                ], 400);
            }
            
            return EInvoiceHelper::getSubmission($schoolId, $id);
        }

        // Handle get document
        if ($action == 'get_document') {
            $id = $request->input('id', '');
            
            if (empty($id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Document ID is required',
                    'errors' => ['id' => 'Document ID is required']
                ], 400);
            }
            
            return EInvoiceHelper::getDocument($schoolId, $id);
        }

        // Handle get document detail
        if ($action == 'get_document_detail') {
            $id = $request->input('id', '');
            
            if (empty($id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Document ID is required',
                    'errors' => ['id' => 'Document ID is required']
                ], 400);
            }
            
            return EInvoiceHelper::getDocumentDetail($schoolId, $id);
        }

        // Handle search documents
        if ($action == 'search_documents') {
            $submissionDateFrom = $request->input('submission_date_from');
            $submissionDateTo = $request->input('submission_date_to');
            
            if (empty($submissionDateFrom) || empty($submissionDateTo)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Submission date range is required',
                    'errors' => [
                        'submission_date_from' => empty($submissionDateFrom) ? 'Start date is required' : null,
                        'submission_date_to' => empty($submissionDateTo) ? 'End date is required' : null
                    ]
                ], 400);
            }
            
            return EInvoiceHelper::searchDocuments(
                $schoolId,
                $submissionDateFrom,
                $submissionDateTo,
                $request->input('id'),
                $request->input('page_no', 1),
                $request->input('page_size', 100),
                $request->input('issue_date_from'),
                $request->input('issue_date_to'),
                $request->input('direction'),
                $request->input('status'),
                $request->input('document_type'),
                $request->input('search_query')
            );
        }

        // Handle get recent documents
        if ($action == 'get_recent_documents') {
            return EInvoiceHelper::getRecentDocuments(
                $schoolId,
                $request->input('page_no'),
                $request->input('page_size'),
                $request->input('submission_date_from'),
                $request->input('submission_date_to'),
                $request->input('issue_date_from'),
                $request->input('issue_date_to'),
                $request->input('direction'),
                $request->input('status'),
                $request->input('document_type'),
                $request->input('receiver_id'),
                $request->input('receiver_id_type'),
                $request->input('receiver_tin'),
                $request->input('issuer_id'),
                $request->input('issuer_id_type'),
                $request->input('issuer_tin')
            );
        }

        // Handle submit document
        if ($action == 'submit_document') {
            $submitDocument = null;
            $invoice = null;
            
            try {
                // Validate required fields
                if (!$request->has('id') || !$request->has('supplier') || !$request->has('customer')) {
                    return ResponseService::errorResponse(
                        'Missing required parameters',
                        ['errors' => ['id, supplier and customer are required']],
                        400
                    );
                }
                
                $data = $request->all();
                
                // Use EInvoiceHelper to validate and convert invoice type code
                $invoiceType = EInvoiceHelper::mapInvoiceTypeCode($request->input('invoice_type'));
                //Generate the invoice xml
                $invoice = EInvoiceHelper::createXmlDocument($invoiceType, $data);
                
                $documents = [];
                $document = MyInvoisHelper::getSubmitDocument($request->input('id'), $invoice);
                $documents[] = $document;
                

                //This is how you extract the signatue value from the invoice xml
                $signatureValue = '';
                $dom = new DOMDocument();
                $dom->loadXML($invoice);
                $xpath = new DOMXPath($dom);
                $xpath->registerNamespace('ds', 'http://www.w3.org/2000/09/xmldsig#');
                $xpath->registerNamespace('cac', 'urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2');
                $xpath->registerNamespace('ext', 'urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2');
                $signatureValueNodes = $xpath->query('//ds:SignatureValue');
                if ($signatureValueNodes->length > 0) {
                    $signatureValue = $signatureValueNodes->item(0)->nodeValue;
                }
                
               return EInvoiceHelper::submitDocument($schoolId, $documents);
            } catch (\Exception $exception) {
                // Try to decode the exception message as JSON
                $errorMessage = $exception->getMessage();
                $submitDocument = json_decode($errorMessage);
                
                // If JSON decode fails, use the original message
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $submitDocument = $errorMessage;
                }
                return $errorMessage;
            }
        }
    }
    
    public function deviceRegister(Request $request)
    {
        //Log::info('device register: ' . json_encode($request->all()));
        // Store the data in the database
        //\App\Models\DeviceLog::create(['data' => json_encode($request->all())]);

        return response()->json(['message' => 'Data received successfully']);
    }
}
