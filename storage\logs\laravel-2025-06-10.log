[2025-06-10 09:24:51] local.INFO: Detected N+1 Query  
[2025-06-10 09:24:51] local.INFO: Model: App\Models\Staff
Relation: App\Models\Expense
Num-Called: 21
Call-Stack:
#14 \vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php:23
#15 \vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php:52
#17 \app\Http\Controllers\PayrollController.php:254
#18 \vendor\laravel\framework\src\Illuminate\Routing\Controller.php:54
#19 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:43
#20 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:260
#21 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#22 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:806
#23 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:144
#24 \app\Http\Middleware\Status.php:28
#25 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#26 \app\Http\Middleware\CheckSchoolStatus.php:43
#27 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#28 \app\Http\Middleware\CheckRole.php:22
#29 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#30 \vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php:66
#31 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#32 \app\Http\Middleware\LanguageManager.php:61
#33 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#34 \app\Http\Middleware\DemoMiddleware.php:50
#35 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#36 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#37 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#38 \vendor\laravel\framework\src\Illuminate\Auth\Middleware\Authenticate.php:57
#39 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#40 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#41 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#42 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#44 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#45 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#47 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#49 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
  
[2025-06-10 09:24:51] local.INFO: Model: App\Models\Expense
Relation: staff
Num-Called: 84
Call-Stack:
#19 \app\Models\Expense.php:86
#24 \vendor\laravel\framework\src\Illuminate\Collections\Traits\EnumeratesValues.php:919
#27 \vendor\laravel\framework\src\Illuminate\Collections\Collection.php:778
#29 \vendor\laravel\framework\src\Illuminate\Collections\Traits\EnumeratesValues.php:919
#32 \app\Http\Controllers\PayrollController.php:208
#33 \vendor\laravel\framework\src\Illuminate\Routing\Controller.php:54
#34 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:43
#35 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:260
#36 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#37 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:806
#38 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:144
#39 \app\Http\Middleware\Status.php:28
#40 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#41 \app\Http\Middleware\CheckSchoolStatus.php:43
#42 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#43 \app\Http\Middleware\CheckRole.php:22
#44 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#45 \vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php:66
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#47 \app\Http\Middleware\LanguageManager.php:61
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#49 \app\Http\Middleware\DemoMiddleware.php:50
  
[2025-06-10 09:24:53] local.INFO: Detected N+1 Query  
[2025-06-10 09:24:53] local.INFO: Model: App\Models\Staff
Relation: App\Models\Expense
Num-Called: 21
Call-Stack:
#14 \vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php:23
#15 \vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php:52
#17 \app\Http\Controllers\PayrollController.php:254
#18 \vendor\laravel\framework\src\Illuminate\Routing\Controller.php:54
#19 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:43
#20 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:260
#21 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#22 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:806
#23 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:144
#24 \app\Http\Middleware\Status.php:28
#25 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#26 \app\Http\Middleware\CheckSchoolStatus.php:43
#27 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#28 \app\Http\Middleware\CheckRole.php:22
#29 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#30 \vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php:66
#31 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#32 \app\Http\Middleware\LanguageManager.php:61
#33 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#34 \app\Http\Middleware\DemoMiddleware.php:50
#35 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#36 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#37 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#38 \vendor\laravel\framework\src\Illuminate\Auth\Middleware\Authenticate.php:57
#39 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#40 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#41 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#42 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#44 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#45 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#47 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#49 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
  
[2025-06-10 09:24:53] local.INFO: Model: App\Models\Expense
Relation: staff
Num-Called: 84
Call-Stack:
#19 \app\Models\Expense.php:86
#24 \vendor\laravel\framework\src\Illuminate\Collections\Traits\EnumeratesValues.php:919
#27 \vendor\laravel\framework\src\Illuminate\Collections\Collection.php:778
#29 \vendor\laravel\framework\src\Illuminate\Collections\Traits\EnumeratesValues.php:919
#32 \app\Http\Controllers\PayrollController.php:208
#33 \vendor\laravel\framework\src\Illuminate\Routing\Controller.php:54
#34 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:43
#35 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:260
#36 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#37 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:806
#38 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:144
#39 \app\Http\Middleware\Status.php:28
#40 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#41 \app\Http\Middleware\CheckSchoolStatus.php:43
#42 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#43 \app\Http\Middleware\CheckRole.php:22
#44 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#45 \vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php:66
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#47 \app\Http\Middleware\LanguageManager.php:61
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#49 \app\Http\Middleware\DemoMiddleware.php:50
  
[2025-06-10 09:24:53] local.INFO: Detected N+1 Query  
[2025-06-10 09:24:53] local.INFO: Model: App\Models\Staff
Relation: App\Models\Expense
Num-Called: 21
Call-Stack:
#14 \vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php:23
#15 \vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php:52
#17 \app\Http\Controllers\PayrollController.php:254
#18 \vendor\laravel\framework\src\Illuminate\Routing\Controller.php:54
#19 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:43
#20 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:260
#21 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#22 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:806
#23 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:144
#24 \app\Http\Middleware\Status.php:28
#25 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#26 \app\Http\Middleware\CheckSchoolStatus.php:43
#27 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#28 \app\Http\Middleware\CheckRole.php:22
#29 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#30 \vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php:66
#31 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#32 \app\Http\Middleware\LanguageManager.php:61
#33 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#34 \app\Http\Middleware\DemoMiddleware.php:50
#35 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#36 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#37 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#38 \vendor\laravel\framework\src\Illuminate\Auth\Middleware\Authenticate.php:57
#39 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#40 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#41 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#42 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#44 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#45 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#47 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#49 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
  
[2025-06-10 09:24:53] local.INFO: Model: App\Models\Expense
Relation: staff
Num-Called: 84
Call-Stack:
#19 \app\Models\Expense.php:86
#24 \vendor\laravel\framework\src\Illuminate\Collections\Traits\EnumeratesValues.php:919
#27 \vendor\laravel\framework\src\Illuminate\Collections\Collection.php:778
#29 \vendor\laravel\framework\src\Illuminate\Collections\Traits\EnumeratesValues.php:919
#32 \app\Http\Controllers\PayrollController.php:208
#33 \vendor\laravel\framework\src\Illuminate\Routing\Controller.php:54
#34 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:43
#35 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:260
#36 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#37 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:806
#38 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:144
#39 \app\Http\Middleware\Status.php:28
#40 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#41 \app\Http\Middleware\CheckSchoolStatus.php:43
#42 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#43 \app\Http\Middleware\CheckRole.php:22
#44 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#45 \vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php:66
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#47 \app\Http\Middleware\LanguageManager.php:61
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#49 \app\Http\Middleware\DemoMiddleware.php:50
  
[2025-06-10 09:35:04] local.INFO: Detected N+1 Query  
[2025-06-10 09:35:04] local.INFO: Model: App\Models\Staff
Relation: App\Models\Expense
Num-Called: 21
Call-Stack:
#14 \vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php:23
#15 \vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php:52
#17 \app\Http\Controllers\PayrollController.php:254
#18 \vendor\laravel\framework\src\Illuminate\Routing\Controller.php:54
#19 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:43
#20 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:260
#21 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#22 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:806
#23 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:144
#24 \app\Http\Middleware\Status.php:28
#25 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#26 \app\Http\Middleware\CheckSchoolStatus.php:43
#27 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#28 \app\Http\Middleware\CheckRole.php:22
#29 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#30 \vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php:66
#31 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#32 \app\Http\Middleware\LanguageManager.php:61
#33 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#34 \app\Http\Middleware\DemoMiddleware.php:50
#35 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#36 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#37 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#38 \vendor\laravel\framework\src\Illuminate\Auth\Middleware\Authenticate.php:57
#39 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#40 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#41 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#42 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#44 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#45 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#47 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#49 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
  
[2025-06-10 09:35:04] local.INFO: Model: App\Models\Expense
Relation: staff
Num-Called: 84
Call-Stack:
#19 \app\Models\Expense.php:86
#24 \vendor\laravel\framework\src\Illuminate\Collections\Traits\EnumeratesValues.php:919
#27 \vendor\laravel\framework\src\Illuminate\Collections\Collection.php:778
#29 \vendor\laravel\framework\src\Illuminate\Collections\Traits\EnumeratesValues.php:919
#32 \app\Http\Controllers\PayrollController.php:208
#33 \vendor\laravel\framework\src\Illuminate\Routing\Controller.php:54
#34 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:43
#35 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:260
#36 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#37 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:806
#38 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:144
#39 \app\Http\Middleware\Status.php:28
#40 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#41 \app\Http\Middleware\CheckSchoolStatus.php:43
#42 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#43 \app\Http\Middleware\CheckRole.php:22
#44 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#45 \vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php:66
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#47 \app\Http\Middleware\LanguageManager.php:61
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#49 \app\Http\Middleware\DemoMiddleware.php:50
  
[2025-06-10 09:48:51] local.INFO: Detected N+1 Query  
[2025-06-10 09:48:51] local.INFO: Model: App\Models\Staff
Relation: App\Models\Expense
Num-Called: 21
Call-Stack:
#14 \vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php:23
#15 \vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php:52
#17 \app\Http\Controllers\PayrollController.php:254
#18 \vendor\laravel\framework\src\Illuminate\Routing\Controller.php:54
#19 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:43
#20 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:260
#21 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#22 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:806
#23 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:144
#24 \app\Http\Middleware\Status.php:28
#25 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#26 \app\Http\Middleware\CheckSchoolStatus.php:43
#27 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#28 \app\Http\Middleware\CheckRole.php:22
#29 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#30 \vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php:66
#31 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#32 \app\Http\Middleware\LanguageManager.php:61
#33 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#34 \app\Http\Middleware\DemoMiddleware.php:50
#35 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#36 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#37 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#38 \vendor\laravel\framework\src\Illuminate\Auth\Middleware\Authenticate.php:57
#39 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#40 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#41 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#42 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#44 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#45 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#47 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#49 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
  
[2025-06-10 09:48:51] local.INFO: Model: App\Models\Expense
Relation: staff
Num-Called: 84
Call-Stack:
#19 \app\Models\Expense.php:86
#24 \vendor\laravel\framework\src\Illuminate\Collections\Traits\EnumeratesValues.php:919
#27 \vendor\laravel\framework\src\Illuminate\Collections\Collection.php:778
#29 \vendor\laravel\framework\src\Illuminate\Collections\Traits\EnumeratesValues.php:919
#32 \app\Http\Controllers\PayrollController.php:208
#33 \vendor\laravel\framework\src\Illuminate\Routing\Controller.php:54
#34 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:43
#35 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:260
#36 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#37 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:806
#38 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:144
#39 \app\Http\Middleware\Status.php:28
#40 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#41 \app\Http\Middleware\CheckSchoolStatus.php:43
#42 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#43 \app\Http\Middleware\CheckRole.php:22
#44 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#45 \vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php:66
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#47 \app\Http\Middleware\LanguageManager.php:61
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#49 \app\Http\Middleware\DemoMiddleware.php:50
  
[2025-06-10 09:48:51] local.INFO: Detected N+1 Query  
[2025-06-10 09:48:51] local.INFO: Model: App\Models\Staff
Relation: App\Models\Expense
Num-Called: 21
Call-Stack:
#14 \vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php:23
#15 \vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php:52
#17 \app\Http\Controllers\PayrollController.php:254
#18 \vendor\laravel\framework\src\Illuminate\Routing\Controller.php:54
#19 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:43
#20 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:260
#21 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#22 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:806
#23 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:144
#24 \app\Http\Middleware\Status.php:28
#25 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#26 \app\Http\Middleware\CheckSchoolStatus.php:43
#27 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#28 \app\Http\Middleware\CheckRole.php:22
#29 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#30 \vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php:66
#31 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#32 \app\Http\Middleware\LanguageManager.php:61
#33 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#34 \app\Http\Middleware\DemoMiddleware.php:50
#35 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#36 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#37 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#38 \vendor\laravel\framework\src\Illuminate\Auth\Middleware\Authenticate.php:57
#39 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#40 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#41 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#42 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#44 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#45 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#47 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#49 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
  
[2025-06-10 09:48:51] local.INFO: Model: App\Models\Expense
Relation: staff
Num-Called: 84
Call-Stack:
#19 \app\Models\Expense.php:86
#24 \vendor\laravel\framework\src\Illuminate\Collections\Traits\EnumeratesValues.php:919
#27 \vendor\laravel\framework\src\Illuminate\Collections\Collection.php:778
#29 \vendor\laravel\framework\src\Illuminate\Collections\Traits\EnumeratesValues.php:919
#32 \app\Http\Controllers\PayrollController.php:208
#33 \vendor\laravel\framework\src\Illuminate\Routing\Controller.php:54
#34 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:43
#35 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:260
#36 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#37 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:806
#38 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:144
#39 \app\Http\Middleware\Status.php:28
#40 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#41 \app\Http\Middleware\CheckSchoolStatus.php:43
#42 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#43 \app\Http\Middleware\CheckRole.php:22
#44 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#45 \vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php:66
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#47 \app\Http\Middleware\LanguageManager.php:61
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#49 \app\Http\Middleware\DemoMiddleware.php:50
  
[2025-06-10 10:16:52] local.INFO: Detected N+1 Query  
[2025-06-10 10:16:52] local.INFO: Model: App\Models\Staff
Relation: App\Models\Expense
Num-Called: 21
Call-Stack:
#14 \vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php:23
#15 \vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php:52
#17 \app\Http\Controllers\PayrollController.php:254
#18 \vendor\laravel\framework\src\Illuminate\Routing\Controller.php:54
#19 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:43
#20 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:260
#21 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#22 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:806
#23 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:144
#24 \app\Http\Middleware\Status.php:28
#25 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#26 \app\Http\Middleware\CheckSchoolStatus.php:43
#27 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#28 \app\Http\Middleware\CheckRole.php:22
#29 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#30 \vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php:66
#31 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#32 \app\Http\Middleware\LanguageManager.php:61
#33 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#34 \app\Http\Middleware\DemoMiddleware.php:50
#35 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#36 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#37 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#38 \vendor\laravel\framework\src\Illuminate\Auth\Middleware\Authenticate.php:57
#39 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#40 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#41 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#42 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#44 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#45 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#47 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#49 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
  
[2025-06-10 10:16:52] local.INFO: Model: App\Models\Expense
Relation: staff
Num-Called: 84
Call-Stack:
#19 \app\Models\Expense.php:86
#24 \vendor\laravel\framework\src\Illuminate\Collections\Traits\EnumeratesValues.php:919
#27 \vendor\laravel\framework\src\Illuminate\Collections\Collection.php:778
#29 \vendor\laravel\framework\src\Illuminate\Collections\Traits\EnumeratesValues.php:919
#32 \app\Http\Controllers\PayrollController.php:208
#33 \vendor\laravel\framework\src\Illuminate\Routing\Controller.php:54
#34 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:43
#35 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:260
#36 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#37 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:806
#38 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:144
#39 \app\Http\Middleware\Status.php:28
#40 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#41 \app\Http\Middleware\CheckSchoolStatus.php:43
#42 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#43 \app\Http\Middleware\CheckRole.php:22
#44 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#45 \vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php:66
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#47 \app\Http\Middleware\LanguageManager.php:61
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#49 \app\Http\Middleware\DemoMiddleware.php:50
  
[2025-06-10 10:47:53] local.INFO: Detected N+1 Query  
[2025-06-10 10:47:53] local.INFO: Model: App\Models\Staff
Relation: App\Models\Expense
Num-Called: 21
Call-Stack:
#14 \vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php:23
#15 \vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php:52
#17 \app\Http\Controllers\PayrollController.php:257
#18 \vendor\laravel\framework\src\Illuminate\Routing\Controller.php:54
#19 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:43
#20 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:260
#21 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#22 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:806
#23 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:144
#24 \app\Http\Middleware\Status.php:28
#25 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#26 \app\Http\Middleware\CheckSchoolStatus.php:43
#27 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#28 \app\Http\Middleware\CheckRole.php:22
#29 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#30 \vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php:66
#31 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#32 \app\Http\Middleware\LanguageManager.php:61
#33 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#34 \app\Http\Middleware\DemoMiddleware.php:50
#35 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#36 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#37 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#38 \vendor\laravel\framework\src\Illuminate\Auth\Middleware\Authenticate.php:57
#39 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#40 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#41 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#42 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#44 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#45 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#47 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#49 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
  
[2025-06-10 10:47:53] local.INFO: Model: App\Models\Expense
Relation: staff
Num-Called: 84
Call-Stack:
#19 \app\Models\Expense.php:86
#24 \vendor\laravel\framework\src\Illuminate\Collections\Traits\EnumeratesValues.php:919
#27 \vendor\laravel\framework\src\Illuminate\Collections\Collection.php:778
#29 \vendor\laravel\framework\src\Illuminate\Collections\Traits\EnumeratesValues.php:919
#32 \app\Http\Controllers\PayrollController.php:211
#33 \vendor\laravel\framework\src\Illuminate\Routing\Controller.php:54
#34 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:43
#35 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:260
#36 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#37 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:806
#38 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:144
#39 \app\Http\Middleware\Status.php:28
#40 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#41 \app\Http\Middleware\CheckSchoolStatus.php:43
#42 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#43 \app\Http\Middleware\CheckRole.php:22
#44 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#45 \vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php:66
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#47 \app\Http\Middleware\LanguageManager.php:61
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#49 \app\Http\Middleware\DemoMiddleware.php:50
  
[2025-06-10 10:50:41] local.INFO: Detected N+1 Query  
[2025-06-10 10:50:41] local.INFO: Model: App\Models\Staff
Relation: App\Models\Expense
Num-Called: 21
Call-Stack:
#14 \vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php:23
#15 \vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php:52
#17 \app\Http\Controllers\PayrollController.php:257
#18 \vendor\laravel\framework\src\Illuminate\Routing\Controller.php:54
#19 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:43
#20 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:260
#21 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#22 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:806
#23 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:144
#24 \app\Http\Middleware\Status.php:28
#25 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#26 \app\Http\Middleware\CheckSchoolStatus.php:43
#27 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#28 \app\Http\Middleware\CheckRole.php:22
#29 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#30 \vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php:66
#31 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#32 \app\Http\Middleware\LanguageManager.php:61
#33 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#34 \app\Http\Middleware\DemoMiddleware.php:50
#35 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#36 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#37 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#38 \vendor\laravel\framework\src\Illuminate\Auth\Middleware\Authenticate.php:57
#39 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#40 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#41 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#42 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#44 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#45 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#47 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#49 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
  
[2025-06-10 10:50:41] local.INFO: Model: App\Models\Expense
Relation: staff
Num-Called: 84
Call-Stack:
#19 \app\Models\Expense.php:86
#24 \vendor\laravel\framework\src\Illuminate\Collections\Traits\EnumeratesValues.php:919
#27 \vendor\laravel\framework\src\Illuminate\Collections\Collection.php:778
#29 \vendor\laravel\framework\src\Illuminate\Collections\Traits\EnumeratesValues.php:919
#32 \app\Http\Controllers\PayrollController.php:211
#33 \vendor\laravel\framework\src\Illuminate\Routing\Controller.php:54
#34 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:43
#35 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:260
#36 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#37 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:806
#38 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:144
#39 \app\Http\Middleware\Status.php:28
#40 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#41 \app\Http\Middleware\CheckSchoolStatus.php:43
#42 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#43 \app\Http\Middleware\CheckRole.php:22
#44 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#45 \vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php:66
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#47 \app\Http\Middleware\LanguageManager.php:61
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#49 \app\Http\Middleware\DemoMiddleware.php:50
  
[2025-06-10 11:11:21] local.INFO: Detected N+1 Query  
[2025-06-10 11:11:21] local.INFO: Model: App\Models\Staff
Relation: App\Models\Expense
Num-Called: 21
Call-Stack:
#14 \vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php:23
#15 \vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php:52
#17 \app\Http\Controllers\PayrollController.php:257
#18 \vendor\laravel\framework\src\Illuminate\Routing\Controller.php:54
#19 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:43
#20 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:260
#21 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#22 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:806
#23 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:144
#24 \app\Http\Middleware\Status.php:28
#25 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#26 \app\Http\Middleware\CheckSchoolStatus.php:43
#27 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#28 \app\Http\Middleware\CheckRole.php:22
#29 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#30 \vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php:66
#31 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#32 \app\Http\Middleware\LanguageManager.php:61
#33 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#34 \app\Http\Middleware\DemoMiddleware.php:50
#35 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#36 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#37 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#38 \vendor\laravel\framework\src\Illuminate\Auth\Middleware\Authenticate.php:57
#39 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#40 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#41 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#42 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#44 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#45 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#47 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#49 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
  
[2025-06-10 11:11:21] local.INFO: Model: App\Models\Expense
Relation: staff
Num-Called: 84
Call-Stack:
#19 \app\Models\Expense.php:86
#24 \vendor\laravel\framework\src\Illuminate\Collections\Traits\EnumeratesValues.php:919
#27 \vendor\laravel\framework\src\Illuminate\Collections\Collection.php:778
#29 \vendor\laravel\framework\src\Illuminate\Collections\Traits\EnumeratesValues.php:919
#32 \app\Http\Controllers\PayrollController.php:211
#33 \vendor\laravel\framework\src\Illuminate\Routing\Controller.php:54
#34 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:43
#35 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:260
#36 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#37 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:806
#38 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:144
#39 \app\Http\Middleware\Status.php:28
#40 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#41 \app\Http\Middleware\CheckSchoolStatus.php:43
#42 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#43 \app\Http\Middleware\CheckRole.php:22
#44 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#45 \vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php:66
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#47 \app\Http\Middleware\LanguageManager.php:61
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#49 \app\Http\Middleware\DemoMiddleware.php:50
  
[2025-06-10 11:26:09] local.INFO: Detected N+1 Query  
[2025-06-10 11:26:09] local.INFO: Model: App\Models\Staff
Relation: App\Models\Expense
Num-Called: 21
Call-Stack:
#14 \vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php:23
#15 \vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php:52
#17 \app\Http\Controllers\PayrollController.php:257
#18 \vendor\laravel\framework\src\Illuminate\Routing\Controller.php:54
#19 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:43
#20 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:260
#21 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#22 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:806
#23 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:144
#24 \app\Http\Middleware\Status.php:28
#25 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#26 \app\Http\Middleware\CheckSchoolStatus.php:43
#27 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#28 \app\Http\Middleware\CheckRole.php:22
#29 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#30 \vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php:66
#31 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#32 \app\Http\Middleware\LanguageManager.php:61
#33 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#34 \app\Http\Middleware\DemoMiddleware.php:50
#35 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#36 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#37 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#38 \vendor\laravel\framework\src\Illuminate\Auth\Middleware\Authenticate.php:57
#39 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#40 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#41 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#42 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#44 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#45 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#47 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#49 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
  
[2025-06-10 11:26:09] local.INFO: Model: App\Models\Expense
Relation: staff
Num-Called: 84
Call-Stack:
#19 \app\Models\Expense.php:86
#24 \vendor\laravel\framework\src\Illuminate\Collections\Traits\EnumeratesValues.php:919
#27 \vendor\laravel\framework\src\Illuminate\Collections\Collection.php:778
#29 \vendor\laravel\framework\src\Illuminate\Collections\Traits\EnumeratesValues.php:919
#32 \app\Http\Controllers\PayrollController.php:211
#33 \vendor\laravel\framework\src\Illuminate\Routing\Controller.php:54
#34 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:43
#35 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:260
#36 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#37 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:806
#38 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:144
#39 \app\Http\Middleware\Status.php:28
#40 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#41 \app\Http\Middleware\CheckSchoolStatus.php:43
#42 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#43 \app\Http\Middleware\CheckRole.php:22
#44 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#45 \vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php:66
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#47 \app\Http\Middleware\LanguageManager.php:61
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:183
#49 \app\Http\Middleware\DemoMiddleware.php:50
  
