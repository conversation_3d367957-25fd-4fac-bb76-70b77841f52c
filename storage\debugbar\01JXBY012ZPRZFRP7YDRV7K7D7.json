{"__meta": {"id": "01JXBY012ZPRZFRP7YDRV7K7D7", "datetime": "2025-06-10 11:26:28", "utime": **********.448106, "method": "GET", "uri": "/credit/manage-credit", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.255281, "end": **********.448119, "duration": 3.192837953567505, "duration_str": "3.19s", "measures": [{"label": "Booting", "start": **********.255281, "relative_start": 0, "end": **********.504399, "relative_end": **********.504399, "duration": 0.*****************, "duration_str": "249ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.50441, "relative_start": 0.***************, "end": **********.44812, "relative_end": 1.1920928955078125e-06, "duration": 2.****************, "duration_str": "2.94s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.525545, "relative_start": 0.****************, "end": **********.53445, "relative_end": **********.53445, "duration": 0.008905172348022461, "duration_str": "8.91ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: credit.manage_credit", "start": **********.598578, "relative_start": 0.*****************, "end": **********.598578, "relative_end": **********.598578, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.master", "start": **********.736293, "relative_start": 0.*****************, "end": **********.736293, "relative_end": **********.736293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.include", "start": **********.74117, "relative_start": 0.****************, "end": **********.74117, "relative_end": **********.74117, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.header", "start": **********.749088, "relative_start": 0.****************, "end": **********.749088, "relative_end": **********.749088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sidebar", "start": 1749525986.442614, "relative_start": 1.187333106994629, "end": 1749525986.442614, "relative_end": 1749525986.442614, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: description_modal", "start": **********.404256, "relative_start": 3.148975133895874, "end": **********.404256, "relative_end": **********.404256, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.footer", "start": **********.406154, "relative_start": 3.1508729457855225, "end": **********.406154, "relative_end": **********.406154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.footer_js", "start": **********.408922, "relative_start": 3.1536409854888916, "end": **********.408922, "relative_end": **********.408922, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.446128, "relative_start": 3.1908469200134277, "end": **********.44633, "relative_end": **********.44633, "duration": 0.000202178955078125, "duration_str": "202μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 95386088, "peak_usage_str": "91MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.10", "Environment": "local", "Debug Mode": "Enabled", "URL": "schola.one", "Timezone": "Asia/Kuala_Lumpur", "Locale": "en"}}, "views": {"count": 8, "nb_templates": 8, "templates": [{"name": "credit.manage_credit", "param_count": null, "params": [], "start": **********.598549, "type": "blade", "hash": "bladeD:\\laragon\\www\\schola\\resources\\views/credit/manage_credit.blade.phpcredit.manage_credit", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fresources%2Fviews%2Fcredit%2Fmanage_credit.blade.php&line=1", "ajax": false, "filename": "manage_credit.blade.php", "line": "?"}}, {"name": "layouts.master", "param_count": null, "params": [], "start": **********.736267, "type": "blade", "hash": "bladeD:\\laragon\\www\\schola\\resources\\views/layouts/master.blade.phplayouts.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fresources%2Fviews%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}}, {"name": "layouts.include", "param_count": null, "params": [], "start": **********.741146, "type": "blade", "hash": "bladeD:\\laragon\\www\\schola\\resources\\views/layouts/include.blade.phplayouts.include", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fresources%2Fviews%2Flayouts%2Finclude.blade.php&line=1", "ajax": false, "filename": "include.blade.php", "line": "?"}}, {"name": "layouts.header", "param_count": null, "params": [], "start": **********.749066, "type": "blade", "hash": "bladeD:\\laragon\\www\\schola\\resources\\views/layouts/header.blade.phplayouts.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "layouts.sidebar", "param_count": null, "params": [], "start": 1749525986.442593, "type": "blade", "hash": "bladeD:\\laragon\\www\\schola\\resources\\views/layouts/sidebar.blade.phplayouts.sidebar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fresources%2Fviews%2Flayouts%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}}, {"name": "description_modal", "param_count": null, "params": [], "start": **********.404237, "type": "blade", "hash": "bladeD:\\laragon\\www\\schola\\resources\\views/description_modal.blade.phpdescription_modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fresources%2Fviews%2Fdescription_modal.blade.php&line=1", "ajax": false, "filename": "description_modal.blade.php", "line": "?"}}, {"name": "layouts.footer", "param_count": null, "params": [], "start": **********.406132, "type": "blade", "hash": "bladeD:\\laragon\\www\\schola\\resources\\views/layouts/footer.blade.phplayouts.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fresources%2Fviews%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}, {"name": "layouts.footer_js", "param_count": null, "params": [], "start": **********.4089, "type": "blade", "hash": "bladeD:\\laragon\\www\\schola\\resources\\views/layouts/footer_js.blade.phplayouts.footer_js", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fresources%2Fviews%2Flayouts%2Ffooter_js.blade.php&line=1", "ajax": false, "filename": "footer_js.blade.php", "line": "?"}}]}, "queries": {"count": 12, "nb_statements": 12, "nb_visible_statements": 12, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.060739999999999995, "accumulated_duration_str": "60.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 2 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.55558, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "schola", "explain": null, "start_percent": 0, "width_percent": 0.873}, {"sql": "select * from `schools` where `schools`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 13}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "Role", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckRole.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.561971, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "checkSchoolStatus:13", "source": {"index": 22, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FMiddleware%2FCheckSchoolStatus.php&line=13", "ajax": false, "filename": "CheckSchoolStatus.php", "line": "13"}, "connection": "schola", "explain": null, "start_percent": 0.873, "width_percent": 0.79}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (2) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 27}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "Role", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckRole.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.567876, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "schola", "explain": null, "start_percent": 1.663, "width_percent": 1.037}, {"sql": "select * from `classes` where `school_id` = 1 and `classes`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/Base/BaseRepository.php", "file": "D:\\laragon\\www\\schola\\app\\Repositories\\Base\\BaseRepository.php", "line": 40}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/CreditController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\CreditController.php", "line": 69}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.572297, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:40", "source": {"index": 15, "namespace": null, "name": "app/Repositories/Base/BaseRepository.php", "file": "D:\\laragon\\www\\schola\\app\\Repositories\\Base\\BaseRepository.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FRepositories%2FBase%2FBaseRepository.php&line=40", "ajax": false, "filename": "BaseRepository.php", "line": "40"}, "connection": "schola", "explain": null, "start_percent": 2.7, "width_percent": 1.893}, {"sql": "select * from `streams` where `streams`.`id` in (94)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/Base/BaseRepository.php", "file": "D:\\laragon\\www\\schola\\app\\Repositories\\Base\\BaseRepository.php", "line": 40}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/CreditController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\CreditController.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.576028, "duration": 0.0026, "duration_str": "2.6ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:40", "source": {"index": 20, "namespace": null, "name": "app/Repositories/Base/BaseRepository.php", "file": "D:\\laragon\\www\\schola\\app\\Repositories\\Base\\BaseRepository.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FRepositories%2FBase%2FBaseRepository.php&line=40", "ajax": false, "filename": "BaseRepository.php", "line": "40"}, "connection": "schola", "explain": null, "start_percent": 4.593, "width_percent": 4.281}, {"sql": "select `name`, `id` from `mediums` where `mediums`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/Base/BaseRepository.php", "file": "D:\\laragon\\www\\schola\\app\\Repositories\\Base\\BaseRepository.php", "line": 40}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/CreditController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\CreditController.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.581193, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:40", "source": {"index": 20, "namespace": null, "name": "app/Repositories/Base/BaseRepository.php", "file": "D:\\laragon\\www\\schola\\app\\Repositories\\Base\\BaseRepository.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FRepositories%2FBase%2FBaseRepository.php&line=40", "ajax": false, "filename": "BaseRepository.php", "line": "40"}, "connection": "schola", "explain": null, "start_percent": 8.874, "width_percent": 0.938}, {"sql": "select `name`, `id` from `classes` where `school_id` = 1 and `classes`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/CreditController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\CreditController.php", "line": 70}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.583931, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "CreditController.php:70", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/CreditController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\CreditController.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FCreditController.php&line=70", "ajax": false, "filename": "CreditController.php", "line": "70"}, "connection": "schola", "explain": null, "start_percent": 9.812, "width_percent": 1.284}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 304}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 174}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 175}], "start": **********.753005, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:266", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=266", "ajax": false, "filename": "PermissionRegistrar.php", "line": "266"}, "connection": "schola", "explain": null, "start_percent": 11.096, "width_percent": 1.465}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 613, 1259, 1260, 1261, 1262, 1263, 1265, 1266, 1267, 1268, 1269, 1270, 1271, 1272, 1273)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 304}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 174}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 175}], "start": **********.76215, "duration": 0.05121, "duration_str": "51.21ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:266", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=266", "ajax": false, "filename": "PermissionRegistrar.php", "line": "266"}, "connection": "schola", "explain": null, "start_percent": 12.562, "width_percent": 84.31}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 2 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [2, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": 1749525986.4280028, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "schola", "explain": null, "start_percent": 96.872, "width_percent": 0.955}, {"sql": "select * from `school_branch` where `school_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": "view", "name": "layouts.sidebar", "file": "D:\\laragon\\www\\schola\\resources\\views/layouts/sidebar.blade.php", "line": 110}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.307966, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "layouts.sidebar:110", "source": {"index": 14, "namespace": "view", "name": "layouts.sidebar", "file": "D:\\laragon\\www\\schola\\resources\\views/layouts/sidebar.blade.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fresources%2Fviews%2Flayouts%2Fsidebar.blade.php&line=110", "ajax": false, "filename": "sidebar.blade.php", "line": "110"}, "connection": "schola", "explain": null, "start_percent": 97.827, "width_percent": 1.169}, {"sql": "select * from `staffs` where `staffs`.`user_id` = 2 and `staffs`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "layouts.sidebar", "file": "D:\\laragon\\www\\schola\\resources\\views/layouts/sidebar.blade.php", "line": 910}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.3565178, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "layouts.sidebar:910", "source": {"index": 21, "namespace": "view", "name": "layouts.sidebar", "file": "D:\\laragon\\www\\schola\\resources\\views/layouts/sidebar.blade.php", "line": 910}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fresources%2Fviews%2Flayouts%2Fsidebar.blade.php&line=910", "ajax": false, "filename": "sidebar.blade.php", "line": "910"}, "connection": "schola", "explain": null, "start_percent": 98.996, "width_percent": 1.004}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 13675, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 227, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\ClassSchool": {"value": 28, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FClassSchool.php&line=1", "ajax": false, "filename": "ClassSchool.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\School": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FSchool.php&line=1", "ajax": false, "filename": "School.php", "line": "?"}}, "App\\Models\\Stream": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FStream.php&line=1", "ajax": false, "filename": "Stream.php", "line": "?"}}, "App\\Models\\Mediums": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FMediums.php&line=1", "ajax": false, "filename": "Mediums.php", "line": "?"}}}, "count": 13934, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 139, "messages": [{"message": "[\n  ability => class-teacher,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-182281382 data-indent-pad=\"  \"><span class=sf-dump-note>class-teacher </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">class-teacher</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-182281382\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749525986.43178, "xdebug_link": null}, {"message": "[\n  ability => medium-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-535144414 data-indent-pad=\"  \"><span class=sf-dump-note>medium-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">medium-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-535144414\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.3029, "xdebug_link": null}, {"message": "[\n  ability => subject-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1180521471 data-indent-pad=\"  \"><span class=sf-dump-note>subject-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">subject-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1180521471\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.303233, "xdebug_link": null}, {"message": "[\n  ability => class-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-676969864 data-indent-pad=\"  \"><span class=sf-dump-note>class-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">class-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-676969864\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.303546, "xdebug_link": null}, {"message": "[\n  ability => class-section-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1600608642 data-indent-pad=\"  \"><span class=sf-dump-note>class-section-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">class-section-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1600608642\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.303871, "xdebug_link": null}, {"message": "[\n  ability => subject-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1627399890 data-indent-pad=\"  \"><span class=sf-dump-note>subject-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">subject-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1627399890\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.304179, "xdebug_link": null}, {"message": "[\n  ability => semester-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-433021144 data-indent-pad=\"  \"><span class=sf-dump-note>semester-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">semester-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-433021144\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.305002, "xdebug_link": null}, {"message": "[\n  ability => stream-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-713528517 data-indent-pad=\"  \"><span class=sf-dump-note>stream-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">stream-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-713528517\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.305766, "xdebug_link": null}, {"message": "[\n  ability => shift-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2006308919 data-indent-pad=\"  \"><span class=sf-dump-note>shift-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">shift-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2006308919\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.306598, "xdebug_link": null}, {"message": "[\n  ability => promote-student-create,\n  target => transfer-student-create,\n  result => true,\n  user => 2,\n  arguments => [0 => transfer-student-create]\n]", "message_html": "<pre class=sf-dump id=sf-dump-762296595 data-indent-pad=\"  \"><span class=sf-dump-note>promote-student-create transfer-student-create</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">promote-student-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">transfer-student-create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; transfer-student-create]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-762296595\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.30721, "xdebug_link": null}, {"message": "[\n  ability => student-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1003287763 data-indent-pad=\"  \"><span class=sf-dump-note>student-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">student-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1003287763\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.307675, "xdebug_link": null}, {"message": "[\n  ability => student-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1518380296 data-indent-pad=\"  \"><span class=sf-dump-note>student-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">student-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1518380296\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.310547, "xdebug_link": null}, {"message": "[\n  ability => form-fields-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1247316861 data-indent-pad=\"  \"><span class=sf-dump-note>form-fields-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">form-fields-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1247316861\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.310974, "xdebug_link": null}, {"message": "[\n  ability => student-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-840127341 data-indent-pad=\"  \"><span class=sf-dump-note>student-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">student-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-840127341\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.311316, "xdebug_link": null}, {"message": "[\n  ability => student-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1354234794 data-indent-pad=\"  \"><span class=sf-dump-note>student-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">student-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1354234794\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.312456, "xdebug_link": null}, {"message": "[\n  ability => student-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-656966931 data-indent-pad=\"  \"><span class=sf-dump-note>student-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">student-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-656966931\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.313569, "xdebug_link": null}, {"message": "[\n  ability => class-report,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1381527773 data-indent-pad=\"  \"><span class=sf-dump-note>class-report </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">class-report</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1381527773\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.314598, "xdebug_link": null}, {"message": "[\n  ability => class-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1787957969 data-indent-pad=\"  \"><span class=sf-dump-note>class-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">class-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1787957969\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.314916, "xdebug_link": null}, {"message": "[\n  ability => student-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1320067675 data-indent-pad=\"  \"><span class=sf-dump-note>student-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">student-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1320067675\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.316273, "xdebug_link": null}, {"message": "[\n  ability => student-reset-password,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1094617455 data-indent-pad=\"  \"><span class=sf-dump-note>student-reset-password </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">student-reset-password</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1094617455\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.316828, "xdebug_link": null}, {"message": "[\n  ability => student-edit,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2040691567 data-indent-pad=\"  \"><span class=sf-dump-note>student-edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">student-edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2040691567\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.317303, "xdebug_link": null}, {"message": "[\n  ability => guardian-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1999967502 data-indent-pad=\"  \"><span class=sf-dump-note>guardian-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">guardian-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1999967502\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.317656, "xdebug_link": null}, {"message": "[\n  ability => teacher-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-335428000 data-indent-pad=\"  \"><span class=sf-dump-note>teacher-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">teacher-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-335428000\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.317996, "xdebug_link": null}, {"message": "[\n  ability => teacher-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1887386828 data-indent-pad=\"  \"><span class=sf-dump-note>teacher-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">teacher-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1887386828\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.318372, "xdebug_link": null}, {"message": "[\n  ability => timetable-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-534749016 data-indent-pad=\"  \"><span class=sf-dump-note>timetable-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">timetable-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-534749016\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.319945, "xdebug_link": null}, {"message": "[\n  ability => timetable-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1116972062 data-indent-pad=\"  \"><span class=sf-dump-note>timetable-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">timetable-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1116972062\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.320327, "xdebug_link": null}, {"message": "[\n  ability => timetable-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-951182538 data-indent-pad=\"  \"><span class=sf-dump-note>timetable-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">timetable-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-951182538\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.321733, "xdebug_link": null}, {"message": "[\n  ability => timetable-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-622941699 data-indent-pad=\"  \"><span class=sf-dump-note>timetable-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">timetable-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-622941699\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.323185, "xdebug_link": null}, {"message": "[\n  ability => holiday-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-305073136 data-indent-pad=\"  \"><span class=sf-dump-note>holiday-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">holiday-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-305073136\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.32369, "xdebug_link": null}, {"message": "[\n  ability => holiday-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2141206495 data-indent-pad=\"  \"><span class=sf-dump-note>holiday-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">holiday-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2141206495\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.32499, "xdebug_link": null}, {"message": "[\n  ability => lesson-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1063651244 data-indent-pad=\"  \"><span class=sf-dump-note>lesson-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">lesson-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1063651244\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.326311, "xdebug_link": null}, {"message": "[\n  ability => lesson-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1985920091 data-indent-pad=\"  \"><span class=sf-dump-note>lesson-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">lesson-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1985920091\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.327544, "xdebug_link": null}, {"message": "[\n  ability => topic-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-822796802 data-indent-pad=\"  \"><span class=sf-dump-note>topic-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">topic-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-822796802\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.328856, "xdebug_link": null}, {"message": "[\n  ability => assignment-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1592969141 data-indent-pad=\"  \"><span class=sf-dump-note>assignment-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">assignment-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1592969141\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.330178, "xdebug_link": null}, {"message": "[\n  ability => assignment-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-24715846 data-indent-pad=\"  \"><span class=sf-dump-note>assignment-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">assignment-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-24715846\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.331495, "xdebug_link": null}, {"message": "[\n  ability => assignment-submission,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1224976774 data-indent-pad=\"  \"><span class=sf-dump-note>assignment-submission </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">assignment-submission</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1224976774\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.332885, "xdebug_link": null}, {"message": "[\n  ability => slider-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-74731831 data-indent-pad=\"  \"><span class=sf-dump-note>slider-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">slider-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-74731831\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.333352, "xdebug_link": null}, {"message": "[\n  ability => notification-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1884693144 data-indent-pad=\"  \"><span class=sf-dump-note>notification-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">notification-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1884693144\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.334122, "xdebug_link": null}, {"message": "[\n  ability => class-teacher,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1043388837 data-indent-pad=\"  \"><span class=sf-dump-note>class-teacher </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">class-teacher</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1043388837\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.334687, "xdebug_link": null}, {"message": "[\n  ability => class-teacher,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1951573489 data-indent-pad=\"  \"><span class=sf-dump-note>class-teacher </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">class-teacher</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1951573489\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.335467, "xdebug_link": null}, {"message": "[\n  ability => class-teacher,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-565684096 data-indent-pad=\"  \"><span class=sf-dump-note>class-teacher </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">class-teacher</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-565684096\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.336034, "xdebug_link": null}, {"message": "[\n  ability => class-teacher,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-566988030 data-indent-pad=\"  \"><span class=sf-dump-note>class-teacher </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">class-teacher</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-566988030\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.337068, "xdebug_link": null}, {"message": "[\n  ability => class-teacher,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1331511772 data-indent-pad=\"  \"><span class=sf-dump-note>class-teacher </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">class-teacher</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1331511772\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.337855, "xdebug_link": null}, {"message": "[\n  ability => class-teacher,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2051144548 data-indent-pad=\"  \"><span class=sf-dump-note>class-teacher </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">class-teacher</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2051144548\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.338496, "xdebug_link": null}, {"message": "[\n  ability => class-teacher,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1123272119 data-indent-pad=\"  \"><span class=sf-dump-note>class-teacher </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">class-teacher</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1123272119\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.339095, "xdebug_link": null}, {"message": "[\n  ability => class-teacher,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-599157140 data-indent-pad=\"  \"><span class=sf-dump-note>class-teacher </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">class-teacher</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-599157140\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.339654, "xdebug_link": null}, {"message": "[\n  ability => class-teacher,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1015429498 data-indent-pad=\"  \"><span class=sf-dump-note>class-teacher </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">class-teacher</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1015429498\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.340614, "xdebug_link": null}, {"message": "[\n  ability => class-teacher,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-695192246 data-indent-pad=\"  \"><span class=sf-dump-note>class-teacher </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">class-teacher</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-695192246\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.341259, "xdebug_link": null}, {"message": "[\n  ability => attendance-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-460185977 data-indent-pad=\"  \"><span class=sf-dump-note>attendance-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">attendance-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-460185977\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.341705, "xdebug_link": null}, {"message": "[\n  ability => attendance-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-753645173 data-indent-pad=\"  \"><span class=sf-dump-note>attendance-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">attendance-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-753645173\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.342158, "xdebug_link": null}, {"message": "[\n  ability => announcement-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1376085869 data-indent-pad=\"  \"><span class=sf-dump-note>announcement-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">announcement-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1376085869\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.343424, "xdebug_link": null}, {"message": "[\n  ability => exam-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-995472337 data-indent-pad=\"  \"><span class=sf-dump-note>exam-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">exam-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-995472337\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.343942, "xdebug_link": null}, {"message": "[\n  ability => exam-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1333229832 data-indent-pad=\"  \"><span class=sf-dump-note>exam-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">exam-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1333229832\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.344407, "xdebug_link": null}, {"message": "[\n  ability => exam-upload-marks,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1916177084 data-indent-pad=\"  \"><span class=sf-dump-note>exam-upload-marks </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">exam-upload-marks</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1916177084\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.344871, "xdebug_link": null}, {"message": "[\n  ability => exam-result,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-124979405 data-indent-pad=\"  \"><span class=sf-dump-note>exam-result </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">exam-result</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-124979405\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.345368, "xdebug_link": null}, {"message": "[\n  ability => grade-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-985946606 data-indent-pad=\"  \"><span class=sf-dump-note>grade-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">grade-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-985946606\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.345861, "xdebug_link": null}, {"message": "[\n  ability => online-exam-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-428380070 data-indent-pad=\"  \"><span class=sf-dump-note>online-exam-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">online-exam-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-428380070\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.347278, "xdebug_link": null}, {"message": "[\n  ability => online-exam-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1889719338 data-indent-pad=\"  \"><span class=sf-dump-note>online-exam-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">online-exam-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1889719338\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.348589, "xdebug_link": null}, {"message": "[\n  ability => online-exam-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-473802713 data-indent-pad=\"  \"><span class=sf-dump-note>online-exam-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">online-exam-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-473802713\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.349978, "xdebug_link": null}, {"message": "[\n  ability => fees-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-574919394 data-indent-pad=\"  \"><span class=sf-dump-note>fees-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">fees-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-574919394\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.350792, "xdebug_link": null}, {"message": "[\n  ability => fees-type-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-667782221 data-indent-pad=\"  \"><span class=sf-dump-note>fees-type-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">fees-type-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-667782221\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.35138, "xdebug_link": null}, {"message": "[\n  ability => fees-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-815796499 data-indent-pad=\"  \"><span class=sf-dump-note>fees-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">fees-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-815796499\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.35205, "xdebug_link": null}, {"message": "[\n  ability => fees-paid,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1339382675 data-indent-pad=\"  \"><span class=sf-dump-note>fees-paid </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">fees-paid</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1339382675\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.352931, "xdebug_link": null}, {"message": "[\n  ability => fees-type-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1654981809 data-indent-pad=\"  \"><span class=sf-dump-note>fees-type-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">fees-type-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1654981809\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.353548, "xdebug_link": null}, {"message": "[\n  ability => schools-list,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1086882290 data-indent-pad=\"  \"><span class=sf-dump-note>schools-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">schools-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1086882290\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.35874, "xdebug_link": null}, {"message": "[\n  ability => schools-create,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-987348112 data-indent-pad=\"  \"><span class=sf-dump-note>schools-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">schools-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-987348112\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.359123, "xdebug_link": null}, {"message": "[\n  ability => schools-edit,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-692055358 data-indent-pad=\"  \"><span class=sf-dump-note>schools-edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">schools-edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-692055358\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.359477, "xdebug_link": null}, {"message": "[\n  ability => schools-delete,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1810265752 data-indent-pad=\"  \"><span class=sf-dump-note>schools-delete </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">schools-delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1810265752\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.359814, "xdebug_link": null}, {"message": "[\n  ability => package-list,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-915059494 data-indent-pad=\"  \"><span class=sf-dump-note>package-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">package-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-915059494\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.360703, "xdebug_link": null}, {"message": "[\n  ability => package-create,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1031709963 data-indent-pad=\"  \"><span class=sf-dump-note>package-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">package-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1031709963\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.361081, "xdebug_link": null}, {"message": "[\n  ability => package-edit,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1529979932 data-indent-pad=\"  \"><span class=sf-dump-note>package-edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">package-edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1529979932\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.361468, "xdebug_link": null}, {"message": "[\n  ability => package-delete,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-718479478 data-indent-pad=\"  \"><span class=sf-dump-note>package-delete </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">package-delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-718479478\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.361828, "xdebug_link": null}, {"message": "[\n  ability => package-list,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-663147179 data-indent-pad=\"  \"><span class=sf-dump-note>package-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">package-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-663147179\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.362207, "xdebug_link": null}, {"message": "[\n  ability => package-create,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2110745844 data-indent-pad=\"  \"><span class=sf-dump-note>package-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">package-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2110745844\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.362577, "xdebug_link": null}, {"message": "[\n  ability => package-edit,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-959112544 data-indent-pad=\"  \"><span class=sf-dump-note>package-edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">package-edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-959112544\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.362953, "xdebug_link": null}, {"message": "[\n  ability => package-delete,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1742507007 data-indent-pad=\"  \"><span class=sf-dump-note>package-delete </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">package-delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1742507007\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.363327, "xdebug_link": null}, {"message": "[\n  ability => addons-list,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2096709485 data-indent-pad=\"  \"><span class=sf-dump-note>addons-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">addons-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2096709485\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.363707, "xdebug_link": null}, {"message": "[\n  ability => addons-create,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2033097749 data-indent-pad=\"  \"><span class=sf-dump-note>addons-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">addons-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2033097749\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.364093, "xdebug_link": null}, {"message": "[\n  ability => addons-edit,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1033740109 data-indent-pad=\"  \"><span class=sf-dump-note>addons-edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">addons-edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1033740109\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.364474, "xdebug_link": null}, {"message": "[\n  ability => addons-delete,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1502584771 data-indent-pad=\"  \"><span class=sf-dump-note>addons-delete </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">addons-delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1502584771\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.364858, "xdebug_link": null}, {"message": "[\n  ability => addons-list,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-436556285 data-indent-pad=\"  \"><span class=sf-dump-note>addons-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">addons-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-436556285\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.365235, "xdebug_link": null}, {"message": "[\n  ability => addons-create,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1146123937 data-indent-pad=\"  \"><span class=sf-dump-note>addons-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">addons-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1146123937\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.365654, "xdebug_link": null}, {"message": "[\n  ability => addons-edit,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1278512225 data-indent-pad=\"  \"><span class=sf-dump-note>addons-edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">addons-edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1278512225\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.366034, "xdebug_link": null}, {"message": "[\n  ability => addons-delete,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1401523685 data-indent-pad=\"  \"><span class=sf-dump-note>addons-delete </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">addons-delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1401523685\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.366453, "xdebug_link": null}, {"message": "[\n  ability => package-list,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-361879952 data-indent-pad=\"  \"><span class=sf-dump-note>package-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">package-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-361879952\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.366836, "xdebug_link": null}, {"message": "[\n  ability => package-create,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-791756526 data-indent-pad=\"  \"><span class=sf-dump-note>package-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">package-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-791756526\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.367273, "xdebug_link": null}, {"message": "[\n  ability => package-edit,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-252290645 data-indent-pad=\"  \"><span class=sf-dump-note>package-edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">package-edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-252290645\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.367721, "xdebug_link": null}, {"message": "[\n  ability => package-delete,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-784885977 data-indent-pad=\"  \"><span class=sf-dump-note>package-delete </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">package-delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-784885977\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.36821, "xdebug_link": null}, {"message": "[\n  ability => subscription-view,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1161830938 data-indent-pad=\"  \"><span class=sf-dump-note>subscription-view </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">subscription-view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1161830938\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.368855, "xdebug_link": null}, {"message": "[\n  ability => expense-category-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1252165322 data-indent-pad=\"  \"><span class=sf-dump-note>expense-category-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">expense-category-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1252165322\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.36973, "xdebug_link": null}, {"message": "[\n  ability => expense-category-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1166783356 data-indent-pad=\"  \"><span class=sf-dump-note>expense-category-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">expense-category-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1166783356\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.370381, "xdebug_link": null}, {"message": "[\n  ability => expense-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1623317436 data-indent-pad=\"  \"><span class=sf-dump-note>expense-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">expense-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1623317436\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.371049, "xdebug_link": null}, {"message": "[\n  ability => payroll-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-240871197 data-indent-pad=\"  \"><span class=sf-dump-note>payroll-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">payroll-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-240871197\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.372295, "xdebug_link": null}, {"message": "[\n  ability => payroll-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1480663848 data-indent-pad=\"  \"><span class=sf-dump-note>payroll-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">payroll-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1480663848\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.372995, "xdebug_link": null}, {"message": "[\n  ability => payroll-settings-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-405340487 data-indent-pad=\"  \"><span class=sf-dump-note>payroll-settings-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">payroll-settings-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-405340487\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.374113, "xdebug_link": null}, {"message": "[\n  ability => gallery-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1822283229 data-indent-pad=\"  \"><span class=sf-dump-note>gallery-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">gallery-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1822283229\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.375207, "xdebug_link": null}, {"message": "[\n  ability => certificate-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-169563695 data-indent-pad=\"  \"><span class=sf-dump-note>certificate-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">certificate-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-169563695\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.375997, "xdebug_link": null}, {"message": "[\n  ability => certificate-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-211400213 data-indent-pad=\"  \"><span class=sf-dump-note>certificate-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">certificate-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-211400213\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.376713, "xdebug_link": null}, {"message": "[\n  ability => certificate-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1149312386 data-indent-pad=\"  \"><span class=sf-dump-note>certificate-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">certificate-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1149312386\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.377465, "xdebug_link": null}, {"message": "[\n  ability => certificate-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1388856959 data-indent-pad=\"  \"><span class=sf-dump-note>certificate-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">certificate-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1388856959\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.378216, "xdebug_link": null}, {"message": "[\n  ability => id-card-settings,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-872691302 data-indent-pad=\"  \"><span class=sf-dump-note>id-card-settings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">id-card-settings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-872691302\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.378951, "xdebug_link": null}, {"message": "[\n  ability => student-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-704439364 data-indent-pad=\"  \"><span class=sf-dump-note>student-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">student-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-704439364\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.380336, "xdebug_link": null}, {"message": "[\n  ability => staff-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2127798279 data-indent-pad=\"  \"><span class=sf-dump-note>staff-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">staff-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2127798279\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.381003, "xdebug_link": null}, {"message": "[\n  ability => role-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-4584830 data-indent-pad=\"  \"><span class=sf-dump-note>role-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">role-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-4584830\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.38139, "xdebug_link": null}, {"message": "[\n  ability => role-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1219539763 data-indent-pad=\"  \"><span class=sf-dump-note>role-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">role-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1219539763\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.381707, "xdebug_link": null}, {"message": "[\n  ability => staff-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1429242297 data-indent-pad=\"  \"><span class=sf-dump-note>staff-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">staff-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1429242297\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.382363, "xdebug_link": null}, {"message": "[\n  ability => staff-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2038473856 data-indent-pad=\"  \"><span class=sf-dump-note>staff-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">staff-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2038473856\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.383141, "xdebug_link": null}, {"message": "[\n  ability => approve-leave,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1138939140 data-indent-pad=\"  \"><span class=sf-dump-note>approve-leave </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">approve-leave</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1138939140\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.384243, "xdebug_link": null}, {"message": "[\n  ability => approve-leave,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-873749035 data-indent-pad=\"  \"><span class=sf-dump-note>approve-leave </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">approve-leave</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-873749035\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.384932, "xdebug_link": null}, {"message": "[\n  ability => faqs-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1347959952 data-indent-pad=\"  \"><span class=sf-dump-note>faqs-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">faqs-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1347959952\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.385744, "xdebug_link": null}, {"message": "[\n  ability => web-settings,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-163083985 data-indent-pad=\"  \"><span class=sf-dump-note>web-settings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">web-settings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-163083985\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.386445, "xdebug_link": null}, {"message": "[\n  ability => school-web-settings,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-437283243 data-indent-pad=\"  \"><span class=sf-dump-note>school-web-settings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">school-web-settings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-437283243\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.387175, "xdebug_link": null}, {"message": "[\n  ability => class-group-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2102344285 data-indent-pad=\"  \"><span class=sf-dump-note>class-group-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">class-group-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2102344285\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.38796, "xdebug_link": null}, {"message": "[\n  ability => faqs-create,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-185023515 data-indent-pad=\"  \"><span class=sf-dump-note>faqs-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">faqs-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-185023515\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.388682, "xdebug_link": null}, {"message": "[\n  ability => app-settings,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-712881993 data-indent-pad=\"  \"><span class=sf-dump-note>app-settings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">app-settings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-712881993\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.389244, "xdebug_link": null}, {"message": "[\n  ability => language-list,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1019202432 data-indent-pad=\"  \"><span class=sf-dump-note>language-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">language-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1019202432\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.389625, "xdebug_link": null}, {"message": "[\n  ability => school-setting-manage,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-597849551 data-indent-pad=\"  \"><span class=sf-dump-note>school-setting-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">school-setting-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-597849551\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.390155, "xdebug_link": null}, {"message": "[\n  ability => app-settings,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1383047975 data-indent-pad=\"  \"><span class=sf-dump-note>app-settings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">app-settings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1383047975\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.390657, "xdebug_link": null}, {"message": "[\n  ability => school-setting-manage,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2051979589 data-indent-pad=\"  \"><span class=sf-dump-note>school-setting-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">school-setting-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2051979589\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.391191, "xdebug_link": null}, {"message": "[\n  ability => student-id-card-settings,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1593967513 data-indent-pad=\"  \"><span class=sf-dump-note>student-id-card-settings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">student-id-card-settings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1593967513\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.39204, "xdebug_link": null}, {"message": "[\n  ability => system-setting-manage,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-237976375 data-indent-pad=\"  \"><span class=sf-dump-note>system-setting-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">system-setting-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-237976375\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.39251, "xdebug_link": null}, {"message": "[\n  ability => subscription-settings,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1594401422 data-indent-pad=\"  \"><span class=sf-dump-note>subscription-settings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">subscription-settings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1594401422\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.393139, "xdebug_link": null}, {"message": "[\n  ability => guidance-create,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-169322482 data-indent-pad=\"  \"><span class=sf-dump-note>guidance-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">guidance-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-169322482\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.393809, "xdebug_link": null}, {"message": "[\n  ability => guidance-list,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1654193451 data-indent-pad=\"  \"><span class=sf-dump-note>guidance-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">guidance-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1654193451\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.394418, "xdebug_link": null}, {"message": "[\n  ability => guidance-edit,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-190778127 data-indent-pad=\"  \"><span class=sf-dump-note>guidance-edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">guidance-edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-190778127\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.395024, "xdebug_link": null}, {"message": "[\n  ability => guidance-delete,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1598569445 data-indent-pad=\"  \"><span class=sf-dump-note>guidance-delete </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">guidance-delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1598569445\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.395685, "xdebug_link": null}, {"message": "[\n  ability => language-list,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1676365881 data-indent-pad=\"  \"><span class=sf-dump-note>language-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">language-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1676365881\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.396103, "xdebug_link": null}, {"message": "[\n  ability => fcm-setting-manage,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1908645667 data-indent-pad=\"  \"><span class=sf-dump-note>fcm-setting-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">fcm-setting-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1908645667\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.396705, "xdebug_link": null}, {"message": "[\n  ability => school-setting-manage,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1882817673 data-indent-pad=\"  \"><span class=sf-dump-note>school-setting-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">school-setting-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1882817673\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.397251, "xdebug_link": null}, {"message": "[\n  ability => email-setting-create,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-412491819 data-indent-pad=\"  \"><span class=sf-dump-note>email-setting-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">email-setting-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-412491819\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.397803, "xdebug_link": null}, {"message": "[\n  ability => email-setting-create,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-660876279 data-indent-pad=\"  \"><span class=sf-dump-note>email-setting-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">email-setting-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-660876279\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.398296, "xdebug_link": null}, {"message": "[\n  ability => email-template,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-870542269 data-indent-pad=\"  \"><span class=sf-dump-note>email-template </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">email-template</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-870542269\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.399405, "xdebug_link": null}, {"message": "[\n  ability => school-setting-manage,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1503095158 data-indent-pad=\"  \"><span class=sf-dump-note>school-setting-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">school-setting-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1503095158\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.400385, "xdebug_link": null}, {"message": "[\n  ability => system-setting-manage,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-733104424 data-indent-pad=\"  \"><span class=sf-dump-note>system-setting-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">system-setting-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-733104424\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.401281, "xdebug_link": null}, {"message": "[\n  ability => privacy-policy,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1955741992 data-indent-pad=\"  \"><span class=sf-dump-note>privacy-policy </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">privacy-policy</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1955741992\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.401893, "xdebug_link": null}, {"message": "[\n  ability => contact-us,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-862711437 data-indent-pad=\"  \"><span class=sf-dump-note>contact-us </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">contact-us</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-862711437\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.402363, "xdebug_link": null}, {"message": "[\n  ability => about-us,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1020946307 data-indent-pad=\"  \"><span class=sf-dump-note>about-us </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">about-us</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1020946307\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.40281, "xdebug_link": null}, {"message": "[\n  ability => terms-condition,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1814350354 data-indent-pad=\"  \"><span class=sf-dump-note>terms-condition </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">terms-condition</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1814350354\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.403258, "xdebug_link": null}, {"message": "[\n  ability => school-terms-condition,\n  target => null,\n  result => null,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1527352335 data-indent-pad=\"  \"><span class=sf-dump-note>school-terms-condition </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">school-terms-condition</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1527352335\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.40383, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "https://schola.test/credit/manage-credit", "action_name": "credit.manage-credit", "controller_action": "App\\Http\\Controllers\\CreditController@manageCreditIndex", "uri": "GET credit/manage-credit/{id?}", "controller": "App\\Http\\Controllers\\CreditController@manageCreditIndex<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FCreditController.php&line=66\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/credit", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FCreditController.php&line=66\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/CreditController.php:66-92</a>", "middleware": "web, Role, auth, checkSchoolStatus, status, language", "duration": "3.19s", "peak_memory": "96MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1148252729 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1148252729\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-131787456 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-131787456\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-253267885 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">schola.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">https://schola.test/credit/view-credit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"742 characters\">XSRF-TOKEN=eyJpdiI6InhWU290Nk9KMFFNZmlZeWZVNHpxTXc9PSIsInZhbHVlIjoiemFKTmZkVXFsaXRpd25FMzVFbEZCelZYQ3lQNUUxTkQ3WnBnMEx6NVVPSGFXREh4WkI0WFRQVjVDSkhXVnNnc2FPWUtZdWREZjF1dkVaQmp2ZC9zQ255b0ZCcElvalFWV3p5aUVYQWZZR3NZU3lvZVRiN25ZdTBKU0ZLMnhMSEkiLCJtYWMiOiI1MTdlZjEzMWNjZDAzMDQ2ZDc4MjJkYTI4ODQ4MDM4MWVlMzI5ZDEwZWRjZDBlNmQ1MmUwZGU5Y2Y0ZDAzNDJjIiwidGFnIjoiIn0%3D; schola_saas_school_management_system_session=eyJpdiI6IjEvbktidGJWWm5vSXdFZkc4K3dCcFE9PSIsInZhbHVlIjoiMms3ZUxSZVZKQjRXN2tSWEhBWDYxSVBCZ2pQekxEV2kvYnB3YXoxODdyTThobitZK2ZyUTBpMDhCL3Rta1FBS0pQb3E0L1BkTWMxS3RUTVpGRVIybkJldzhqcHZVMDVFTG5DRmd2RmRPd1diVUJNdnJHbTlUSXRXbmRwRTAyQ28iLCJtYWMiOiJmOTBmYzhjNTBhNmM4ZmIxZmM3MGQwYzhhNThkZWI0MmRlMzA4MGE1OWRiM2Y5N2MxYmNhNTdiMDY3ZTZkOTk2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-253267885\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1857202630 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0nsyLuN5aAz6u8lgTG1r68KyFeDp1NRQbXnBoOH</span>\"\n  \"<span class=sf-dump-key>schola_saas_school_management_system_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IQrLeRaEfTugtoRzBcuJ4wOlCeh4PG6VwaBH418U</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1857202630\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-122662685 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 10 Jun 2025 03:26:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-122662685\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-243918822 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0nsyLuN5aAz6u8lgTG1r68KyFeDp1NRQbXnBoOH</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">https://schola.test/credit/view-credit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>landing_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>language</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Language\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Language</span></span> {<a class=sf-dump-ref>#3429</a><samp data-depth=2 class=sf-dump-compact>\n    #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">languages</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n    +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n    #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n    +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n    +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n    +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">English</span>\"\n      \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"7 characters\">en.json</span>\"\n      \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>is_rtl</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">English</span>\"\n      \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"7 characters\">en.json</span>\"\n      \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>is_rtl</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n    +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n    +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">code</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">is_rtl</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n    </samp>]\n  </samp>}\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1749518683</span>\n  </samp>]\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-243918822\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://schola.test/credit/manage-credit", "action_name": "credit.manage-credit", "controller_action": "App\\Http\\Controllers\\CreditController@manageCreditIndex"}, "badge": null}}