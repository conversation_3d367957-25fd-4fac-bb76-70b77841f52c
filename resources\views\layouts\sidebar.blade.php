<!-- partial:../../partials/_sidebar.html -->
<nav class="sidebar sidebar-offcanvas" id="sidebar">
    <ul class="nav">
        {{-- dashboard --}}
        <li class="nav-item">
            <a href="{{ url('/dashboard') }}" class="nav-link">
                <i class="fa fa-home menu-icon"></i>
                <span class="menu-title">{{ __('dashboard') }}</span>
            </a>
        </li>
        {{-- Academics --}}
        @canany(['medium-create', 'section-create', 'subject-create', 'class-create', 'subject-create',
            'promote-student-create', 'transfer-student-create'])
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#academics-menu" aria-expanded="false"
                    aria-controls="academics-menu">
                    <i class="fa fa-university menu-icon"></i>
                    <span class="menu-title">{{ __('academics') }}</span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="academics-menu">
                    <ul class="nav flex-column sub-menu">
                        @if (false)
                            @can('medium-create')
                                <li class="nav-item"><a href="{{ route('mediums.index') }}" class="nav-link">
                                        {{ __('medium') }} {{ __('(optional)') }}</a></li>
                            @endcan

                            @can('section-create')
                                <li class="nav-item"><a href="{{ route('section.index') }}" class="nav-link">
                                        {{ __('section') }} {{ __('(optional)') }}</a></li>
                            @endcan
                        @endif

                        @can('subject-create')
                            <li class="nav-item"><a href="{{ route('subject.index') }}" class="nav-link"> {{ __('subject') }}
                                </a></li>

                        @endcan

                        @can('class-create')
                        <li class="nav-item"><a href="{{ route('class.index') }}" class="nav-link"> {{ __('Class') }}
                                </a></li>
                        <li class="nav-item"><a href="{{ route('class.subject.index') }}" class="nav-link">
                        {{ __('Class Subject') }} </a></li>
                        @endcan

                        @can('class-section-create')
                            <li class="nav-item"><a href="{{ route('class-section.index') }}"
                                    class="nav-link">{{ __('Class Section & Teachers') }} </a></li>
                        @endcan

                        @can('subject-create')
                            <li class="nav-item"><a href="{{ route('commission.index') }}" class="nav-link">
                                    {{ __('subject_fees_per_section') }}</a></li>
                            <!-- <li class="nav-item"><a href="{{ route('subject-students.index') }}" class="nav-link">
                                    {{ __('Subject Students') }} </a></li> -->
                        @endcan

                        @can('semester-create')
                            <li class="nav-item"><a href="{{ route('semester.index') }}" class="nav-link">
                                    {{ __('semester') }} {{ __('(optional)') }}</a></li>
                        @endcan

                        @can('stream-create')
                            <li class="nav-item"><a class="nav-link" href="{{ route('stream.index') }}"> {{ __('Stream') }}
                                    {{ __('(optional)') }}</a></li>
                        @endcan

                        @can('shift-create')
                            <li class="nav-item"><a class="nav-link" href="{{ route('shift.index') }}"> {{ __('Shift') }}
                                    {{ __('(optional)') }}</a></li>
                        @endcan

                        @canany('promote-student-create', 'transfer-student-create')
                            <li class="nav-item"><a href="{{ route('promote-student.index') }}"
                                    class="nav-link text-wrap">{{ __('Transfer & Promote Students') }}</a></li>
                        @endcan

                        @can('student-create')
                            <li class="nav-item"><a href="{{ route('students.roll-number.index') }}"
                                    class="nav-link">{{ __('assign') }} {{ __('roll_no') }}</a></li>
                        @endcan
                    </ul>
                </div>
            </li>
        @endcanany

        {{-- Class Section For Teacher --}}
        @role('Teacher')
        @if(false)
            <li class="nav-item">
                <a class="nav-link" href="{{ route('class-section.index') }}">
                    <i class="fa fa-sticky-note menu-icon"></i>
                    <span class="menu-title"> {{ __('Class Section') }} </span>
                </a>
            </li>
        @endif
        @endrole

        {{-- SchoolBranch --}}
        @hasanyrole(['School Admin'])
            @php
                $userSchoolBranch = DB::table('school_branch')
                    ->where('school_id', Auth::user()->school_id)
                    ->first();
            @endphp
            @if ($userSchoolBranch)
                <li class="nav-item">
                    <a class="nav-link" href="{{ route('branch.index') }}">
                        <i class="fa fa-building-o menu-icon"></i>
                        <span class="menu-title"> {{ __('School Branch') }} </span>
                    </a>
                </li>
            @endif
        @endrole

        {{-- Student Progress --}}
        @hasanyrole(['School Admin', 'Teacher'])
            <li class="nav-item">
                <a class="nav-link" href="{{ route('students.student-progress') }}" data-access="@hasFeatureAccess('Student Progress')">
                    <i class="fa fa-list-alt menu-icon"></i>
                    <span class="menu-title"> {{ __('Student Progress') }} </span>
                </a>
            </li>
        @endrole

        {{-- student --}}
        @canany(['student-create', 'student-list', 'student-reset-password', 'class-teacher', 'form-fields-list',
            'form-fields-create', 'form-fields-edit', 'form-fields-delete', 'guardian-create'])
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#student-menu" aria-expanded="false"
                    aria-controls="academics-menu">
                    <i class="fa fa-graduation-cap menu-icon"></i>
                    <span class="menu-title">{{ __('students') }}</span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="student-menu">
                    <ul class="nav flex-column sub-menu">
                        {{-- Student Addmission Form Manage --}}
                        @canany(['form-fields-list', 'form-fields-create', 'form-fields-edit', 'form-fields-delete'])
                            <li class="nav-item">
                                <a href="{{ route('form-fields.index') }}"
                                    class="nav-link">{{ __('admission_form_fields') }}</i></a>
                            </li>
                        @endcan
                        @can('student-create')
                            <li class="nav-item"><a href="{{ route('students.create') }}"
                                    class="nav-link">{{ __('student_admission') }}</a></li>
                        @endcan

                        @canany(['student-list', 'class-teacher'])
                            <li class="nav-item"><a href="{{ route('students.index') }}"
                                    class="nav-link">{{ __('student_details') }}</a></li>
                        @endcanany

                        @canany(['student-list', 'class-teacher'])
                            <li class="nav-item"><a href="{{ route('students.student_subject-index') }}"
                                    class="nav-link">{{ __('student_subject') }}</a></li>
                        @endcanany
                        
                        @canany(['class-report','class-create'])
                            <li class="nav-item"><a href="{{ route('class-report.index') }}"
                                    class="nav-link">{{ __('Class Report') }}</a></li>
                        @endcanany

                        <!-- @canany(['student-list', 'class-teacher'])
                                                                    <li class="nav-item"><a href="{{ route('students.generate-id-card-index') }}" class="nav-link" data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('ID Card - Certificate Generation')">{{ __('generate_id_card') }}</a></li>
                                            @endcanany -->
                        @can('student-reset-password')
                            <li class="nav-item"><a href="{{ route('students.reset-password.index') }}"
                                    class="nav-link">{{ __('students') . ' ' . __('reset_password') }}</a></li>
                        @endcan
                        @if (Auth::user()->hasRole('School Admin'))
                            <li class="nav-item"><a href="{{ route('students.create-bulk-data') }}"
                                    class="nav-link">{{ __('add_bulk_data') }}</a></li>
                        @endif

                        @can('student-edit')
                            <li class="nav-item"><a href="{{ route('students.upload-profile') }}"
                                    class="nav-link">{{ __('upload_profile_images') }}</a></li>
                        @endcan

                        {{-- parents --}}
                        @can('guardian-create')
                            <li class="nav-item">
                                <a href="{{ route('guardian.index') }}" class="nav-link"> {{ __('Guardian') }} </a>
                            </li>
                        @endcan
                    </ul>
                </div>
            </li>
        @endcanany

        {{-- teacher --}}
        @can('teacher-list')
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#teacher-menu" aria-expanded="false"
                    aria-controls="academics-menu">
                    <i class="fa fa-user menu-icon"></i>
                    <span class="menu-title">{{ __('teacher') }}</span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="teacher-menu">
                    <ul class="nav flex-column sub-menu">
                        {{-- Teacher Registration --}}
                        <li class="nav-item">
                            <a href="{{ route('teachers.index') }}" class="nav-link">
                                <span class="menu-title">{{ __('manage_teacher') }}</span>
                            </a>
                        </li>
                        @can('teacher-create')
                        <li class="nav-item">
                            <a href="{{ route('teachers.create-bulk-upload') }}" class="nav-link">
                                <span class="menu-title">{{ __('bulk upload') }}</span>
                            </a>
                        </li>
                        @endcan
                    </ul>
                </div>
            </li>
        @endcan

        {{-- deposits --}}
        @hasanyrole(['School Admin'])
        <li class="nav-item">
            <a class="nav-link" data-toggle="collapse" href="#deposits-menu" aria-expanded="false"
                aria-controls="deposits-menu" data-access="@hasFeatureAccess('Deposit Management')">
                <i class="fa fa-money menu-icon"></i>
                <span class="menu-title">{{ __('Deposits') }}</span>
                <i class="menu-arrow"></i>
            </a>
            <div class="collapse" id="deposits-menu">
                <ul class="nav flex-column sub-menu">
                    <li class="nav-item">
                        <a href="{{ route('deposit-types.index') }}" class="nav-link" data-access="@hasFeatureAccess('Deposit Management')">
                            {{ __('Deposit Types') }}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{{ route('student-deposits.index') }}" class="nav-link" data-access="@hasFeatureAccess('Deposit Management')">
                            {{ __('Student Deposits') }}
                        </a>
                    </li>
                </ul>
            </div>
        </li>
        @endrole
        
        {{-- reward system --}}
        @hasanyrole(['School Admin', 'Teacher'])
            <li class="nav-item">
                <!-- <a class="nav-link" data-toggle="collapse" href="#reward-menu" aria-expanded="false" aria-controls="academics-menu"> -->
                <a class="nav-link" aria-expanded="false" data-toggle="collapse" href="#reward-menu" data-access="@hasFeatureAccess('Reward Management')">
                    <i class="fa fa-solid fa-gift menu-icon"></i>
                    <span class="menu-title">{{ __('reward_system') }}</span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="reward-menu">
                    <ul class="nav flex-column sub-menu">
                        {{--Manage Reward--}}
                        <li class="nav-item">
                            <a href="{{route('reward.index')}}" class="nav-link"  data-access="@hasFeatureAccess('Reward Management')">
                                <span class="menu-title">{{ __('Manage Reward System') }}</span>
                            </a>
                        </li> 
                        {{--Redeem Reward--}}
                        <li class="nav-item">
                            <a href="{{route('reward-redeem.index')}}" class="nav-link"  data-access="@hasFeatureAccess('Reward Management')">
                                <span class="menu-title">{{ __('redeem_gift') }}</span>
                            </a>
                        </li>
                        {{--Academic Performance Setting--}}
                        <li class="nav-item">
                            <a href="{{route('reward-category.index')}}" class="nav-link" data-access="@hasFeatureAccess('Reward Management')">
                                <span class="menu-title">{{ __('setting') }}</span>
                            </a>
                        </li>                       
                    </ul>
                </div>
            </li> 
        @endrole
        
        {{--Booking System--}}
        @hasanyrole(['School Admin', 'Teacher'])
        <li class="nav-item">
                    <a href="#booking-menu" class="nav-link"  data-toggle="collapse"  data-access="@hasFeatureAccess('Booking Management')">
                        <i class="fa-solid fa-calendar-days menu-icon"></i>
                        <span class="menu-title">{{ __('Booking System') }}</span>
                        <i class="menu-arrow"></i>
                        
                    </a>
                    <div class="collapse" id="booking-menu">
                    <ul class="nav flex-column sub-menu">                       
                        {{--Academic Performance Setting--}}
                        <li class="nav-item">
                        <a href="{{route('booking.index')}}" class="nav-link" data-access="@hasFeatureAccess('Booking Management')">
                                <span class="menu-title">{{ __('Add Booking System') }}</span>
                            </a>
                            <a href="{{route('bookingsetting.index')}}" class="nav-link" data-access="@hasFeatureAccess('Booking Management')">
                                <span class="menu-title">{{ __('setting') }}</span>
                            </a>
                            
                        </li>
                       
                    </ul>

                </div>
                </li>

        @endrole

        {{-- timetable --}}
        @if (Auth::user()->hasRole('Teacher'))
            <li class="nav-item">
                <a href="{{ route('timetable.teacher.show', Auth::user()->id) }}" class="nav-link"
                    data-access="@hasFeatureAccess('Timetable Management')">
                    <i class="fa fa-calendar menu-icon"></i>
                    <span class="menu-title">{{ __('timetable') }}</span>
                </a>
            </li>
        @else
            @canany(['timetable-create', 'timetable-list'])
                <li class="nav-item">
                    <a class="nav-link" data-toggle="collapse" href="#timetable-menu" aria-expanded="false"
                        aria-controls="timetable-menu" data-access="@hasFeatureAccess('Timetable Management')">
                        <i class="fa fa-calendar menu-icon"></i>
                        <span class="menu-title">{{ __('timetable') }}</span>
                        <i class="menu-arrow"></i>
                    </a>

                    <div class="collapse" id="timetable-menu">
                        <ul class="nav flex-column sub-menu">
                            @can('timetable-create')
                                <li class="nav-item">
                                    <a href="{{ route('timetable.index') }}" class="nav-link"
                                        data-name="{{ Auth::user()->getRoleNames()[0] }}"
                                        data-access="@hasFeatureAccess('Timetable Management')">{{ __('create_timetable') }} </a>
                                </li>
                            @endcan

                            @can('timetable-list')
                                <li class="nav-item">
                                    <a href="{{ route('timetable.teacher.index') }}" class="nav-link"
                                        data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Timetable Management')">
                                        {{ __('teacher_timetable') }}
                                    </a>
                                </li>
                            @endcan
                            @can('timetable-list')
                                <li class="nav-item">
                                    <a href="{{ route('timetable.timetable_configurations') }}" class="nav-link"
                                        data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Timetable Management')">
                                        {{ __('timetable_configurations') }}
                                    </a>
                                </li>
                            @endcan
                        </ul>
                    </div>
                </li>
            @endcanany
        @endif

        {{-- Holiday --}}
        @canany(['holiday-create', 'holiday-list'])
            <li class="nav-item">
                @can('holiday-list')
                    <a href="{{ route('holiday.index') }}" class="nav-link"
                        data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Holiday Management')">
                        <i class="fa fa-calendar-check-o menu-icon"></i>
                        <span class="menu-title">{{ __('holiday_list') }}</span>
                    </a>
                @endcan
            </li>
        @endcanany
        {{-- subject lesson --}}
        @canany(['lesson-list', 'lesson-create', 'lesson-edit', 'lesson-delete', 'topic-list', 'topic-create',
            'topic-edit', 'topic-delete'])
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#subject-lesson-menu" aria-expanded="false"
                    aria-controls="subject-lesson-menu" data-access="@hasFeatureAccess('Lesson Management')">
                    <i class="fa fa-book menu-icon"></i>
                    <span class="menu-title">{{ __('subject_lesson') }}</span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="subject-lesson-menu">
                    <ul class="nav flex-column sub-menu">
                        @canany(['lesson-list', 'lesson-create', 'lesson-edit', 'lesson-delete'])
                            <li class="nav-item">
                                <a href="{{ url('lesson') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Lesson Management')">
                                    {{ __('create_lesson') }}</a>
                            </li>
                        @endcanany

                        @canany(['topic-list', 'topic-create', 'topic-edit', 'topic-delete'])
                            <li class="nav-item">
                                <a href="{{ url('lesson-topic') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Lesson Management')">
                                    {{ __('create_topic') }}</a>
                            </li>
                        @endcanany
                    </ul>
                </div>
            </li>
        @endcanany

        {{-- student assignment --}}
        @canany(['assignment-create', 'assignment-submission'])
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#student-assignment-menu" aria-expanded="false"
                    aria-controls="student-assignment-menu" data-access="@hasFeatureAccess('Assignment Management')">
                    <i class="fa fa-tasks menu-icon"></i>
                    <span class="menu-title">{{ __('student_assignment') }}</span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="student-assignment-menu">
                    <ul class="nav flex-column sub-menu">
                        @can('assignment-create')
                            <li class="nav-item">
                                <a href="{{ route('assignment.index') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Assignment Management')">
                                    {{ __('create_assignment') }}
                                </a>
                            </li>
                        @endcan
                        @can('assignment-submission')
                            <li class="nav-item">
                                <a href="{{ route('assignment.submission') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Assignment Management')">
                                    {{ __('assignment_submission') }}
                                </a>
                            </li>
                        @endcan
                    </ul>
                </div>
            </li>
        @endcanany

        {{-- Slider --}}
        @can('slider-create')
            <li class="nav-item">
                <a href="{{ route('sliders.index') }}" class="nav-link"
                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Slider Management')">
                    <i class="fa fa-list menu-icon"></i>
                    <span class="menu-title">{{ __('sliders') }}</span>
                </a>
            </li>
        @endcan

        @canany(['notification-create', 'notification-list', 'notification-delete'])
            <li class="nav-item">
                <a href="{{ route('notifications.index') }}" class="nav-link"
                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Announcement Management')">
                    <i class="fa fa-bell menu-icon"></i>
                    <span class="menu-title">{{ __('notification') }}</span>
                </a>
            </li>
        @endcanany

        {{-- Student Attendance --}}
        @canany(['class-teacher','attendance-list','attendance-create','attendance-edit','attendance-delete'])
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#attendance-menu" data-access="@hasFeatureAccess('Attendance Management')" aria-expanded="false"
                   aria-controls="attendance-menu">
                   <i class="fa fa-check menu-icon"></i>
                   <span class="menu-title">{{ __('Student Attendance') }}</span>
                   <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="attendance-menu">
                    <ul class="nav flex-column sub-menu">
                        @canany(['class-teacher', 'attendance-create'])
                            <li class="nav-item">
                                <a href="{{ route('attendance.index') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Attendance Management')">
                                    {{ __('add_attendance') }}
                                </a>
                            </li>
                        @endcan

                        @canany(['class-teacher', 'attendance-create'])
                            <li class="nav-item">
                                <a href="{{ route('attendance.subject') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Attendance Management')">
                                    {{ __('Subject') }} {{ __('attendance') }}
                                </a>
                            </li>
                        @endcan

                        {{-- view attendance --}}
                        @canany(['class-teacher', 'attendance-list'])
                            <li class="nav-item">
                                <a href="{{ route('attendance.view') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Attendance Management')">
                                    {{ __('view_attendance') }}
                                </a>
                            </li>
                        @endcan
                        {{-- scan attendance --}}
                        @canany(['class-teacher', 'attendance-list'])
                            <li class="nav-item">
                                <a href="{{ route('attendance.scan') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Attendance Management')">
                                    {{ __('scan') }} {{ __('attendance') }}
                                </a>
                            </li>
                        @endcan

                        @canany(['class-teacher', 'attendance-list'])
                            <li class="nav-item">
                                <a href="{{ route('attendance.daily-attendance-report') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Attendance Management')">
                                    {{ __('daily') }} {{ __('attendance') }} {{ __('report') }}
                                </a>
                            </li>
                        @endcan

                        {{-- Student Clock In / Out Attendance Report --}}
                        @canany(['class-teacher', 'attendance-list'])
                            <li class="nav-item">
                                <a href="{{ route('attendance.student-attendance-report') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Attendance Management')">
                                    {{ __('student') }} {{ __('attendance') }} {{ __('report') }}
                                </a>
                            </li>
                        @endcan
                        
                        @canany(['class-teacher', 'attendance-list'])
                             <li class="nav-item">
                                 <a href="{{ route('attendance.summary-attendance-report') }}" class="nav-link"
                                     data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Attendance Management')">
                                     {{ __('Attendance') }} {{ __('Summary') }} 
                                 </a>
                             </li>
                         @endcan

                    </ul>
                </div>
            </li>
        @endcanany

        {{-- teacher Attendance --}}
        @canany(['class-teacher','attendance-list','attendance-create','attendance-edit','attendance-delete'])
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#teacher_attendance-menu" data-access="@hasFeatureAccess('Attendance Management')" aria-expanded="false"
                   aria-controls="teacher_attendance-menu">
                   <i class="fa fa-check menu-icon"></i>
                   <span class="menu-title">{{ __('Teacher Attendance') }}</span>
                   <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="teacher_attendance-menu">
                    <ul class="nav flex-column sub-menu">

                        {{-- scan attendance --}}
                        @canany(['class-teacher','attendance-list'])
                            <li class="nav-item">
                                <a href="{{ route('attendance.teacher_scan') }}" class="nav-link" data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Attendance Management')">
                                    {{ __('scan') }} {{ __('attendance') }}
                                </a>
                            </li>
                        @endcan

                        @canany(['attendance-list'])
                            <li class="nav-item">
                                <a href="{{ route('attendance.daily-teacher-attendance-report') }}" class="nav-link" data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Attendance Management')">
                                    {{ __('daily') }} {{ __('attendance') }} {{ __('report') }}
                                </a>
                            </li>
                        @endcan

                        {{-- Teacher Clock In / Out Attendance Report --}}
                        @canany(['attendance-list'])
                            <li class="nav-item">
                                <a href="{{ route('attendance.teacher-attendance-report') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Attendance Management')">
                                    {{ __('teacher') }} {{ __('attendance') }} {{ __('report') }}
                                </a>
                            </li>
                        @endcan
                    </ul>
                </div>
            </li>
        @endcanany

        {{-- announceent --}}
        @can('announcement-list')
            <li class="nav-item">
                <a href="{{ route('announcement.index') }}" class="nav-link"
                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Announcement Management')">
                    <i class="fa fa-bullhorn menu-icon"></i>
                    <span class="menu-title">{{ __('announcement') }}</span>
                </a>
            </li>
        @endcan

        {{-- exam --}}
        @canany(['exam-create', 'exam-upload-marks', 'grade-create', 'exam-result'])
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#exam-menu" aria-expanded="false"
                    aria-controls="exam-menu" data-access="@hasFeatureAccess('Exam Management')">
                    <i class="fa fa-book menu-icon"></i>
                    <span class="menu-title">{{ __('Offline Exam') }}</span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="exam-menu">
                    <ul class="nav flex-column sub-menu">
                        @can('exam-create')
                            <li class="nav-item">
                                <a href="{{ route('exams.index') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Exam Management')">
                                    {{ __('manage_offline_exam') }}
                                </a>
                            </li>
                        @endcan
                        @can('exam-upload-marks')
                            <li class="nav-item">
                                <a href="{{ route('exams.timetable') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Exam Management')">
                                    {{ __('timetable') }}
                                </a>
                            </li>

                            <li class="nav-item">
                                <a href="{{ route('exams.upload-marks') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Exam Management')">
                                    {{ __('upload') }} {{ __('Exam Marks') }}
                                </a>
                            </li>
                        @endcan
                        @can('exam-result')
                            <li class="nav-item">
                                <a href="{{ route('exams.get-result') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Exam Management')">
                                    {{ __('offline_exam_result') }}
                                </a>
                            </li>
                        @endcan

                        @can('grade-create')
                            <li class="nav-item">
                                <a href="{{ route('exam.grade.index') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Exam Management')">
                                    {{ __('exam_grade') }}
                                </a>
                            </li>
                        @endcan
                    </ul>
                </div>
            </li>
        @endcan

        {{-- Online Exam --}}
        @canany(['online-exam-create', 'online-exam-list', 'online-exam-edit', 'online-exam-delete'])
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#online-exam-menu" aria-expanded="false"
                    aria-controls="online-exam-menu" data-access="@hasFeatureAccess('Exam Management')">
                    <i class="fa fa-laptop menu-icon"></i>
                    <span class="menu-title">{{ __('online_exam') }}</span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="online-exam-menu">
                    <ul class="nav flex-column sub-menu">
                        @can('online-exam-list')
                            <li class="nav-item">

                                <a href="{{ route('online-exam.index') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Exam Management')">
                                    {{ __('manage_online_exam') }}
                                </a>
                            </li>
                        @endcan
                        @can('online-exam-create')
                            <li class="nav-item">
                                <a href="{{ route('online-exam-question.index') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Exam Management')">
                                    {{ __('manage_questions') }}
                                </a>
                            </li>
                        @endcan
                    </ul>
                </div>
            </li>
        @endcanany

        {{-- Fees --}}
        @canany(['fees-list', 'fees-type-list', 'fees-classes-list', 'fees-paid'])
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#fees-menu" aria-expanded="false"
                    aria-controls="fees-menu" data-access="@hasFeatureAccess('Fees Management')">
                    <i class="fa fa-dollar menu-icon"></i>
                    <span class="menu-title">{{ __('Fees') }}</span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="fees-menu">
                    <ul class="nav flex-column sub-menu">
                        @can('fees-type-list')
                            @if (false)
                                <li class="nav-item">
                                    <a href="{{ route('fees-type.index') }}" class="nav-link"
                                        data-access="@hasFeatureAccess('Fees Management')"> {{ __('Fees Type') }}
                                    </a>
                                </li>
                            @endif
                            
                            <li class="nav-item">
                                <a href="{{ route('student-fee-types.item-code') }}" class="nav-link" data-access="@hasFeatureAccess('Fees Management')"> {{ __('manage_item_code') }}
                                </a>
                            </li>

                            <li class="nav-item">
                                <a href="{{ route('student-fee-types.index') }}" class="nav-link"
                                    data-access="@hasFeatureAccess('Fees Management')"> {{ __('Student Fees Type') }}
                                </a>
                            </li>
                        @endcan
                        @can('fees-list')
                            @if (false)
                                <li class="nav-item">
                                    <a href="{{ route('fees.index') }}" class="nav-link" data-access="@hasFeatureAccess('Fees Management')">
                                        {{ __('Manage Fees') }}</a>
                                </li>
                            @endif
                            <li class="nav-item">
                                <a href="{{ route('student-fees.index') }}" class="nav-link"
                                    data-access="@hasFeatureAccess('Fees Management')"> {{ __('Manage Student Fees') }}</a>
                            </li>
                            <li class="nav-item">
                                <a href="{{ route('student-fee.recurring-fees') }}" class="nav-link"
                                    data-access="@hasFeatureAccess('Fees Management')"> {{ __('Manage Recurring Fees') }}</a>
                            </li>
                        @endcan
                        @can('fees-paid')
                            @if (false)
                                <li class="nav-item">
                                    <a href="{{ route('fees.paid.index') }}" class="nav-link"
                                        data-access="@hasFeatureAccess('Fees Management')"> {{ __('Fees Paid') }}
                                    </a>
                                </li>
                            @endif
                            <li class="nav-item">
                                <a href="{{ route('student-fees.paid.index') }}" class="nav-link"
                                    data-access="@hasFeatureAccess('Fees Management')"> {{ __('Student Fees Paid') }}
                                </a>
                            </li>
                        @endcan
                        @can('fees-type-list')
                            <li class="nav-item">
                                <a href="{{ route('student-fee-types.credit-note') }}" class="nav-link"
                                    data-access="@hasFeatureAccess('Fees Management')"> {{ __('Manage Credit Note') }}
                                </a>
                                <a href="{{ route('student-fee-types.debit-note') }}" class="nav-link"
                                    data-access="@hasFeatureAccess('Fees Management')"> {{ __('Manage Debit Note') }}
                                </a>
                                <a href="{{ route('student-fee-types.refund-note') }}" class="nav-link"
                                    data-access="@hasFeatureAccess('Fees Management')"> {{ __('Manage Refund Note') }}
                                </a>
                            </li>
                        @endcan
                        @if (false)
                            @can('fees-paid')
                                <li class="nav-item">
                                    <a href="{{ route('fees.transactions.log.index') }}" class="nav-link"
                                        data-access="@hasFeatureAccess('Fees Management')"> {{ __('Fees Transaction Logs') }}
                                    </a>
                                </li>
                            @endcan
                        @endif
                    </ul>
                </div>
            </li>
        @endcan

        @hasanyrole(['School Admin'])
        {{-- Self Billing --}}
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#selfbilling-menu" aria-expanded="false"
                    aria-controls="selfbilling-menu" data-access="@hasFeatureAccess('E-Invoicing')">
                    <i class="fa-solid fa-money-bill menu-icon"></i>
                    <span class="menu-title">{{ __('self_billing') }}</span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="selfbilling-menu">
                    <ul class="nav flex-column sub-menu">
                        <li class="nav-item">
                            <a href="{{ route('supplier.index') }}" class="nav-link"
                                data-access="@hasFeatureAccess('E-Invoicing')"> {{ __('supplier') }}
                            </a>
                        </li>
                            
                        <li class="nav-item">
                            <a href="{{ route('self-billing.index') }}" class="nav-link" data-access="@hasFeatureAccess('E-Invoicing')"> {{ __('self_billing') }}
                            </a>
                        </li>
                    </ul>
                </div>
            </li>

        {{-- Statement --}}
            <li class="nav-item">
                
                <a href="{{ route('statement.index') }}" class="nav-link" data-access="@hasFeatureAccess('Statement Management')">  
                    <i class="fa fa-file-alt menu-icon"></i> 
                    <span class="menu-title">{{__('statement') }}</span>
                </a>
            </li>
        @endrole

        {{-- Package --}}
        @hasrole(['School Admin'])
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#subject-package-menu" aria-expanded="false"
                    aria-controls="subject-package-menu" data-name="{{ Auth::user()->getRoleNames()[0] }}"
                    data-access="@hasFeatureAccess('Package Management')">
                    <i class="fa fa-archive menu-icon" aria-hidden="true"></i>
                    <span class="menu-title">{{ __('package') }}</span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="subject-package-menu">
                    <ul class="nav flex-column sub-menu">
                        <li class="nav-item">
                            <a href="{{ route('subject-package.index') }}" class="nav-link"
                                data-name="{{ Auth::user()->getRoleNames()[0] }}"
                                data-access="@hasFeatureAccess('Package Management')">{{ __('package') }} </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ route('subject-package.purchase') }}" class="nav-link"
                                data-name="{{ Auth::user()->getRoleNames()[0] }}"
                                data-access="@hasFeatureAccess('Package Management')">{{ __('assign_package') }} </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ route('subject-package.usage') }}" class="nav-link"
                                data-name="{{ Auth::user()->getRoleNames()[0] }}"
                                data-access="@hasFeatureAccess('Package Management')">{{ __('usage') }} </a>
                        </li>
                    </ul>
                </div>
            </li>
        @endrole

        {{-- Leave --}}
        @hasanyrole(['Admin', 'Staff', 'Teacher'])
            @canany(['leave-list', 'leave-create', 'leave-edit', 'leave-delete'])
                <li class="nav-item">
                    <a class="nav-link" data-toggle="collapse" href="#staff-leave-menu" data-access="@hasFeatureAccess('Staff Leave Management')"
                        aria-expanded="false" aria-controls="staff-leave-menu">
                        <i class="fa fa-plane menu-icon"></i>
                        <span class="menu-title">{{ __('leaves') }}</span>
                        <i class="menu-arrow"></i>
                    </a>
                    <div class="collapse" id="staff-leave-menu">
                        <ul class="nav flex-column sub-menu">
                            <li class="nav-item">
                                <a href="{{ route('leave.index') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Staff Leave Management')">
                                    {{ __('apply_leave') }}
                                </a>
                            </li>

                            <li class="nav-item">
                                <a href="{{ route('leave.leave_report') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Staff Leave Management')">
                                    {{ __('leave_report') }}
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>
            @endcanany
        @endrole

        @if (Auth::user()->school_id && Auth::user()->staff)
            <li class="nav-item">
                <a href="{{ route('payroll.slip.index') }}" class="nav-link"
                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Expense Management')">
                    <i class="fa fa-money menu-icon"></i>
                    <span class="menu-title">{{ __('payroll') }} {{ __('slips') }}</span>
                </a>
            </li>
        @endif

        {{-- Schools --}}
        @canany(['schools-list', 'schools-create', 'schools-edit', 'schools-delete'])
            <li class="nav-item">
                <a href="{{ route('schools.index') }}" class="nav-link">
                    <i class="fa fa-university menu-icon"></i>
                    <span class="menu-title">{{ __('schools') }}</span>
                </a>
            </li>
        @endcanany

        @hasanyrole(['School Admin'])
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#report-menu" aria-expanded="false"
                    aria-controls="fees-menu" data-access="@hasFeatureAccess('Reporting')">
                    <i class="fa fa-file menu-icon"></i>
                    <span class="menu-title">{{ __('Reports') }}</span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="report-menu">
                    <ul class="nav flex-column sub-menu">

                        <li class="nav-item">
                            <a href="{{ route('student-fees.fees-collection-report.index') }}" class="nav-link"
                                data-access="@hasFeatureAccess('Reporting')"> {{ __('Fees Collection Report') }}
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="{{ route('student-fees.fees-pending-report.index') }}" class="nav-link"
                                data-access="@hasFeatureAccess('Reporting')"> {{ __('Pending Fees Report') }}
                            </a>
                        </li>

                        <!-- <li class="nav-item">
                            <a href="{{ route('student-fees.fees-outstanding-report.index') }}" class="nav-link"
                                data-access="@hasFeatureAccess('Class Report')"> {{ __('Outstanding Fees Report') }}
                            </a>
                        </li> -->

                        <li class="nav-item">
                            <a href="{{ route('student-fees.fees-student-report.index') }}" class="nav-link"
                                data-access="@hasFeatureAccess('Reporting')"> {{ __('Student Fees Report') }}
                            </a>
                        </li>

                        <!-- <li class="nav-item">
                            <a href="{{ route('student-fees.fees-type-report.index') }}" class="nav-link"
                                data-access="@hasFeatureAccess('Class Report')"> {{ __('Student Fees Type Report') }}
                            </a>
                        </li> -->

                        <!-- <li class="nav-item">
                            <a href="{{ route('student-fees.fees-class-report.index') }}" class="nav-link"
                                data-access="@hasFeatureAccess('Class Report')"> {{ __('Class Fees Report') }}
                            </a>
                        </li> -->

                        <li class="nav-item">
                            <a href="{{ route('financial-report.index') }}" class="nav-link"
                                data-access="@hasFeatureAccess('Reporting')"> {{ __('Financial Report') }}
                            </a>
                        </li>

                            <!-- <li class="nav-item">
                                <a href="{{ route('student-fees.fees-all-report.index') }}" class="nav-link" data-access="@hasFeatureAccess('Class Report')"> {{__('All Transaction Report') }}
                                </a>
                            </li> -->
                    </ul>
                </div>
            </li>
        @endrole

      

        {{-- package --}}
        @canany(['package-list', 'package-create', 'package-edit', 'package-delete'])
            <li class="nav-item">
                <a href="{{ route('package.index') }}" class="nav-link">
                <i class="fa fa-receipt menu-icon"></i>
                    <span class="menu-title">{{ __('package') }}</span>
                </a>
            </li>
        @endcan

        {{-- Announcement --}}
        @canany(['package-list', 'package-create', 'package-edit', 'package-delete'])
            <li class="nav-item">
                <a href="{{ route('announcement-news.index') }}" class="nav-link">
                    <i class="fa fa-bullhorn menu-icon"></i>
                    <span class="menu-title">{{ __('announcement') }}</span>
                </a>
            </li>
        @endcan
        {{-- package --}}
        @canany(['addons-list', 'addons-create', 'addons-edit', 'addons-delete'])
            <li class="nav-item">
                <a href="{{ route('addons.index') }}" class="nav-link">
                    <i class="fa fa-puzzle-piece menu-icon"></i>
                    <span class="menu-title">{{ __('addons') }}</span>
                </a>
            </li>
        @endcan

        {{-- Features list --}}
        @canany(['addons-list', 'addons-create', 'addons-edit', 'addons-delete', 'package-list', 'package-create',
            'package-edit', 'package-delete'])
            <li class="nav-item">
                <a href="{{ url('features') }}" class="nav-link">
                    <i class="fa fa-list-ul menu-icon"></i>
                    <span class="menu-title">{{ __('features') }}</span>
                </a>
            </li>
        @endcan

        {{-- subscription-view --}}
        @can('subscription-view')
            <li class="nav-item">
                <a href="{{ url('subscriptions/report') }}" class="nav-link">
                    <i class="fa fa-puzzle-piece menu-icon"></i>
                    <span class="menu-title">{{ __('subscription') }}</span>
                </a>
            </li>

            <li class="nav-item">
                <a href="{{ url('subscriptions/transactions') }}" class="nav-link">
                    <i class="fa fa-money menu-icon"></i>
                    <span class="menu-title">{{ __('subscription_transaction') }}</span>
                </a>
            </li>
        @endcan

        {{-- Expense --}}
        @canany(['expense-category-create', 'expense-category-list', 'expense-category-edit', 'expense-category-delete',
            'expense-create', 'expense-list', 'expense-edit', 'expense-delete'])
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#expense-menu" aria-expanded="false"
                    aria-controls="expense-menu" data-access="@hasFeatureAccess('Expense Management')">
                    <i class="fa fa-money menu-icon"></i>
                    <span class="menu-title">{{ __('expense') }}</span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="expense-menu">
                    <ul class="nav flex-column sub-menu">
                        @canany(['expense-category-create', 'expense-category-list', 'expense-category-edit',
                            'expense-category-delete'])
                            <li class="nav-item">
                                <a href="{{ route('expense-category.index') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}"
                                    data-access="@hasFeatureAccess('Expense Management')">{{ __('manage_category') }} </a>
                            </li>
                        @endcanany

                        @canany(['expense-create', 'expense-list', 'expense-edit', 'expense-delete'])
                            <li class="nav-item">
                                <a href="{{ route('expense.index') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Expense Management')">
                                    {{ __('manage_expense') }}
                                </a>
                            </li>
                        @endcanany
                    </ul>
                </div>
            </li>
        @endcanany

        {{-- Credit --}}
        @hasrole(['School Admin'])
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#credit-menu" aria-expanded="false"
                    aria-controls="credit-menu" data-name="{{ Auth::user()->getRoleNames()[0] }}"
                    data-access="@hasFeatureAccess('Credit Management')">
                    <i class="las la-wallet menu-icon" aria-hidden="true"
                        style="font-size: 25px; margin-left: 2px !important;; margin-right: 3px;"></i>
                    <span class="menu-title">{{ __('Credit') }}</span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="credit-menu">
                    <ul class="nav flex-column sub-menu">
                        <li class="nav-item">
                            <a href="{{ route('credit.view-credit') }}" class="nav-link"
                                data-name="{{ Auth::user()->getRoleNames()[0] }}"
                                data-access="@hasFeatureAccess('Credit Management')">{{ __('View Credit') }} </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ route('credit.manage-credit') }}" class="nav-link"
                                data-name="{{ Auth::user()->getRoleNames()[0] }}"
                                data-access="@hasFeatureAccess('Credit Management')">{{ __('Manage Credit') }} </a>
                        </li>

                    </ul>
                </div>
            </li>
        @endrole

        {{-- Payroll --}}
        @canany(['payroll-create', 'payroll-list', 'payroll-edit', 'payroll-delete', 'payroll-settings-list',
            'payroll-settings-create', 'payroll-settings-edit', 'payroll-settings-delete'])
            <li class="nav-item">
                <a href="#payroll-menu" class="nav-link" data-toggle="collapse"
                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Expense Management')">
                    <i class="fa fa-credit-card-alt menu-icon"></i>
                    <span class="menu-title">{{ __('payroll') }}</span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="payroll-menu">
                    <ul class="nav flex-column sub-menu">
                        @canany(['payroll-create', 'payroll-list', 'payroll-edit', 'payroll-delete'])
                            <li class="nav-item">
                                <a href="{{ route('payroll.index') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}"
                                    data-access="@hasFeatureAccess('Expense Management')">{{ __('manage_payroll') }} </a>
                            </li>

                            <li class="nav-item">
                                <a href="{{ route('customPayroll.index') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}"
                                    data-access="@hasFeatureAccess('Expense Management')">{{ __('custom_payroll') }} </a>
                            </li>

                        @endcanany

                        <!-- @canany(['payroll-settings-list', 'payroll-settings-create', 'payroll-settings-edit',
                            'payroll-settings-delete'])
                            <li class="nav-item">
                                <a href="{{ route('payroll-setting.index') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Expense Management')">
                                    {{ __('payroll_setting') }}
                                </a>
                            </li>
                        @endcanany -->
                    </ul>
                </div>
            </li>
        @endcanany

        {{-- Commission --}}
        @role(['School Admin'])
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#commission-menu" 
                    aria-expanded="false" aria-controls="commission-menu" 
                    data-access="@hasFeatureAccess('Commission Management')">
                    <i class="fa las la-coins menu-icon"
                        style="font-size: 25px; margin-left: 2px !important;; margin-right: 3px;"></i>
                    <span class="menu-title">{{ __('Commission') }}</span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="commission-menu">
                    <ul class="nav flex-column sub-menu">
                        <li class="nav-item">
                            <a href="{{ route('user-groups.index') }}" class="nav-link"
                                data-access="@hasFeatureAccess('Commission Management')"> 
                                {{ __('user_group') }}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ route('managecommission.scan') }}" class="nav-link"
                                data-access="@hasFeatureAccess('Commission Management')"> 
                                {{ __('manage').' '.__('commission_per_session') }}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ route('managecommission.per-month.index') }}" class="nav-link"
                                data-access="@hasFeatureAccess('Commission Management')"> 
                                {{ __('manage').' '.__('commission_per_month') }}
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
        @endrole

        {{-- session-year --}}
        {{-- @can('session-year-create')
            <li class="nav-item">
                <a href="{{ route('session-year.index') }}" class="nav-link">
                    <i class="fa fa-calendar-o menu-icon"></i>
                    <span class="menu-title">{{ __('Session Years') }}</span>
                </a>
            </li>
        @endcan --}}

        {{-- gallery --}}
        @canany(['gallery-create', 'gallery-list', 'gallery-edit', 'gallery-delete'])
            <li class="nav-item">
                <a href="{{ route('gallery.index') }}" class="nav-link"
                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('School Gallery Management')">
                    <i class="fa fa-picture-o menu-icon"></i>
                    <span class="menu-title">{{ __('gallery') }}</span>
                </a>
            </li>
        @endcan

        {{-- Certificate --}}
        @canany(['certificate-create', 'certificate-list', 'certificate-edit', 'certificate-delete', 'student-list',
            'class-teacher', 'id-card-settings'])
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#certificate-menu" aria-expanded="false"
                    aria-controls="certificate-menu" data-access="@hasFeatureAccess('ID Card - Certificate Generation')">
                    <i class="fa fa-trophy menu-icon"></i>
                    <span class="menu-title">{{ __('certificate_id_card') }}</span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="certificate-menu">
                    <ul class="nav flex-column sub-menu">

                        @canany(['certificate-create', 'certificate-list', 'certificate-edit', 'certificate-delete'])
                            <li class="nav-item">
                                <a href="{{ url('certificate-template') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('ID Card - Certificate Generation')">
                                    {{ __('certificate_template') }}
                                </a>
                            </li>
                        @endcanany

                        @canany(['certificate-list'])
                            <li class="nav-item">
                                <a href="{{ url('certificate') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('ID Card - Certificate Generation')">
                                    {{ __('student_certificate') }}
                                </a>
                            </li>
                        @endcanany

                        @canany(['certificate-list'])
                            <li class="nav-item">
                                <a href="{{ url('certificate/staff-certificate') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('ID Card - Certificate Generation')">
                                    {{ __('staff_certificate') }}
                                </a>
                            </li>
                        @endcanany

                        @can('id-card-settings')
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('id-card-settings') }}"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}"
                                    data-access="@hasFeatureAccess('ID Card - Certificate Generation')">{{ __('id_card_settings') }}</a>
                            </li>
                        @endcan

                        @canany(['student-list', 'class-teacher'])
                            <li class="nav-item"><a href="{{ route('students.generate-id-card-index') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}"
                                    data-access="@hasFeatureAccess('ID Card - Certificate Generation')">{{ __('student_id_card') }}</a></li>
                        @endcanany

                        @can('staff-list')
                            <li class="nav-item">
                                <a href="{{ route('staff.id-card') }}" class="nav-link"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}"
                                    data-access="@hasFeatureAccess('ID Card - Certificate Generation')">{{ __('staff_id_card') }}</a>
                            </li>
                        @endcan
                    </ul>
                </div>
            </li>
        @endcanany
        @if (Auth::user()->school_id)
            @canany(['role-list', 'role-create', 'role-edit', 'role-delete', 'staff-list', 'staff-create', 'staff-edit',
                'staff-delete', 'approve-leave'])
                <li class="nav-item">
                    <a class="nav-link" data-toggle="collapse" href="#staff-management" aria-expanded="false"
                        aria-controls="staff-management-menu" data-access="@hasFeatureAccess('Staff Management')">
                        <i class="fa fa-user-secret menu-icon"></i>
                        <span class="menu-title">{{ __('Staff Management') }}</span>
                        <i class="menu-arrow"></i>
                    </a>
                    <div class="collapse" id="staff-management">
                        <ul class="nav flex-column sub-menu">
                            @canany(['role-list', 'role-create', 'role-edit', 'role-delete'])
                                <li class="nav-item">
                                    <a href="{{ route('roles.index') }}" class="nav-link"
                                        data-name="{{ Auth::user()->getRoleNames()[0] }}"
                                        data-access="@hasFeatureAccess('Staff Management')">{{ __('Role & Permission') }}</a>
                                </li>
                            @endcanany
                            @canany(['staff-list', 'staff-create', 'staff-edit', 'staff-delete'])
                                <li class="nav-item">
                                    <a href="{{ route('staff.index') }}" class="nav-link"
                                        data-name="{{ Auth::user()->getRoleNames()[0] }}"
                                        data-access="@hasFeatureAccess('Staff Management')">{{ __('staff') }}</a>
                                </li>
                            @endcanany
                            @canany(['staff-list', 'staff-create', 'staff-edit', 'staff-delete'])
                                <li class="nav-item">
                                    <a href="{{ route('staff.create-bulk-upload') }}" class="nav-link"
                                        data-name="{{ Auth::user()->getRoleNames()[0] }}"
                                        data-access="@hasFeatureAccess('Staff Management')">{{ __('bulk upload') }}</a>
                                </li>
                            @endcanany
                        </ul>
                    </div>
                </li>
            @endcan
            @hasrole(['School Admin'])
                <li class="nav-item">
                    <a href="{{ route('document.index') }}" class="nav-link"
                        data-name="{{ Auth::user()->getRoleNames()[0] }}">
                        <i
                            class="las la-file-alt menu-icon"style="font-size: 25px; margin-left: 2px !important;; margin-right: 3px;"></i>
                        <span class="menu-title">{{ __('Document Upload') }}</span>
                    </a>
                </li>
            @endrole

            {{-- Staff Leave Management --}}
            @canany(['approve-leave'])
                <li class="nav-item">
                    <a class="nav-link" data-toggle="collapse" href="#staff-leave-management" aria-expanded="false"
                        aria-controls="staff-leave-management-menu" data-access="@hasFeatureAccess('Staff Leave Management')">
                        <i class="fa fa-plane menu-icon"></i>
                        <span class="menu-title">{{ __('Staff Leave') }}</span>
                        <i class="menu-arrow"></i>
                    </a>
                    <div class="collapse" id="staff-leave-management">
                        <ul class="nav flex-column sub-menu">

                            @can('approve-leave')
                                <li class="nav-item">
                                    <a href="{{ route('leave.request') }}" class="nav-link"
                                        data-name="{{ Auth::user()->getRoleNames()[0] }}"
                                        data-access="@hasFeatureAccess('Staff Leave Management')">{{ __('staff') }} {{ __('leaves') }}</a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{ url('leave/leave_report') }}" class="nav-link"
                                        data-name="{{ Auth::user()->getRoleNames()[0] }}"
                                        data-access="@hasFeatureAccess('Staff Leave Management')">{{ __('leave_report') }}</a>
                                </li>
                            @endcan
                        </ul>
                    </div>
                </li>
            @endcan
        @else
            @canany(['role-list', 'role-create', 'role-edit', 'role-delete', 'staff-list', 'staff-create', 'staff-edit',
                'staff-delete'])
                <li class="nav-item">
                    <a class="nav-link" data-toggle="collapse" href="#staff-management" aria-expanded="false"
                        aria-controls="staff-management-menu">
                        <i class="fa fa-user-secret menu-icon"></i>
                        <span class="menu-title">{{ __('Staff Management') }}</span>
                        <i class="menu-arrow"></i>
                    </a>
                    <div class="collapse" id="staff-management">
                        <ul class="nav flex-column sub-menu">
                            @canany(['role-list', 'role-create', 'role-edit', 'role-delete'])
                                <li class="nav-item">
                                    <a href="{{ route('roles.index') }}"
                                        class="nav-link">{{ __('Role & Permission') }}</a>
                                </li>
                            @endcanany
                            @canany(['staff-list', 'staff-create', 'staff-edit', 'staff-delete'])
                                <li class="nav-item">
                                    <a href="{{ route('staff.index') }}" class="nav-link">{{ __('staff') }}</a>
                                </li>
                            @endcanany
                        </ul>
                    </div>
                </li>
            @endcan
        @endif

        {{-- Subscription Plans & Addons --}}
        @role('School Admin')
            @if (false)
                <li class="nav-item">
                    <a class="nav-link" data-toggle="collapse" href="#subscription" aria-expanded="false"
                        aria-controls="subscription-menu">
                        <i class="fa fa-puzzle-piece menu-icon"></i>
                        <span class="menu-title">{{ __('subscription') }}</span>
                        <i class="menu-arrow"></i>
                    </a>
                    <div class="collapse" id="subscription">
                        <ul class="nav flex-column sub-menu">
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="{{ route('subscriptions.history') }}">{{ __('subscription') }}</a>
                            </li>
                            @if (false)
                                <li class="nav-item">
                                    <a class="nav-link"
                                        href="{{ route('subscriptions.index') }}">{{ __('plans') }}</a>
                                </li>
                            @endif
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('addons.plan') }}">{{ __('addons') }}</a>
                            </li>
                        </ul>
                    </div>
                </li>
            @endif
            {{-- Support --}}
            @if (false)
                <li class="nav-item">
                    <a href="{{ url('staff/support') }}" class="nav-link">
                        <i class="fa fa-question menu-icon"></i>
                        <span class="menu-title">{{ __('support') }}</span>
                    </a>
                </li>
            @endif

            <li class="nav-item">
                <a href="{{ url('features') }}" class="nav-link">
                    <i class="fa fa-list-ul menu-icon"></i>
                    <span class="menu-title">{{ __('features') }}</span>
                </a>
            </li>

            <li class="nav-item">
                <a href="https://leapseed-sdn-bhd.gitbook.io/schola.one/" class="nav-link" target="_blank">
                    <i class="fa fa-solid fa-book menu-icon"></i>
                    <span class="menu-title">{{ __('admin_manual') }}</span>
                </a>
            </li>
        @endrole

        <!-- @canany(['faqs-create', 'faqs-list', 'faqs-edit', 'faqs-delete'])
            <li class="nav-item">
                <a href="{{ route('faqs.index') }}" class="nav-link">
                    <i class="fa fa-question menu-icon"></i>
                    <span class="menu-title">{{ __('faqs') }}</span>
                </a>
            </li>
        @endcanany -->
        @can('web-settings')
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#web_settings" aria-expanded="false"
                    aria-controls="web_settings-menu">
                    <i class="fa fa-cogs menu-icon"></i>
                    <span class="menu-title">{{ __('web_settings') }}</span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="web_settings">
                    <ul class="nav flex-column sub-menu">
                        <li class="nav-item">
                            <a class="nav-link"
                                href="{{ route('web-settings.index') }}">{{ __('general_settings') }}</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link"
                                href="{{ route('web-settings.feature.sections') }}">{{ __('feature_sections') }}</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link"
                                href="{{ route('video-settings.index') }}">{{ __('video_setting') }}</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link"
                                href="{{ route('picture-settings.index') }}">{{ __('picture_setting') }}</a>
                        </li>

                        @canany(['faqs-create', 'faqs-list', 'faqs-edit', 'faqs-delete'])
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('faqs.index') }}">{{ __('faqs') }}</a>
                            </li>
                        @endcanany
                    </ul>
                </div>
            </li>
        @endcan

        {{-- School web page setttings --}}
        @if (isset($systemSettings['school_website_feature']) && $systemSettings['school_website_feature'] == 1)
            @can('school-web-settings')
                <li class="nav-item">
                    <a class="nav-link" data-toggle="collapse" href="#web_settings" aria-expanded="false"
                        aria-controls="web_settings-menu" data-access="@hasFeatureAccess('Website Management')">
                        <i class="fa fa-cogs menu-icon"></i>
                        <span class="menu-title">{{ __('web_settings') }}</span>
                        <i class="menu-arrow"></i>
                    </a>
                    <div class="collapse" id="web_settings">
                        <ul class="nav flex-column sub-menu">
                            @if (isset($systemSettings['school_website_feature']) && $systemSettings['school_website_feature'] == 1)
                                @can('class-group-create')
                                    <li class="nav-item"><a href="{{ route('class-group.index') }}" class="nav-link">
                                            {{ __('class_group') }} </a></li>
                                @endcan
                            @endif

                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('school.web-settings.index') }}"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}"
                                    data-access="@hasFeatureAccess('Website Management')">{{ __('content') }}</a>
                            </li>

                            @canany(['faqs-create', 'faqs-list', 'faqs-edit', 'faqs-delete'])
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ route('faqs.index') }}"
                                        data-name="{{ Auth::user()->getRoleNames()[0] }}"
                                        data-access="@hasFeatureAccess('Website Management')">{{ __('faqs') }}</a>
                                </li>
                            @endcanany
                        </ul>
                    </div>
                </li>
            @endcan
        @endif
        {{-- settings --}}
        @canany(['app-settings', 'language-list', 'school-setting-manage', 'system-setting-manage',
            'fcm-setting-manage', 'email-setting-create', 'privacy-policy', 'contact-us', 'about-us', 'guidance-create',
            'guidance-list', 'guidance-edit', 'guidance-delete', 'email-template'])
            <li class="nav-item">
                <a class="nav-link" data-toggle="collapse" href="#settings-menu" aria-expanded="false"
                    aria-controls="settings-menu">
                    <i class="fa fa-cog menu-icon"></i>
                    <span class="menu-title">{{ __('system_settings') }}</span>
                    <i class="menu-arrow"></i>
                </a>
                <div class="collapse" id="settings-menu">
                    <ul class="nav flex-column sub-menu">
                        @can('app-settings')
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('system-settings.app') }}">{{ __('app_settings') }}</a>
                            </li>
                        @endcan
                        @can('school-setting-manage')
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="{{ route('school-settings.index') }}">{{ __('general_settings') }}</a>
                            </li>

                            {{-- session-year.index --}}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('session-year.index') }}">{{ __('session_year') }}</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('leave-master.index') }}"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}"
                                    data-access="@hasFeatureAccess('Staff Leave Management')">{{ __('leaves') }} {{ __('settings') }}</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('leave-master.category-index') }}"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}"
                                    data-access="@hasFeatureAccess('Staff Leave Management')">{{ __('leaves') }} {{ __('category') }}
                                    {{ __('settings') }}</a>
                            </li>
                        @endcan

                        @can('student-id-card-settings')
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('student.id-card-settings') }}"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}"
                                    data-access="@hasFeatureAccess('ID Card - Certificate Generation')">{{ __('student_id_card') }}</a>
                            </li>
                        @endcan

                        @can('system-setting-manage')
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="{{ route('system-settings.index') }}">{{ __('general_settings') }}</a>
                            </li>
                        @endcan

                        @can('subscription-settings')
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="{{ route('system-settings.subscription-settings') }}">{{ __('subscription_settings') }}</a>
                            </li>
                        @endcan

                         @hasrole('Superadmin')
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="{{ route('system-settings.rfid-whitelist') }}">{{ __('rfid_whitelist') }}</a>
                            </li>
                        @endrole

                        {{-- @can('front-site-setting')
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('system-settings.front-site-settings') }}">{{ __('front_site_settings') }}</a>
                            </li>
                        @endcan --}}
                        @canany(['guidance-create', 'guidance-list', 'guidance-edit', 'guidance-delete'])
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('guidances.index') }}">{{ __('guidance') }}</a>
                            </li>
                        @endcanany

                        @can('language-list')
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url('language') }}">
                                    {{ __('language_settings') }}</a>
                            </li>
                        @endcan
                        @can('fcm-setting-manage')
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('system-settings.fcm') }}">
                                    {{ __('notification_settings') }}</a>
                            </li>
                        @endcan

                        {{-- @can('fees-config')
                            <li class="nav-item">
                                <a href="{{ route('fees.config.index') }}" class="nav-link" data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Fees Management')">
                                    {{ __('Fees Settings') }}</a>
                            </li>
                        @endcan --}}

                        @can('school-setting-manage')
                            <li class="nav-item">
                                <a href="{{ route('school-settings.online-exam.index') }}" class="nav-link text-wrap"
                                    data-name="{{ Auth::user()->getRoleNames()[0] }}" data-access="@hasFeatureAccess('Exam Management')">
                                    {{ __('online_exam_terms_condition') }}
                                </a>
                            </li>
                        @endcan

                        @can('email-setting-create')
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="{{ route('system-settings.email.index') }}">{{ __('email_configuration') }}</a>
                            </li>
                        @endcan

                        {{-- Super admin panel --}}
                        @can('email-setting-create')
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="{{ route('system-settings.email.template') }}">{{ __('email_template') }}</a>
                            </li>
                        @endcan

                        {{-- School admin panel --}}
                        @can('email-template')
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="{{ route('school-settings.email.template') }}">{{ __('email_template') }}</a>
                            </li>
                        @endcan

                         @hasrole('School Admin')
                            <li class="nav-item">
                                <a class="nav-link" data-access="@hasFeatureAccess('E-Invoicing')"
                                    href="{{ route('school-settings.e-invoice.index') }}">{{ __('eInvoice_settings') }}</a>
                            </li>
                        @endrole

                        {{-- Payment Configuration Menu For Superadmin --}}
                        @hasanyrole(['Super Admin', 'School Admin'])
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="{{ route('system-settings.payment.index') }}">{{ __('Payment Settings') }}</a>
                            </li>
                        @endrole

                        @can('school-setting-manage')
                        <li class="nav-item">
                            <a class="nav-link" data-access="@hasFeatureAccess('Website Management')" href="{{ route('school-settings.third-party') }}">{{ __('Third-Party APIs') }}</a>
                        </li>
                        @endcan

                        @can('system-setting-manage')
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('system-settings.third-party') }}">{{ __('Third-Party APIs') }}</a>
                        </li>
                        @endcan

                        @hasrole('School Admin')
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="{{ route('school-settings.terms-condition') }}">{{ __('terms_condition') }}</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link"
                                    href="{{ route('school-settings.privacy-policy') }}">{{ __('privacy_policy') }}</a>
                            </li>
                        @endrole

                        @can('privacy-policy')
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="{{ route('system-settings.privacy-policy') }}">{{ __('privacy_policy') }}</a>
                            </li>
                        @endcan
                        @can('contact-us')
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('system-settings.contact-us') }}">
                                    {{ __('contact_us') }}</a>
                            </li>
                        @endcan
                        @can('about-us')
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('system-settings.about-us') }}"> {{ __('about_us') }}
                                </a>
                            </li>
                        @endcan
                        @can('terms-condition')
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="{{ route('system-settings.terms-condition') }}">{{ __('terms_condition') }}</a>
                            </li>
                        @endcan

                        @can('school-terms-condition')
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="{{ route('system-settings.school-terms-condition') }}">{{ __('school_terms_condition') }}</a>
                            </li>
                        @endcan
                    </ul>
                </div>
            </li>
        @endcanany

        @if (Auth::user()->hasRole('Super Admin'))
            <li class="nav-item">
                <a class="nav-link" href="{{ route('system-update.index') }}">
                    <i class="fa fa-cloud-download menu-icon"></i>
                    <span class="menu-title">{{ __('system_update') }}</span>
                </a>
            </li>
        @endif

    </ul>
</nav>
