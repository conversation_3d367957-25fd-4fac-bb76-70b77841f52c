<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('epf_contributions', function (Blueprint $table) {
            $table->id();
            $table->decimal('min_range', 10, 2);
            $table->decimal('max_range', 10, 2)->nullable();
            $table->decimal('socso_employee', 8, 2)->default(0);
            $table->decimal('socso_employer', 8, 2)->default(0);
            $table->decimal('eis_employee', 8, 2)->default(0);
            $table->decimal('eis_employer', 8, 2)->default(0);
            $table->timestamps();
        });

        // Insert sample EPF contribution data
        $contributions = [
            ['min_range' => 0, 'max_range' => 30, 'socso_employee' => 0, 'socso_employer' => 0, 'eis_employee' => 0, 'eis_employer' => 0],
            ['min_range' => 30.01, 'max_range' => 50, 'socso_employee' => 0.30, 'socso_employer' => 0.70, 'eis_employee' => 0.05, 'eis_employer' => 0.10],
            ['min_range' => 50.01, 'max_range' => 70, 'socso_employee' => 0.50, 'socso_employer' => 1.20, 'eis_employee' => 0.07, 'eis_employer' => 0.14],
            ['min_range' => 70.01, 'max_range' => 100, 'socso_employee' => 0.70, 'socso_employer' => 1.70, 'eis_employee' => 0.10, 'eis_employer' => 0.20],
            ['min_range' => 100.01, 'max_range' => 140, 'socso_employee' => 1.00, 'socso_employer' => 2.40, 'eis_employee' => 0.14, 'eis_employer' => 0.28],
            ['min_range' => 140.01, 'max_range' => 200, 'socso_employee' => 1.40, 'socso_employer' => 3.40, 'eis_employee' => 0.20, 'eis_employer' => 0.40],
            ['min_range' => 200.01, 'max_range' => 300, 'socso_employee' => 2.00, 'socso_employer' => 4.80, 'eis_employee' => 0.30, 'eis_employer' => 0.60],
            ['min_range' => 300.01, 'max_range' => 400, 'socso_employee' => 3.00, 'socso_employer' => 7.20, 'eis_employee' => 0.40, 'eis_employer' => 0.80],
            ['min_range' => 400.01, 'max_range' => 500, 'socso_employee' => 4.00, 'socso_employer' => 9.60, 'eis_employee' => 0.50, 'eis_employer' => 1.00],
            ['min_range' => 500.01, 'max_range' => 600, 'socso_employee' => 5.00, 'socso_employer' => 12.00, 'eis_employee' => 0.60, 'eis_employer' => 1.20],
            ['min_range' => 600.01, 'max_range' => 700, 'socso_employee' => 6.00, 'socso_employer' => 14.40, 'eis_employee' => 0.70, 'eis_employer' => 1.40],
            ['min_range' => 700.01, 'max_range' => 800, 'socso_employee' => 7.00, 'socso_employer' => 16.80, 'eis_employee' => 0.80, 'eis_employer' => 1.60],
            ['min_range' => 800.01, 'max_range' => 900, 'socso_employee' => 8.00, 'socso_employer' => 19.20, 'eis_employee' => 0.90, 'eis_employer' => 1.80],
            ['min_range' => 900.01, 'max_range' => 1000, 'socso_employee' => 9.00, 'socso_employer' => 21.60, 'eis_employee' => 1.00, 'eis_employer' => 2.00],
            ['min_range' => 1000.01, 'max_range' => 1100, 'socso_employee' => 10.00, 'socso_employer' => 24.00, 'eis_employee' => 1.10, 'eis_employer' => 2.20],
            ['min_range' => 1100.01, 'max_range' => 1200, 'socso_employee' => 11.00, 'socso_employer' => 26.40, 'eis_employee' => 1.20, 'eis_employer' => 2.40],
            ['min_range' => 1200.01, 'max_range' => 1300, 'socso_employee' => 12.00, 'socso_employer' => 28.80, 'eis_employee' => 1.30, 'eis_employer' => 2.60],
            ['min_range' => 1300.01, 'max_range' => 1400, 'socso_employee' => 13.00, 'socso_employer' => 31.20, 'eis_employee' => 1.40, 'eis_employer' => 2.80],
            ['min_range' => 1400.01, 'max_range' => 1500, 'socso_employee' => 14.00, 'socso_employer' => 33.60, 'eis_employee' => 1.50, 'eis_employer' => 3.00],
            ['min_range' => 1500.01, 'max_range' => 1600, 'socso_employee' => 15.00, 'socso_employer' => 36.00, 'eis_employee' => 1.60, 'eis_employer' => 3.20],
            ['min_range' => 1600.01, 'max_range' => 1700, 'socso_employee' => 16.00, 'socso_employer' => 38.40, 'eis_employee' => 1.70, 'eis_employer' => 3.40],
            ['min_range' => 1700.01, 'max_range' => 1800, 'socso_employee' => 17.00, 'socso_employer' => 40.80, 'eis_employee' => 1.80, 'eis_employer' => 3.60],
            ['min_range' => 1800.01, 'max_range' => 1900, 'socso_employee' => 18.00, 'socso_employer' => 43.20, 'eis_employee' => 1.90, 'eis_employer' => 3.80],
            ['min_range' => 1900.01, 'max_range' => 2000, 'socso_employee' => 19.00, 'socso_employer' => 45.60, 'eis_employee' => 2.00, 'eis_employer' => 4.00],
            ['min_range' => 2000.01, 'max_range' => 2100, 'socso_employee' => 20.00, 'socso_employer' => 48.00, 'eis_employee' => 2.10, 'eis_employer' => 4.20],
            ['min_range' => 2100.01, 'max_range' => 2200, 'socso_employee' => 21.00, 'socso_employer' => 50.40, 'eis_employee' => 2.20, 'eis_employer' => 4.40],
            ['min_range' => 2200.01, 'max_range' => 2300, 'socso_employee' => 22.00, 'socso_employer' => 52.80, 'eis_employee' => 2.30, 'eis_employer' => 4.60],
            ['min_range' => 2300.01, 'max_range' => 2400, 'socso_employee' => 23.00, 'socso_employer' => 55.20, 'eis_employee' => 2.40, 'eis_employer' => 4.80],
            ['min_range' => 2400.01, 'max_range' => 2500, 'socso_employee' => 24.00, 'socso_employer' => 57.60, 'eis_employee' => 2.50, 'eis_employer' => 5.00],
            ['min_range' => 2500.01, 'max_range' => 2600, 'socso_employee' => 25.00, 'socso_employer' => 60.00, 'eis_employee' => 2.60, 'eis_employer' => 5.20],
            ['min_range' => 2600.01, 'max_range' => 2700, 'socso_employee' => 26.00, 'socso_employer' => 62.40, 'eis_employee' => 2.70, 'eis_employer' => 5.40],
            ['min_range' => 2700.01, 'max_range' => 2800, 'socso_employee' => 27.00, 'socso_employer' => 64.80, 'eis_employee' => 2.80, 'eis_employer' => 5.60],
            ['min_range' => 2800.01, 'max_range' => 2900, 'socso_employee' => 28.00, 'socso_employer' => 67.20, 'eis_employee' => 2.90, 'eis_employer' => 5.80],
            ['min_range' => 2900.01, 'max_range' => 3000, 'socso_employee' => 29.00, 'socso_employer' => 69.60, 'eis_employee' => 3.00, 'eis_employer' => 6.00],
            ['min_range' => 3000.01, 'max_range' => 3100, 'socso_employee' => 30.00, 'socso_employer' => 72.00, 'eis_employee' => 3.10, 'eis_employer' => 6.20],
            ['min_range' => 3100.01, 'max_range' => 3200, 'socso_employee' => 31.00, 'socso_employer' => 74.40, 'eis_employee' => 3.20, 'eis_employer' => 6.40],
            ['min_range' => 3200.01, 'max_range' => 3300, 'socso_employee' => 32.00, 'socso_employer' => 76.80, 'eis_employee' => 3.30, 'eis_employer' => 6.60],
            ['min_range' => 3300.01, 'max_range' => 3400, 'socso_employee' => 33.00, 'socso_employer' => 79.20, 'eis_employee' => 3.40, 'eis_employer' => 6.80],
            ['min_range' => 3400.01, 'max_range' => 3500, 'socso_employee' => 34.00, 'socso_employer' => 81.60, 'eis_employee' => 3.50, 'eis_employer' => 7.00],
            ['min_range' => 3500.01, 'max_range' => 3600, 'socso_employee' => 35.00, 'socso_employer' => 84.00, 'eis_employee' => 3.60, 'eis_employer' => 7.20],
            ['min_range' => 3600.01, 'max_range' => 3700, 'socso_employee' => 36.00, 'socso_employer' => 86.40, 'eis_employee' => 3.70, 'eis_employer' => 7.40],
            ['min_range' => 3700.01, 'max_range' => 3800, 'socso_employee' => 37.00, 'socso_employer' => 88.80, 'eis_employee' => 3.80, 'eis_employer' => 7.60],
            ['min_range' => 3800.01, 'max_range' => 3900, 'socso_employee' => 38.00, 'socso_employer' => 91.20, 'eis_employee' => 3.90, 'eis_employer' => 7.80],
            ['min_range' => 3900.01, 'max_range' => 4000, 'socso_employee' => 39.00, 'socso_employer' => 93.60, 'eis_employee' => 4.00, 'eis_employer' => 8.00],
            ['min_range' => 4000.01, 'max_range' => null, 'socso_employee' => 39.25, 'socso_employer' => 94.05, 'eis_employee' => 4.00, 'eis_employer' => 8.00],
        ];

        DB::table('epf_contributions')->insert($contributions);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('epf_contributions');
    }
};
